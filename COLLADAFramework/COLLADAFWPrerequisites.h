/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

    This file is part of COLLADAFramework.

    Licensed under the MIT Open Source License, 
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADAFW_PREREQUISITES_H__
#define __COLLADAFW_PREREQUISITES_H__

#include <string>

namespace COLLADAFW
{
    typedef std::string String;
}

#define FW_NEW new 
#define FW_DELETE delete 


#endif //__COLLADAFW_PREREQUISITES_H__
