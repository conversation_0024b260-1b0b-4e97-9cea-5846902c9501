/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

    This file is part of COLLADAFramework.

    Licensed under the MIT Open Source License, 
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADAFW_H__
#define __COLLADAFW_H__


#include "COLLADAFWAnimatable.h"
#include "COLLADAFWAnimatableFloat.h"
#include "COLLADAFWAnimation.h"
#include "COLLADAFWAnimationClip.h"
#include "COLLADAFWAnimationCurve.h"
#include "COLLADAFWAnimationList.h"
#include "COLLADAFWAnnotate.h"
#include "COLLADAFWArray.h"
#include "COLLADAFWArrayPrimitiveType.h"
#include "COLLADAFWCamera.h"
#include "COLLADAFWColor.h"
#include "COLLADAFWColorOrTexture.h"
#include "COLLADAFWConstants.h"
#include "COLLADAFWController.h"
#include "COLLADAFWEdge.h"
#include "COLLADAFWEffect.h"
#include "COLLADAFWEffectCommon.h"
#include "COLLADAFWException.h"
#include "COLLADAFWFileInfo.h"
#include "COLLADAFWFloatOrDoubleArray.h"
#include "COLLADAFWFloatOrParam.h"
#include "COLLADAFWFormula.h"
#include "COLLADAFWFormulaNewParam.h"
#include "COLLADAFWFormulas.h"
#include "COLLADAFWGeometry.h"
#include "COLLADAFWILoader.h"
#include "COLLADAFWIWriter.h"
#include "COLLADAFWImage.h"
#include "COLLADAFWImageSource.h"
#include "COLLADAFWIndexList.h"
#include "COLLADAFWInstanceCamera.h"
#include "COLLADAFWInstanceController.h"
#include "COLLADAFWInstanceGeometry.h"
#include "COLLADAFWInstanceLight.h"
#include "COLLADAFWInstanceNode.h"
#include "COLLADAFWInstanceSceneGraph.h"
#include "COLLADAFWInstanceVisualScene.h"
#include "COLLADAFWJoint.h"
#include "COLLADAFWJointPrimitive.h"
#include "COLLADAFWKinematicsController.h"
#include "COLLADAFWKinematicsModel.h"
#include "COLLADAFWKinematicsScene.h"
#include "COLLADAFWLibraryNodes.h"
#include "COLLADAFWLight.h"
#include "COLLADAFWLinestrips.h"
#include "COLLADAFWLoaderUtils.h"
#include "COLLADAFWLookat.h"
#include "COLLADAFWMaterial.h"
#include "COLLADAFWMatrix.h"
#include "COLLADAFWMesh.h"
#include "COLLADAFWMeshPrimitive.h"
#include "COLLADAFWMeshPrimitiveWithFaceVertexCount.h"
#include "COLLADAFWMeshVertexData.h"
#include "COLLADAFWMorphController.h"
#include "COLLADAFWMotionProfile.h"
#include "COLLADAFWNode.h"
#include "COLLADAFWObject.h"
#include "COLLADAFWParam.h"
#include "COLLADAFWPointerArray.h"
#include "COLLADAFWPolygons.h"
#include "COLLADAFWRoot.h"
#include "COLLADAFWRotate.h"
#include "COLLADAFWSampler.h"
#include "COLLADAFWScale.h"
#include "COLLADAFWScene.h"
#include "COLLADAFWSemantic.h"
#include "COLLADAFWSetParam.h"
#include "COLLADAFWShear.h"
#include "COLLADAFWSkew.h"
#include "COLLADAFWSkinController.h"
#include "COLLADAFWSkinControllerData.h"
#include "COLLADAFWSpline.h"
#include "COLLADAFWTargetableValue.h"
#include "COLLADAFWTechnique.h"
#include "COLLADAFWTexture.h"
#include "COLLADAFWTransformation.h"
#include "COLLADAFWTranslate.h"
#include "COLLADAFWTriangles.h"
#include "COLLADAFWTrifans.h"
#include "COLLADAFWTristrips.h"
#include "COLLADAFWTypes.h"
#include "COLLADAFWUniqueId.h"
#include "COLLADAFWValidate.h"
#include "COLLADAFWValueType.h"
#include "COLLADAFWVisualScene.h"


#endif // __COLLADAFW_H__
