/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

	This file is part of COLLADASaxFrameworkLoader.

    Licensed under the MIT Open Source License,
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADASAXFWL_STABLE_HEADERS_H__
#define __COLLADASAXFWL_STABLE_HEADERS_H__

//STL
#include <vector>
#include <sstream>
#include <algorithm>
#include <math.h>
#include <string>
#include <iostream>
#include <map>
#include <list>
#include <set>
#include <cassert>

// Own attributes
#include "COLLADASaxFWLXmlTypes.h"

// auto generated
#include "COLLADASaxFWLColladaParserAutoGen14.h"
#include "COLLADASaxFWLColladaParserAutoGen14Attributes.h"
#include "COLLADASaxFWLColladaParserAutoGen14Enums.h"
#include "COLLADASaxFWLColladaParserAutoGen14FunctionMapFactory.h"
#include "COLLADASaxFWLColladaParserAutoGen14Private.h"
#include "COLLADASaxFWLColladaParserAutoGen14ValidationData.h"

#include "COLLADASaxFWLColladaParserAutoGen15.h"
#include "COLLADASaxFWLColladaParserAutoGen15Attributes.h"
#include "COLLADASaxFWLColladaParserAutoGen15Enums.h"
#include "COLLADASaxFWLColladaParserAutoGen15FunctionMapFactory.h"
#include "COLLADASaxFWLColladaParserAutoGen15Private.h"
#include "COLLADASaxFWLColladaParserAutoGen15ValidationData.h"

// GeneratedSaxParser
#include "GeneratedSaxParser.h"

// The frame work
#include "COLLADAFW.h"

// base utils headers
#include "COLLADABU.h"


#endif //__COLLADASAXFWL_STABLE_HEADERS_H__
