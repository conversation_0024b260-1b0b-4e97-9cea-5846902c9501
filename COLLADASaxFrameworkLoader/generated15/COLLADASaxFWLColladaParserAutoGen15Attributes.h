/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

    This file is part of COLLADASaxFrameworkLoader.

    Licensed under the MIT Open Source License,
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/



#ifndef __COLLADASAXFWL15_COLLADAPARSERAUTOGEN15ATTRIBUTES_H__
#define __COLLADASAXFWL15_COLLADAPARSERAUTOGEN15ATTRIBUTES_H__


#include "COLLADASaxFWLColladaParserAutoGen15Enums.h"


namespace COLLADASaxFWL15
{


extern const char* NAME_NAMESPACE_COLLADA;
const StringHash HASH_NAMESPACE_COLLADA = 234671633;
extern const char* NAME_NAMESPACE_http___www_w3_org_2001_XMLSchema;
const StringHash HASH_NAMESPACE_http___www_w3_org_2001_XMLSchema = 9996177;
extern const char* NAME_NAMESPACE_http___www_w3_org_XML_1998_namespace;
const StringHash HASH_NAMESPACE_http___www_w3_org_XML_1998_namespace = 153133749;
extern const char* NAME_NAMESPACE_mathml;
const StringHash HASH_NAMESPACE_mathml = 169589116;
extern const char* NAME_NAMESPACE_http___www_w3_org_1999_xlink;
const StringHash HASH_NAMESPACE_http___www_w3_org_1999_xlink = 156218043;


extern const char* NAME_ATTRIBUTE_ALTIMG;
extern const char* NAME_ATTRIBUTE_ALTTEXT;
extern const char* NAME_ATTRIBUTE_ARRAY_INDEX;
extern const char* NAME_ATTRIBUTE_AUTO_GENERATE;
extern const char* NAME_ATTRIBUTE_AXIS;
extern const char* NAME_ATTRIBUTE_BASE;
extern const char* NAME_ATTRIBUTE_BASELINE;
extern const char* NAME_ATTRIBUTE_BODY;
extern const char* NAME_ATTRIBUTE_CAMERA_NODE;
extern const char* NAME_ATTRIBUTE_CHANNELS;
extern const char* NAME_ATTRIBUTE_CLASS;
extern const char* NAME_ATTRIBUTE_CLOSED;
extern const char* NAME_ATTRIBUTE_CLOSED_U;
extern const char* NAME_ATTRIBUTE_CLOSED_V;
extern const char* NAME_ATTRIBUTE_CLOSURE;
extern const char* NAME_ATTRIBUTE_CONSTRAINT;
extern const char* NAME_ATTRIBUTE_CONVEX_HULL_OF;
extern const char* NAME_ATTRIBUTE_COUNT;
extern const char* NAME_ATTRIBUTE_DEFINITIONURL;
extern const char* NAME_ATTRIBUTE_DEGREE;
extern const char* NAME_ATTRIBUTE_DEGREE_U;
extern const char* NAME_ATTRIBUTE_DEGREE_V;
extern const char* NAME_ATTRIBUTE_DEPTH;
extern const char* NAME_ATTRIBUTE_DIGITS;
extern const char* NAME_ATTRIBUTE_DISPLAY;
extern const char* NAME_ATTRIBUTE_ENABLE;
extern const char* NAME_ATTRIBUTE_ENCODING;
extern const char* NAME_ATTRIBUTE_END;
extern const char* NAME_ATTRIBUTE_ENTRY;
extern const char* NAME_ATTRIBUTE_FACE;
extern const char* NAME_ATTRIBUTE_FORMAT;
extern const char* NAME_ATTRIBUTE_HEIGHT;
extern const char* NAME_ATTRIBUTE_HREF;
extern const char* NAME_ATTRIBUTE_ID;
extern const char* NAME_ATTRIBUTE_INDEX;
extern const char* NAME_ATTRIBUTE_INPUT_SEMANTIC;
extern const char* NAME_ATTRIBUTE_INPUT_SET;
extern const char* NAME_ATTRIBUTE_JOINT;
extern const char* NAME_ATTRIBUTE_LANGUAGE;
extern const char* NAME_ATTRIBUTE_LAYER;
extern const char* NAME_ATTRIBUTE_LENGTH;
extern const char* NAME_ATTRIBUTE_LEVELS;
extern const char* NAME_ATTRIBUTE_LINK;
extern const char* NAME_ATTRIBUTE_MACROS;
extern const char* NAME_ATTRIBUTE_MAGNITUDE;
extern const char* NAME_ATTRIBUTE_MATERIAL;
extern const char* NAME_ATTRIBUTE_MAXINCLUSIVE;
extern const char* NAME_ATTRIBUTE_METER;
extern const char* NAME_ATTRIBUTE_METHOD;
extern const char* NAME_ATTRIBUTE_MININCLUSIVE;
extern const char* NAME_ATTRIBUTE_MIP;
extern const char* NAME_ATTRIBUTE_MIPS_GENERATE;
extern const char* NAME_ATTRIBUTE_MIP_INDEX;
extern const char* NAME_ATTRIBUTE_MODE;
extern const char* NAME_ATTRIBUTE_NAME;
extern const char* NAME_ATTRIBUTE_NARGS;
extern const char* NAME_ATTRIBUTE_NODE;
extern const char* NAME_ATTRIBUTE_OCCURRENCE;
extern const char* NAME_ATTRIBUTE_OFFSET;
extern const char* NAME_ATTRIBUTE_OPAQUE;
extern const char* NAME_ATTRIBUTE_OPERAND;
extern const char* NAME_ATTRIBUTE_OPERATOR;
extern const char* NAME_ATTRIBUTE_OPTIONS;
extern const char* NAME_ATTRIBUTE_ORDER;
extern const char* NAME_ATTRIBUTE_OVERFLOW;
extern const char* NAME_ATTRIBUTE_PARAM;
extern const char* NAME_ATTRIBUTE_PARENT;
extern const char* NAME_ATTRIBUTE_PASS;
extern const char* NAME_ATTRIBUTE_PLATFORM;
extern const char* NAME_ATTRIBUTE_PLATFORMS;
extern const char* NAME_ATTRIBUTE_POST_BEHAVIOR;
extern const char* NAME_ATTRIBUTE_PRECISION;
extern const char* NAME_ATTRIBUTE_PRE_BEHAVIOR;
extern const char* NAME_ATTRIBUTE_PROFILE;
extern const char* NAME_ATTRIBUTE_PROXY;
extern const char* NAME_ATTRIBUTE_RANGE;
extern const char* NAME_ATTRIBUTE_REF;
extern const char* NAME_ATTRIBUTE_RESIZABLE;
extern const char* NAME_ATTRIBUTE_RIGID_BODY;
extern const char* NAME_ATTRIBUTE_SAMPLER;
extern const char* NAME_ATTRIBUTE_SCALE;
extern const char* NAME_ATTRIBUTE_SCOPE;
extern const char* NAME_ATTRIBUTE_SEMANTIC;
extern const char* NAME_ATTRIBUTE_SET;
extern const char* NAME_ATTRIBUTE_SHARE;
extern const char* NAME_ATTRIBUTE_SID;
extern const char* NAME_ATTRIBUTE_SLICE;
extern const char* NAME_ATTRIBUTE_SOURCE;
extern const char* NAME_ATTRIBUTE_SPACE;
extern const char* NAME_ATTRIBUTE_STAGE;
extern const char* NAME_ATTRIBUTE_START;
extern const char* NAME_ATTRIBUTE_STRIDE;
extern const char* NAME_ATTRIBUTE_STYLE;
extern const char* NAME_ATTRIBUTE_SYMBOL;
extern const char* NAME_ATTRIBUTE_TARGET;
extern const char* NAME_ATTRIBUTE_TEXCOORD;
extern const char* NAME_ATTRIBUTE_TEXTURE;
extern const char* NAME_ATTRIBUTE_TYPE;
extern const char* NAME_ATTRIBUTE_TYPENAME;
extern const char* NAME_ATTRIBUTE_URL;
extern const char* NAME_ATTRIBUTE_VALUE;
extern const char* NAME_ATTRIBUTE_VERSION;
extern const char* NAME_ATTRIBUTE_WIDTH;
extern const char* NAME_ATTRIBUTE_XMLNS;
extern const char* NAME_ATTRIBUTE_XREF;
extern const char* NAME_ELEMENT_ABS;
extern const char* NAME_ELEMENT_ACCELERATION;
extern const char* NAME_ELEMENT_ACCELERATION____COMMON_FLOAT2_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_ACCELERATION____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_ACCESSOR;
extern const char* NAME_ELEMENT_ACTIVE;
extern const char* NAME_ELEMENT_ALPHA;
extern const char* NAME_ELEMENT_ALPHA_FUNC;
extern const char* NAME_ELEMENT_ALPHA_TEST_ENABLE;
extern const char* NAME_ELEMENT_ALPHA____GLES_TEXCOMBINER_COMMAND_ALPHA_TYPE;
extern const char* NAME_ELEMENT_ALTITUDE;
extern const char* NAME_ELEMENT_AMBIENT;
extern const char* NAME_ELEMENT_AMBIENT____FX_COMMON_COLOR_OR_TEXTURE_TYPE;
extern const char* NAME_ELEMENT_AND;
extern const char* NAME_ELEMENT_ANGLE;
extern const char* NAME_ELEMENT_ANGULAR;
extern const char* NAME_ELEMENT_ANGULAR_VELOCITY;
extern const char* NAME_ELEMENT_ANIMATION;
extern const char* NAME_ELEMENT_ANIMATION_CLIP;
extern const char* NAME_ELEMENT_ANNOTATE;
extern const char* NAME_ELEMENT_ANNOTATION;
extern const char* NAME_ELEMENT_ANNOTATION_XML;
extern const char* NAME_ELEMENT_APPLY;
extern const char* NAME_ELEMENT_APPROX;
extern const char* NAME_ELEMENT_ARCCOS;
extern const char* NAME_ELEMENT_ARCCOSH;
extern const char* NAME_ELEMENT_ARCCOT;
extern const char* NAME_ELEMENT_ARCCOTH;
extern const char* NAME_ELEMENT_ARCCSC;
extern const char* NAME_ELEMENT_ARCCSCH;
extern const char* NAME_ELEMENT_ARCSEC;
extern const char* NAME_ELEMENT_ARCSECH;
extern const char* NAME_ELEMENT_ARCSIN;
extern const char* NAME_ELEMENT_ARCSINH;
extern const char* NAME_ELEMENT_ARCTAN;
extern const char* NAME_ELEMENT_ARCTANH;
extern const char* NAME_ELEMENT_ARG;
extern const char* NAME_ELEMENT_ARGUMENT;
extern const char* NAME_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENT_ALPHA_TYPE;
extern const char* NAME_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENT_RGB_TYPE;
extern const char* NAME_ELEMENT_ARRAY;
extern const char* NAME_ELEMENT_ARRAY____CG_ARRAY_TYPE;
extern const char* NAME_ELEMENT_ARRAY____GLSL_ARRAY_TYPE;
extern const char* NAME_ELEMENT_ARTICULATED_SYSTEM;
extern const char* NAME_ELEMENT_ASPECT_RATIO;
extern const char* NAME_ELEMENT_ASSET;
extern const char* NAME_ELEMENT_ATTACHMENT;
extern const char* NAME_ELEMENT_ATTACHMENT_END;
extern const char* NAME_ELEMENT_ATTACHMENT_FULL;
extern const char* NAME_ELEMENT_ATTACHMENT_START;
extern const char* NAME_ELEMENT_AUTHOR;
extern const char* NAME_ELEMENT_AUTHORING_TOOL;
extern const char* NAME_ELEMENT_AUTHOR_EMAIL;
extern const char* NAME_ELEMENT_AUTHOR_WEBSITE;
extern const char* NAME_ELEMENT_AXIS;
extern const char* NAME_ELEMENT_AXIS_INFO;
extern const char* NAME_ELEMENT_AXIS_INFO____KINEMATICS_AXIS_INFO_TYPE;
extern const char* NAME_ELEMENT_AXIS_INFO____MOTION_AXIS_INFO_TYPE;
extern const char* NAME_ELEMENT_AXIS____AXIS_TYPE;
extern const char* NAME_ELEMENT_AXIS____COMMON_SIDREF_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_AXIS____FLOAT3_TYPE;
extern const char* NAME_ELEMENT_BACK;
extern const char* NAME_ELEMENT_BINARY;
extern const char* NAME_ELEMENT_BINARY__HEX;
extern const char* NAME_ELEMENT_BIND;
extern const char* NAME_ELEMENT_BIND_ATTRIBUTE;
extern const char* NAME_ELEMENT_BIND_JOINT_AXIS;
extern const char* NAME_ELEMENT_BIND_KINEMATICS_MODEL;
extern const char* NAME_ELEMENT_BIND_MATERIAL;
extern const char* NAME_ELEMENT_BIND_MATERIAL_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_BIND_SHAPE_MATRIX;
extern const char* NAME_ELEMENT_BIND_UNIFORM;
extern const char* NAME_ELEMENT_BIND_VERTEX_INPUT;
extern const char* NAME_ELEMENT_BIND____KINEMATICS_BIND_TYPE;
extern const char* NAME_ELEMENT_BLEND_COLOR;
extern const char* NAME_ELEMENT_BLEND_ENABLE;
extern const char* NAME_ELEMENT_BLEND_EQUATION;
extern const char* NAME_ELEMENT_BLEND_EQUATION_SEPARATE;
extern const char* NAME_ELEMENT_BLEND_FUNC;
extern const char* NAME_ELEMENT_BLEND_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_BLINN;
extern const char* NAME_ELEMENT_BOOL;
extern const char* NAME_ELEMENT_BOOL2;
extern const char* NAME_ELEMENT_BOOL2X1;
extern const char* NAME_ELEMENT_BOOL2X2;
extern const char* NAME_ELEMENT_BOOL2X3;
extern const char* NAME_ELEMENT_BOOL2X4;
extern const char* NAME_ELEMENT_BOOL3;
extern const char* NAME_ELEMENT_BOOL3X1;
extern const char* NAME_ELEMENT_BOOL3X2;
extern const char* NAME_ELEMENT_BOOL3X3;
extern const char* NAME_ELEMENT_BOOL3X4;
extern const char* NAME_ELEMENT_BOOL4;
extern const char* NAME_ELEMENT_BOOL4X1;
extern const char* NAME_ELEMENT_BOOL4X2;
extern const char* NAME_ELEMENT_BOOL4X3;
extern const char* NAME_ELEMENT_BOOL4X4;
extern const char* NAME_ELEMENT_BOOL_ARRAY;
extern const char* NAME_ELEMENT_BORDER_COLOR;
extern const char* NAME_ELEMENT_BOX;
extern const char* NAME_ELEMENT_BREP;
extern const char* NAME_ELEMENT_BVAR;
extern const char* NAME_ELEMENT_BVEC2;
extern const char* NAME_ELEMENT_BVEC3;
extern const char* NAME_ELEMENT_BVEC4;
extern const char* NAME_ELEMENT_CAMERA;
extern const char* NAME_ELEMENT_CAPSULE;
extern const char* NAME_ELEMENT_CARD;
extern const char* NAME_ELEMENT_CARTESIANPRODUCT;
extern const char* NAME_ELEMENT_CEILING;
extern const char* NAME_ELEMENT_CG_PASS_TYPE____EVALUATE;
extern const char* NAME_ELEMENT_CG_PASS_TYPE____PROGRAM;
extern const char* NAME_ELEMENT_CG_PASS_TYPE____STATES;
extern const char* NAME_ELEMENT_CHANNEL;
extern const char* NAME_ELEMENT_CI;
extern const char* NAME_ELEMENT_CIRCLE;
extern const char* NAME_ELEMENT_CLIP_PLANE;
extern const char* NAME_ELEMENT_CLIP_PLANE_ENABLE;
extern const char* NAME_ELEMENT_CN;
extern const char* NAME_ELEMENT_CODE;
extern const char* NAME_ELEMENT_CODOMAIN;
extern const char* NAME_ELEMENT_COLLADA;
extern const char* NAME_ELEMENT_COLOR;
extern const char* NAME_ELEMENT_COLOR_CLEAR;
extern const char* NAME_ELEMENT_COLOR_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_COLOR_MASK;
extern const char* NAME_ELEMENT_COLOR_MATERIAL;
extern const char* NAME_ELEMENT_COLOR_MATERIAL_ENABLE;
extern const char* NAME_ELEMENT_COLOR_MATERIAL__FACE;
extern const char* NAME_ELEMENT_COLOR_MATERIAL__MODE;
extern const char* NAME_ELEMENT_COLOR_TARGET;
extern const char* NAME_ELEMENT_COLOR____TARGETABLE_FLOAT3_TYPE;
extern const char* NAME_ELEMENT_COMMENTS;
extern const char* NAME_ELEMENT_COMPILER;
extern const char* NAME_ELEMENT_COMPLEXES;
extern const char* NAME_ELEMENT_COMPOSE;
extern const char* NAME_ELEMENT_CONDITION;
extern const char* NAME_ELEMENT_CONE;
extern const char* NAME_ELEMENT_CONJUGATE;
extern const char* NAME_ELEMENT_CONNECT_PARAM;
extern const char* NAME_ELEMENT_CONSTANT;
extern const char* NAME_ELEMENT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_CONSTANT____GLES_TEXTURE_CONSTANT_TYPE;
extern const char* NAME_ELEMENT_CONTRIBUTOR;
extern const char* NAME_ELEMENT_CONTROLLER;
extern const char* NAME_ELEMENT_CONTROL_VERTICES;
extern const char* NAME_ELEMENT_CONVEX_MESH;
extern const char* NAME_ELEMENT_COPYRIGHT;
extern const char* NAME_ELEMENT_COS;
extern const char* NAME_ELEMENT_COSH;
extern const char* NAME_ELEMENT_COT;
extern const char* NAME_ELEMENT_COTH;
extern const char* NAME_ELEMENT_COVERAGE;
extern const char* NAME_ELEMENT_CREATED;
extern const char* NAME_ELEMENT_CREATE_2D;
extern const char* NAME_ELEMENT_CREATE_2D__ARRAY;
extern const char* NAME_ELEMENT_CREATE_2D__FORMAT;
extern const char* NAME_ELEMENT_CREATE_2D__FORMAT__HINT;
extern const char* NAME_ELEMENT_CREATE_2D__INIT_FROM;
extern const char* NAME_ELEMENT_CREATE_3D;
extern const char* NAME_ELEMENT_CREATE_3D__ARRAY;
extern const char* NAME_ELEMENT_CREATE_3D__FORMAT;
extern const char* NAME_ELEMENT_CREATE_3D__FORMAT__HINT;
extern const char* NAME_ELEMENT_CREATE_3D__INIT_FROM;
extern const char* NAME_ELEMENT_CREATE_3D__SIZE;
extern const char* NAME_ELEMENT_CREATE_CUBE;
extern const char* NAME_ELEMENT_CREATE_CUBE__ARRAY;
extern const char* NAME_ELEMENT_CREATE_CUBE__FORMAT;
extern const char* NAME_ELEMENT_CREATE_CUBE__FORMAT__HINT;
extern const char* NAME_ELEMENT_CREATE_CUBE__INIT_FROM;
extern const char* NAME_ELEMENT_CREATE_CUBE__SIZE;
extern const char* NAME_ELEMENT_CSC;
extern const char* NAME_ELEMENT_CSCH;
extern const char* NAME_ELEMENT_CSYMBOL;
extern const char* NAME_ELEMENT_CULL_FACE;
extern const char* NAME_ELEMENT_CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_CURL;
extern const char* NAME_ELEMENT_CURVE;
extern const char* NAME_ELEMENT_CURVES;
extern const char* NAME_ELEMENT_CYLINDER;
extern const char* NAME_ELEMENT_CYLINDER____CYLINDER_TYPE;
extern const char* NAME_ELEMENT_DAMPING;
extern const char* NAME_ELEMENT_DECELERATION;
extern const char* NAME_ELEMENT_DECELERATION____COMMON_FLOAT2_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_DECELERATION____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_DECLARE;
extern const char* NAME_ELEMENT_DEGREE;
extern const char* NAME_ELEMENT_DENSITY;
extern const char* NAME_ELEMENT_DEPTH_BOUNDS;
extern const char* NAME_ELEMENT_DEPTH_BOUNDS_ENABLE;
extern const char* NAME_ELEMENT_DEPTH_CLAMP_ENABLE;
extern const char* NAME_ELEMENT_DEPTH_CLEAR;
extern const char* NAME_ELEMENT_DEPTH_FUNC;
extern const char* NAME_ELEMENT_DEPTH_MASK;
extern const char* NAME_ELEMENT_DEPTH_RANGE;
extern const char* NAME_ELEMENT_DEPTH_TARGET;
extern const char* NAME_ELEMENT_DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_DEST;
extern const char* NAME_ELEMENT_DEST_ALPHA;
extern const char* NAME_ELEMENT_DEST_RGB;
extern const char* NAME_ELEMENT_DETERMINANT;
extern const char* NAME_ELEMENT_DIFF;
extern const char* NAME_ELEMENT_DIFFUSE;
extern const char* NAME_ELEMENT_DIRECTION;
extern const char* NAME_ELEMENT_DIRECTIONAL;
extern const char* NAME_ELEMENT_DITHER_ENABLE;
extern const char* NAME_ELEMENT_DIVERGENCE;
extern const char* NAME_ELEMENT_DIVIDE;
extern const char* NAME_ELEMENT_DOMAIN;
extern const char* NAME_ELEMENT_DOMAINOFAPPLICATION;
extern const char* NAME_ELEMENT_DRAW;
extern const char* NAME_ELEMENT_DYNAMIC;
extern const char* NAME_ELEMENT_DYNAMIC_FRICTION;
extern const char* NAME_ELEMENT_EDGES;
extern const char* NAME_ELEMENT_EFFECT;
extern const char* NAME_ELEMENT_EFFECTOR_INFO;
extern const char* NAME_ELEMENT_ELLIPSE;
extern const char* NAME_ELEMENT_EMISSION;
extern const char* NAME_ELEMENT_EMPTYSET;
extern const char* NAME_ELEMENT_ENABLED;
extern const char* NAME_ELEMENT_ENUM;
extern const char* NAME_ELEMENT_ENUM____GLES_ENUMERATION_TYPE;
extern const char* NAME_ELEMENT_ENUM____GL_ENUMERATION_TYPE;
extern const char* NAME_ELEMENT_ENUM____STRING;
extern const char* NAME_ELEMENT_EQ;
extern const char* NAME_ELEMENT_EQUATION;
extern const char* NAME_ELEMENT_EQUIVALENT;
extern const char* NAME_ELEMENT_EULERGAMMA;
extern const char* NAME_ELEMENT_EVALUATE;
extern const char* NAME_ELEMENT_EVALUATE_SCENE;
extern const char* NAME_ELEMENT_EXACT;
extern const char* NAME_ELEMENT_EXISTS;
extern const char* NAME_ELEMENT_EXP;
extern const char* NAME_ELEMENT_EXPONENTIALE;
extern const char* NAME_ELEMENT_EXTRA;
extern const char* NAME_ELEMENT_FACE;
extern const char* NAME_ELEMENT_FACES;
extern const char* NAME_ELEMENT_FACTORIAL;
extern const char* NAME_ELEMENT_FACTOROF;
extern const char* NAME_ELEMENT_FAIL;
extern const char* NAME_ELEMENT_FALLOFF_ANGLE;
extern const char* NAME_ELEMENT_FALLOFF_EXPONENT;
extern const char* NAME_ELEMENT_FALSE;
extern const char* NAME_ELEMENT_FIXED;
extern const char* NAME_ELEMENT_FIXED2;
extern const char* NAME_ELEMENT_FIXED2X1;
extern const char* NAME_ELEMENT_FIXED2X2;
extern const char* NAME_ELEMENT_FIXED2X3;
extern const char* NAME_ELEMENT_FIXED2X4;
extern const char* NAME_ELEMENT_FIXED3;
extern const char* NAME_ELEMENT_FIXED3X1;
extern const char* NAME_ELEMENT_FIXED3X2;
extern const char* NAME_ELEMENT_FIXED3X3;
extern const char* NAME_ELEMENT_FIXED3X4;
extern const char* NAME_ELEMENT_FIXED4;
extern const char* NAME_ELEMENT_FIXED4X1;
extern const char* NAME_ELEMENT_FIXED4X2;
extern const char* NAME_ELEMENT_FIXED4X3;
extern const char* NAME_ELEMENT_FIXED4X4;
extern const char* NAME_ELEMENT_FLOAT;
extern const char* NAME_ELEMENT_FLOAT1X1;
extern const char* NAME_ELEMENT_FLOAT1X2;
extern const char* NAME_ELEMENT_FLOAT1X3;
extern const char* NAME_ELEMENT_FLOAT1X4;
extern const char* NAME_ELEMENT_FLOAT2;
extern const char* NAME_ELEMENT_FLOAT2X1;
extern const char* NAME_ELEMENT_FLOAT2X2;
extern const char* NAME_ELEMENT_FLOAT2X3;
extern const char* NAME_ELEMENT_FLOAT2X4;
extern const char* NAME_ELEMENT_FLOAT3;
extern const char* NAME_ELEMENT_FLOAT3X1;
extern const char* NAME_ELEMENT_FLOAT3X2;
extern const char* NAME_ELEMENT_FLOAT3X3;
extern const char* NAME_ELEMENT_FLOAT3X4;
extern const char* NAME_ELEMENT_FLOAT4;
extern const char* NAME_ELEMENT_FLOAT4X1;
extern const char* NAME_ELEMENT_FLOAT4X2;
extern const char* NAME_ELEMENT_FLOAT4X3;
extern const char* NAME_ELEMENT_FLOAT4X4;
extern const char* NAME_ELEMENT_FLOAT_ARRAY;
extern const char* NAME_ELEMENT_FLOAT____FLOAT_TYPE;
extern const char* NAME_ELEMENT_FLOOR;
extern const char* NAME_ELEMENT_FOCAL;
extern const char* NAME_ELEMENT_FOG_COLOR;
extern const char* NAME_ELEMENT_FOG_COORD_SRC;
extern const char* NAME_ELEMENT_FOG_DENSITY;
extern const char* NAME_ELEMENT_FOG_ENABLE;
extern const char* NAME_ELEMENT_FOG_END;
extern const char* NAME_ELEMENT_FOG_MODE;
extern const char* NAME_ELEMENT_FOG_START;
extern const char* NAME_ELEMENT_FORALL;
extern const char* NAME_ELEMENT_FORCE_FIELD;
extern const char* NAME_ELEMENT_FORMAT;
extern const char* NAME_ELEMENT_FORMULA;
extern const char* NAME_ELEMENT_FRAME_OBJECT;
extern const char* NAME_ELEMENT_FRAME_ORIGIN;
extern const char* NAME_ELEMENT_FRAME_TCP;
extern const char* NAME_ELEMENT_FRAME_TIP;
extern const char* NAME_ELEMENT_FRONT;
extern const char* NAME_ELEMENT_FRONT_FACE;
extern const char* NAME_ELEMENT_FUNC;
extern const char* NAME_ELEMENT_FX_COLORTARGET_TYPE____PARAM;
extern const char* NAME_ELEMENT_FX_COMMON_COLOR_OR_TEXTURE_TYPE____COLOR;
extern const char* NAME_ELEMENT_FX_COMMON_COLOR_OR_TEXTURE_TYPE____PARAM;
extern const char* NAME_ELEMENT_FX_COMMON_FLOAT_OR_PARAM_TYPE____FLOAT;
extern const char* NAME_ELEMENT_FX_COMMON_FLOAT_OR_PARAM_TYPE____PARAM;
extern const char* NAME_ELEMENT_GCD;
extern const char* NAME_ELEMENT_GEOGRAPHIC_LOCATION;
extern const char* NAME_ELEMENT_GEOMETRY;
extern const char* NAME_ELEMENT_GEQ;
extern const char* NAME_ELEMENT_GLES2_PASS_TYPE____EVALUATE;
extern const char* NAME_ELEMENT_GLES2_PASS_TYPE____STATES;
extern const char* NAME_ELEMENT_GLES2_PROGRAM_TYPE____BIND_ATTRIBUTE;
extern const char* NAME_ELEMENT_GLES2_PROGRAM_TYPE____BIND_UNIFORM;
extern const char* NAME_ELEMENT_GLES2_SHADER_TYPE____SOURCES;
extern const char* NAME_ELEMENT_GLSL_PROGRAM_TYPE____BIND_ATTRIBUTE;
extern const char* NAME_ELEMENT_GLSL_PROGRAM_TYPE____BIND_UNIFORM;
extern const char* NAME_ELEMENT_GRAD;
extern const char* NAME_ELEMENT_GRAVITY;
extern const char* NAME_ELEMENT_GT;
extern const char* NAME_ELEMENT_H;
extern const char* NAME_ELEMENT_HALF;
extern const char* NAME_ELEMENT_HALF2;
extern const char* NAME_ELEMENT_HALF2X1;
extern const char* NAME_ELEMENT_HALF2X2;
extern const char* NAME_ELEMENT_HALF2X3;
extern const char* NAME_ELEMENT_HALF2X4;
extern const char* NAME_ELEMENT_HALF3;
extern const char* NAME_ELEMENT_HALF3X1;
extern const char* NAME_ELEMENT_HALF3X2;
extern const char* NAME_ELEMENT_HALF3X3;
extern const char* NAME_ELEMENT_HALF3X4;
extern const char* NAME_ELEMENT_HALF4;
extern const char* NAME_ELEMENT_HALF4X1;
extern const char* NAME_ELEMENT_HALF4X2;
extern const char* NAME_ELEMENT_HALF4X3;
extern const char* NAME_ELEMENT_HALF4X4;
extern const char* NAME_ELEMENT_HALF_EXTENTS;
extern const char* NAME_ELEMENT_HEIGHT;
extern const char* NAME_ELEMENT_HEX;
extern const char* NAME_ELEMENT_HINT;
extern const char* NAME_ELEMENT_HOLLOW;
extern const char* NAME_ELEMENT_HYPERBOLA;
extern const char* NAME_ELEMENT_IDENT;
extern const char* NAME_ELEMENT_IDREF_ARRAY;
extern const char* NAME_ELEMENT_IMAGE;
extern const char* NAME_ELEMENT_IMAGER;
extern const char* NAME_ELEMENT_IMAGE_TYPE____INIT_FROM;
extern const char* NAME_ELEMENT_IMAGE____FUNCTIONS_TYPE;
extern const char* NAME_ELEMENT_IMAGE____IMAGE_TYPE;
extern const char* NAME_ELEMENT_IMAGINARY;
extern const char* NAME_ELEMENT_IMAGINARYI;
extern const char* NAME_ELEMENT_IMPLIES;
extern const char* NAME_ELEMENT_IMPORT;
extern const char* NAME_ELEMENT_IN;
extern const char* NAME_ELEMENT_INCLUDE;
extern const char* NAME_ELEMENT_INDEX;
extern const char* NAME_ELEMENT_INDEX_OF_REFRACTION;
extern const char* NAME_ELEMENT_INERTIA;
extern const char* NAME_ELEMENT_INFINITY;
extern const char* NAME_ELEMENT_INIT_FROM;
extern const char* NAME_ELEMENT_INLINE;
extern const char* NAME_ELEMENT_INPUT;
extern const char* NAME_ELEMENT_INPUT____INPUT_LOCAL_OFFSET_TYPE;
extern const char* NAME_ELEMENT_INPUT____INPUT_LOCAL_TYPE;
extern const char* NAME_ELEMENT_INSTANCE_ANIMATION;
extern const char* NAME_ELEMENT_INSTANCE_ARTICULATED_SYSTEM;
extern const char* NAME_ELEMENT_INSTANCE_CAMERA;
extern const char* NAME_ELEMENT_INSTANCE_CONTROLLER;
extern const char* NAME_ELEMENT_INSTANCE_EFFECT;
extern const char* NAME_ELEMENT_INSTANCE_EFFECT_TYPE____SETPARAM;
extern const char* NAME_ELEMENT_INSTANCE_FORCE_FIELD;
extern const char* NAME_ELEMENT_INSTANCE_FORMULA;
extern const char* NAME_ELEMENT_INSTANCE_GEOMETRY;
extern const char* NAME_ELEMENT_INSTANCE_IMAGE;
extern const char* NAME_ELEMENT_INSTANCE_JOINT;
extern const char* NAME_ELEMENT_INSTANCE_KINEMATICS_MODEL;
extern const char* NAME_ELEMENT_INSTANCE_KINEMATICS_SCENE;
extern const char* NAME_ELEMENT_INSTANCE_LIGHT;
extern const char* NAME_ELEMENT_INSTANCE_MATERIAL;
extern const char* NAME_ELEMENT_INSTANCE_MATERIAL_TYPE____BIND;
extern const char* NAME_ELEMENT_INSTANCE_MATERIAL____INSTANCE_MATERIAL_TYPE;
extern const char* NAME_ELEMENT_INSTANCE_NODE;
extern const char* NAME_ELEMENT_INSTANCE_PHYSICS_MATERIAL;
extern const char* NAME_ELEMENT_INSTANCE_PHYSICS_MODEL;
extern const char* NAME_ELEMENT_INSTANCE_PHYSICS_SCENE;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_CONSTRAINT;
extern const char* NAME_ELEMENT_INSTANCE_VISUAL_SCENE;
extern const char* NAME_ELEMENT_INT;
extern const char* NAME_ELEMENT_INT2;
extern const char* NAME_ELEMENT_INT2X1;
extern const char* NAME_ELEMENT_INT2X2;
extern const char* NAME_ELEMENT_INT2X3;
extern const char* NAME_ELEMENT_INT2X4;
extern const char* NAME_ELEMENT_INT3;
extern const char* NAME_ELEMENT_INT3X1;
extern const char* NAME_ELEMENT_INT3X2;
extern const char* NAME_ELEMENT_INT3X3;
extern const char* NAME_ELEMENT_INT3X4;
extern const char* NAME_ELEMENT_INT4;
extern const char* NAME_ELEMENT_INT4X1;
extern const char* NAME_ELEMENT_INT4X2;
extern const char* NAME_ELEMENT_INT4X3;
extern const char* NAME_ELEMENT_INT4X4;
extern const char* NAME_ELEMENT_INTEGERS;
extern const char* NAME_ELEMENT_INTERPENETRATE;
extern const char* NAME_ELEMENT_INTERSECT;
extern const char* NAME_ELEMENT_INTERVAL;
extern const char* NAME_ELEMENT_INT_ARRAY;
extern const char* NAME_ELEMENT_INT____INT_TYPE;
extern const char* NAME_ELEMENT_INT____INT_TYPE____MATHML;
extern const char* NAME_ELEMENT_INVERSE;
extern const char* NAME_ELEMENT_INVERT;
extern const char* NAME_ELEMENT_IVEC2;
extern const char* NAME_ELEMENT_IVEC3;
extern const char* NAME_ELEMENT_IVEC4;
extern const char* NAME_ELEMENT_JERK;
extern const char* NAME_ELEMENT_JERK____COMMON_FLOAT2_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_JERK____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_JOINT;
extern const char* NAME_ELEMENT_JOINTS;
extern const char* NAME_ELEMENT_KEYWORDS;
extern const char* NAME_ELEMENT_KINEMATICS;
extern const char* NAME_ELEMENT_KINEMATICS_MODEL;
extern const char* NAME_ELEMENT_KINEMATICS_SCENE;
extern const char* NAME_ELEMENT_LAMBDA;
extern const char* NAME_ELEMENT_LAMBERT;
extern const char* NAME_ELEMENT_LAPLACIAN;
extern const char* NAME_ELEMENT_LATITUDE;
extern const char* NAME_ELEMENT_LAYER;
extern const char* NAME_ELEMENT_LCM;
extern const char* NAME_ELEMENT_LEQ;
extern const char* NAME_ELEMENT_LIBRARY_ANIMATIONS;
extern const char* NAME_ELEMENT_LIBRARY_ANIMATION_CLIPS;
extern const char* NAME_ELEMENT_LIBRARY_ARTICULATED_SYSTEMS;
extern const char* NAME_ELEMENT_LIBRARY_CAMERAS;
extern const char* NAME_ELEMENT_LIBRARY_CONTROLLERS;
extern const char* NAME_ELEMENT_LIBRARY_EFFECTS;
extern const char* NAME_ELEMENT_LIBRARY_FORCE_FIELDS;
extern const char* NAME_ELEMENT_LIBRARY_FORMULAS;
extern const char* NAME_ELEMENT_LIBRARY_GEOMETRIES;
extern const char* NAME_ELEMENT_LIBRARY_IMAGES;
extern const char* NAME_ELEMENT_LIBRARY_IMAGES__IMAGE__INIT_FROM__HEX;
extern const char* NAME_ELEMENT_LIBRARY_JOINTS;
extern const char* NAME_ELEMENT_LIBRARY_KINEMATICS_MODELS;
extern const char* NAME_ELEMENT_LIBRARY_KINEMATICS_SCENES;
extern const char* NAME_ELEMENT_LIBRARY_LIGHTS;
extern const char* NAME_ELEMENT_LIBRARY_MATERIALS;
extern const char* NAME_ELEMENT_LIBRARY_NODES;
extern const char* NAME_ELEMENT_LIBRARY_PHYSICS_MATERIALS;
extern const char* NAME_ELEMENT_LIBRARY_PHYSICS_MODELS;
extern const char* NAME_ELEMENT_LIBRARY_PHYSICS_SCENES;
extern const char* NAME_ELEMENT_LIBRARY_VISUAL_SCENES;
extern const char* NAME_ELEMENT_LIGHT;
extern const char* NAME_ELEMENT_LIGHTING_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_AMBIENT;
extern const char* NAME_ELEMENT_LIGHT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_LIGHT_DIFFUSE;
extern const char* NAME_ELEMENT_LIGHT_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_LINEAR_ATTENUATION;
extern const char* NAME_ELEMENT_LIGHT_MODEL_AMBIENT;
extern const char* NAME_ELEMENT_LIGHT_MODEL_COLOR_CONTROL;
extern const char* NAME_ELEMENT_LIGHT_MODEL_LOCAL_VIEWER_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_MODEL_TWO_SIDE_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_POSITION;
extern const char* NAME_ELEMENT_LIGHT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_LIGHT_SPECULAR;
extern const char* NAME_ELEMENT_LIGHT_SPOT_CUTOFF;
extern const char* NAME_ELEMENT_LIGHT_SPOT_DIRECTION;
extern const char* NAME_ELEMENT_LIGHT_SPOT_EXPONENT;
extern const char* NAME_ELEMENT_LIGHT_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_LIGHT__TECHNIQUE_COMMON__AMBIENT;
extern const char* NAME_ELEMENT_LIMIT;
extern const char* NAME_ELEMENT_LIMITS;
extern const char* NAME_ELEMENT_LIMITS____JOINT_LIMITS_TYPE;
extern const char* NAME_ELEMENT_LIMITS____KINEMATICS_LIMITS_TYPE;
extern const char* NAME_ELEMENT_LINE;
extern const char* NAME_ELEMENT_LINEAR;
extern const char* NAME_ELEMENT_LINEAR_ATTENUATION;
extern const char* NAME_ELEMENT_LINES;
extern const char* NAME_ELEMENT_LINESTRIPS;
extern const char* NAME_ELEMENT_LINE_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_LINE_STIPPLE;
extern const char* NAME_ELEMENT_LINE_STIPPLE_ENABLE;
extern const char* NAME_ELEMENT_LINE_WIDTH;
extern const char* NAME_ELEMENT_LINK;
extern const char* NAME_ELEMENT_LINKER;
extern const char* NAME_ELEMENT_LIST;
extern const char* NAME_ELEMENT_LN;
extern const char* NAME_ELEMENT_LOCKED;
extern const char* NAME_ELEMENT_LOG;
extern const char* NAME_ELEMENT_LOGBASE;
extern const char* NAME_ELEMENT_LOGIC_OP;
extern const char* NAME_ELEMENT_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_LONGITUDE;
extern const char* NAME_ELEMENT_LOOKAT;
extern const char* NAME_ELEMENT_LOWLIMIT;
extern const char* NAME_ELEMENT_LT;
extern const char* NAME_ELEMENT_MAGFILTER;
extern const char* NAME_ELEMENT_MASK;
extern const char* NAME_ELEMENT_MASS;
extern const char* NAME_ELEMENT_MASS_FRAME;
extern const char* NAME_ELEMENT_MAT2;
extern const char* NAME_ELEMENT_MAT3;
extern const char* NAME_ELEMENT_MAT4;
extern const char* NAME_ELEMENT_MATERIAL;
extern const char* NAME_ELEMENT_MATERIAL_AMBIENT;
extern const char* NAME_ELEMENT_MATERIAL_DIFFUSE;
extern const char* NAME_ELEMENT_MATERIAL_EMISSION;
extern const char* NAME_ELEMENT_MATERIAL_SHININESS;
extern const char* NAME_ELEMENT_MATERIAL_SPECULAR;
extern const char* NAME_ELEMENT_MATH;
extern const char* NAME_ELEMENT_MATRIX;
extern const char* NAME_ELEMENT_MATRIXROW;
extern const char* NAME_ELEMENT_MATRIX____MATRIX_TYPE;
extern const char* NAME_ELEMENT_MATRIX____MATRIX_TYPE____MATHML;
extern const char* NAME_ELEMENT_MAX;
extern const char* NAME_ELEMENT_MAX_ANISOTROPY;
extern const char* NAME_ELEMENT_MAX____ARITH_TYPE;
extern const char* NAME_ELEMENT_MAX____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_MAX____MINMAX_TYPE;
extern const char* NAME_ELEMENT_MAX____TARGETABLE_FLOAT3_TYPE;
extern const char* NAME_ELEMENT_MEAN;
extern const char* NAME_ELEMENT_MEDIAN;
extern const char* NAME_ELEMENT_MESH;
extern const char* NAME_ELEMENT_MIN;
extern const char* NAME_ELEMENT_MINFILTER;
extern const char* NAME_ELEMENT_MINUS;
extern const char* NAME_ELEMENT_MIN____ARITH_TYPE;
extern const char* NAME_ELEMENT_MIN____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_MIN____MINMAX_TYPE;
extern const char* NAME_ELEMENT_MIN____TARGETABLE_FLOAT3_TYPE;
extern const char* NAME_ELEMENT_MIPFILTER;
extern const char* NAME_ELEMENT_MIPS;
extern const char* NAME_ELEMENT_MIP_BIAS;
extern const char* NAME_ELEMENT_MIP_MAX_LEVEL;
extern const char* NAME_ELEMENT_MIP_MIN_LEVEL;
extern const char* NAME_ELEMENT_MODE;
extern const char* NAME_ELEMENT_MODEL_VIEW_MATRIX;
extern const char* NAME_ELEMENT_MODE____MODE_TYPE;
extern const char* NAME_ELEMENT_MODIFIED;
extern const char* NAME_ELEMENT_MODIFIER;
extern const char* NAME_ELEMENT_MOMENT;
extern const char* NAME_ELEMENT_MOMENTABOUT;
extern const char* NAME_ELEMENT_MORPH;
extern const char* NAME_ELEMENT_MOTION;
extern const char* NAME_ELEMENT_MULTISAMPLE_ENABLE;
extern const char* NAME_ELEMENT_NAME_ARRAY;
extern const char* NAME_ELEMENT_NATURALNUMBERS;
extern const char* NAME_ELEMENT_NEQ;
extern const char* NAME_ELEMENT_NEWPARAM;
extern const char* NAME_ELEMENT_NEWPARAM____CG_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____FORMULA_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____FX_COMMON_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____FX_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____GLES_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____GLSL_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____KINEMATICS_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NODE;
extern const char* NAME_ELEMENT_NORMALIZE_ENABLE;
extern const char* NAME_ELEMENT_NOT;
extern const char* NAME_ELEMENT_NOTANUMBER;
extern const char* NAME_ELEMENT_NOTIN;
extern const char* NAME_ELEMENT_NOTPRSUBSET;
extern const char* NAME_ELEMENT_NOTSUBSET;
extern const char* NAME_ELEMENT_NURBS;
extern const char* NAME_ELEMENT_NURBS_SURFACE;
extern const char* NAME_ELEMENT_NURBS_SURFACE_TYPE____CONTROL_VERTICES;
extern const char* NAME_ELEMENT_NURBS_TYPE____CONTROL_VERTICES;
extern const char* NAME_ELEMENT_OPTICS;
extern const char* NAME_ELEMENT_OPTICS__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_OR;
extern const char* NAME_ELEMENT_ORIENT;
extern const char* NAME_ELEMENT_ORIGIN;
extern const char* NAME_ELEMENT_ORIGIN____FLOAT3_TYPE;
extern const char* NAME_ELEMENT_ORIGIN____ORIGIN_TYPE;
extern const char* NAME_ELEMENT_ORTHOGRAPHIC;
extern const char* NAME_ELEMENT_OTHERWISE;
extern const char* NAME_ELEMENT_OUTERPRODUCT;
extern const char* NAME_ELEMENT_P;
extern const char* NAME_ELEMENT_PARABOLA;
extern const char* NAME_ELEMENT_PARAM;
extern const char* NAME_ELEMENT_PARAM____COMMON_PARAM_TYPE;
extern const char* NAME_ELEMENT_PARAM____KINEMATICS_PARAM_TYPE;
extern const char* NAME_ELEMENT_PARAM____NCNAME;
extern const char* NAME_ELEMENT_PARAM____PARAM_TYPE;
extern const char* NAME_ELEMENT_PARTIALDIFF;
extern const char* NAME_ELEMENT_PASS;
extern const char* NAME_ELEMENT_PASS____CG_PASS_TYPE;
extern const char* NAME_ELEMENT_PASS____GLES2_PASS_TYPE;
extern const char* NAME_ELEMENT_PCURVES;
extern const char* NAME_ELEMENT_PERSPECTIVE;
extern const char* NAME_ELEMENT_PH;
extern const char* NAME_ELEMENT_PHONG;
extern const char* NAME_ELEMENT_PHYSICS_MATERIAL;
extern const char* NAME_ELEMENT_PHYSICS_MATERIAL_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_PHYSICS_MODEL;
extern const char* NAME_ELEMENT_PHYSICS_SCENE;
extern const char* NAME_ELEMENT_PHYSICS_SCENE_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_PI;
extern const char* NAME_ELEMENT_PIECE;
extern const char* NAME_ELEMENT_PIECEWISE;
extern const char* NAME_ELEMENT_PLANE;
extern const char* NAME_ELEMENT_PLUS;
extern const char* NAME_ELEMENT_POINT;
extern const char* NAME_ELEMENT_POINT_DISTANCE_ATTENUATION;
extern const char* NAME_ELEMENT_POINT_FADE_THRESHOLD_SIZE;
extern const char* NAME_ELEMENT_POINT_SIZE;
extern const char* NAME_ELEMENT_POINT_SIZE_ENABLE;
extern const char* NAME_ELEMENT_POINT_SIZE_MAX;
extern const char* NAME_ELEMENT_POINT_SIZE_MIN;
extern const char* NAME_ELEMENT_POINT_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_POLYGONS;
extern const char* NAME_ELEMENT_POLYGON_MODE;
extern const char* NAME_ELEMENT_POLYGON_MODE__FACE;
extern const char* NAME_ELEMENT_POLYGON_MODE__MODE;
extern const char* NAME_ELEMENT_POLYGON_OFFSET;
extern const char* NAME_ELEMENT_POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_OFFSET_LINE_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_OFFSET_POINT_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_STIPPLE_ENABLE;
extern const char* NAME_ELEMENT_POLYLIST;
extern const char* NAME_ELEMENT_POWER;
extern const char* NAME_ELEMENT_PRIMES;
extern const char* NAME_ELEMENT_PRISMATIC;
extern const char* NAME_ELEMENT_PRODUCT;
extern const char* NAME_ELEMENT_PROFILE_BRIDGE;
extern const char* NAME_ELEMENT_PROFILE_CG;
extern const char* NAME_ELEMENT_PROFILE_CG_TYPE____TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER__BIND_UNIFORM;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER__BIND_UNIFORM__PARAM;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER__SOURCES;
extern const char* NAME_ELEMENT_PROFILE_COMMON;
extern const char* NAME_ELEMENT_PROFILE_COMMON_TYPE____TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_COMMON__TECHNIQUE__CONSTANT;
extern const char* NAME_ELEMENT_PROFILE_GLES;
extern const char* NAME_ELEMENT_PROFILE_GLES2;
extern const char* NAME_ELEMENT_PROFILE_GLES2_TYPE____NEWPARAM;
extern const char* NAME_ELEMENT_PROFILE_GLES2_TYPE____TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__NEWPARAM__USERTYPE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__NEWPARAM__USERTYPE__SETPARAM;
extern const char* NAME_ELEMENT_PROFILE_GLES2__NEWPARAM__USERTYPE__SETPARAM__ARRAY;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__PROGRAM__BIND_UNIFORM__PARAM;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__ALPHA;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__RGB;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_ALPHA;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_RGB;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_ALPHA;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_RGB;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC__DEST;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC__SRC;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__COLOR_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__CULL_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_RANGE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DITHER_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__FRONT_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__LINE_WIDTH;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__POINT_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__POLYGON_OFFSET;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SCISSOR;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__BACK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__FRONT;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__REF;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC__REF;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES_TYPE____TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__EVALUATE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_FUNC__VALUE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_FUNC__DEST;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_FUNC__SRC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CLIP_PLANE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CLIP_PLANE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__COLOR_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__COLOR_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__COLOR_MATERIAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CULL_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_RANGE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DITHER_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_DENSITY;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_END;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_MODE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_START;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FRONT_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHTING_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_LINEAR_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_MODEL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_MODEL_TWO_SIDE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_POSITION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPOT_CUTOFF;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPOT_DIRECTION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPOT_EXPONENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LINE_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LINE_WIDTH;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LOGIC_OP;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_EMISSION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_SHININESS;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MODEL_VIEW_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MULTISAMPLE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__NORMALIZE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_DISTANCE_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_FADE_THRESHOLD_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SIZE_MAX;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SIZE_MIN;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POLYGON_OFFSET;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__PROJECTION_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__RESCALE_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_ONE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SCISSOR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SHADE_MODEL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC__REF;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL;
extern const char* NAME_ELEMENT_PROFILE_GLSL_TYPE____TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__EVALUATE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__PROGRAM__BIND_UNIFORM__PARAM;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_FUNC__VALUE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__ALPHA;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__RGB;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_ALPHA;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_RGB;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_ALPHA;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_RGB;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC__DEST;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC__SRC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CLIP_PLANE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CLIP_PLANE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__COLOR_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__COLOR_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__COLOR_MATERIAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CULL_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_RANGE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DITHER_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_DENSITY;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_END;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_MODE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_START;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FRONT_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHTING_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_LINEAR_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_MODEL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_MODEL_TWO_SIDE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_POSITION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPOT_CUTOFF;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPOT_DIRECTION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPOT_EXPONENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LINE_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LINE_WIDTH;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LOGIC_OP;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_EMISSION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_SHININESS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MODEL_VIEW_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MULTISAMPLE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__NORMALIZE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_DISTANCE_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_FADE_THRESHOLD_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SIZE_MAX;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SIZE_MIN;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POLYGON_OFFSET;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__PROJECTION_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__RESCALE_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_ONE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SCISSOR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SHADE_MODEL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__BACK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__FRONT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__REF;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC__REF;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__FACE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FACE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROGRAM;
extern const char* NAME_ELEMENT_PROGRAM____GLES2_PROGRAM_TYPE;
extern const char* NAME_ELEMENT_PROGRAM____GLSL_PROGRAM_TYPE;
extern const char* NAME_ELEMENT_PROJECTION_MATRIX;
extern const char* NAME_ELEMENT_PRSUBSET;
extern const char* NAME_ELEMENT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_QUOTIENT;
extern const char* NAME_ELEMENT_RADIUS;
extern const char* NAME_ELEMENT_RADIUS____FLOAT2_TYPE;
extern const char* NAME_ELEMENT_RADIUS____FLOAT3_TYPE;
extern const char* NAME_ELEMENT_RADIUS____FLOAT_TYPE;
extern const char* NAME_ELEMENT_RATIONALS;
extern const char* NAME_ELEMENT_REAL;
extern const char* NAME_ELEMENT_REALS;
extern const char* NAME_ELEMENT_REF;
extern const char* NAME_ELEMENT_REFLECTIVE;
extern const char* NAME_ELEMENT_REFLECTIVITY;
extern const char* NAME_ELEMENT_REF_ATTACHMENT;
extern const char* NAME_ELEMENT_REF____ANYURI;
extern const char* NAME_ELEMENT_REM;
extern const char* NAME_ELEMENT_RENDER;
extern const char* NAME_ELEMENT_RENDERABLE;
extern const char* NAME_ELEMENT_RENDER__INSTANCE_MATERIAL;
extern const char* NAME_ELEMENT_RENDER__INSTANCE_MATERIAL__BIND;
extern const char* NAME_ELEMENT_RESCALE_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_RESTITUTION;
extern const char* NAME_ELEMENT_REVISION;
extern const char* NAME_ELEMENT_REVOLUTE;
extern const char* NAME_ELEMENT_RGB;
extern const char* NAME_ELEMENT_RIGID_BODY;
extern const char* NAME_ELEMENT_RIGID_BODY_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW;
extern const char* NAME_ELEMENT_RIGID_CONSTRAINT;
extern const char* NAME_ELEMENT_RIGID_CONSTRAINT_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_RIGID_CONSTRAINT__TECHNIQUE_COMMON__LIMITS;
extern const char* NAME_ELEMENT_RIGID_CONSTRAINT__TECHNIQUE_COMMON__LIMITS__LINEAR;
extern const char* NAME_ELEMENT_ROOT;
extern const char* NAME_ELEMENT_ROTATE;
extern const char* NAME_ELEMENT_SAMPLER;
extern const char* NAME_ELEMENT_SAMPLER1D;
extern const char* NAME_ELEMENT_SAMPLER2D;
extern const char* NAME_ELEMENT_SAMPLER2D____FX_SAMPLER2D_TYPE;
extern const char* NAME_ELEMENT_SAMPLER2D____GLES_SAMPLER_TYPE;
extern const char* NAME_ELEMENT_SAMPLER3D;
extern const char* NAME_ELEMENT_SAMPLERCUBE;
extern const char* NAME_ELEMENT_SAMPLERDEPTH;
extern const char* NAME_ELEMENT_SAMPLERRECT;
extern const char* NAME_ELEMENT_SAMPLER_IMAGE;
extern const char* NAME_ELEMENT_SAMPLER_STATES;
extern const char* NAME_ELEMENT_SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_SAMPLE_ALPHA_TO_ONE_ENABLE;
extern const char* NAME_ELEMENT_SAMPLE_COVERAGE;
extern const char* NAME_ELEMENT_SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_SAMPLE_COVERAGE__VALUE;
extern const char* NAME_ELEMENT_SCALARPRODUCT;
extern const char* NAME_ELEMENT_SCALE;
extern const char* NAME_ELEMENT_SCENE;
extern const char* NAME_ELEMENT_SCISSOR;
extern const char* NAME_ELEMENT_SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_SDEV;
extern const char* NAME_ELEMENT_SEC;
extern const char* NAME_ELEMENT_SECH;
extern const char* NAME_ELEMENT_SELECTOR;
extern const char* NAME_ELEMENT_SEMANTIC;
extern const char* NAME_ELEMENT_SEMANTICS;
extern const char* NAME_ELEMENT_SEMANTIC____NCNAME;
extern const char* NAME_ELEMENT_SEMANTIC____TOKEN;
extern const char* NAME_ELEMENT_SEP;
extern const char* NAME_ELEMENT_SET;
extern const char* NAME_ELEMENT_SETDIFF;
extern const char* NAME_ELEMENT_SETPARAM;
extern const char* NAME_ELEMENT_SETPARAM____CG_SETPARAM_TYPE;
extern const char* NAME_ELEMENT_SETPARAM____FORMULA_SETPARAM_TYPE;
extern const char* NAME_ELEMENT_SETPARAM____KINEMATICS_SETPARAM_TYPE;
extern const char* NAME_ELEMENT_SHADER;
extern const char* NAME_ELEMENT_SHADER____GLES2_SHADER_TYPE;
extern const char* NAME_ELEMENT_SHADER____GLSL_SHADER_TYPE;
extern const char* NAME_ELEMENT_SHADE_MODEL;
extern const char* NAME_ELEMENT_SHAPE;
extern const char* NAME_ELEMENT_SHELLS;
extern const char* NAME_ELEMENT_SHININESS;
extern const char* NAME_ELEMENT_SIDREF;
extern const char* NAME_ELEMENT_SIDREF_ARRAY;
extern const char* NAME_ELEMENT_SIN;
extern const char* NAME_ELEMENT_SINH;
extern const char* NAME_ELEMENT_SIZE;
extern const char* NAME_ELEMENT_SIZE_EXACT;
extern const char* NAME_ELEMENT_SIZE_RATIO;
extern const char* NAME_ELEMENT_SKELETON;
extern const char* NAME_ELEMENT_SKEW;
extern const char* NAME_ELEMENT_SKIN;
extern const char* NAME_ELEMENT_SOLIDS;
extern const char* NAME_ELEMENT_SOURCE;
extern const char* NAME_ELEMENT_SOURCES;
extern const char* NAME_ELEMENT_SOURCES____FX_SOURCES_TYPE;
extern const char* NAME_ELEMENT_SOURCE_DATA;
extern const char* NAME_ELEMENT_SOURCE_TYPE____TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_SPECULAR;
extern const char* NAME_ELEMENT_SPEED;
extern const char* NAME_ELEMENT_SPEED____COMMON_FLOAT2_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_SPEED____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_SPHERE;
extern const char* NAME_ELEMENT_SPLINE;
extern const char* NAME_ELEMENT_SPLINE_TYPE____CONTROL_VERTICES;
extern const char* NAME_ELEMENT_SPOT;
extern const char* NAME_ELEMENT_SPRING;
extern const char* NAME_ELEMENT_SPRING__LINEAR;
extern const char* NAME_ELEMENT_SRC;
extern const char* NAME_ELEMENT_SRC_ALPHA;
extern const char* NAME_ELEMENT_SRC_RGB;
extern const char* NAME_ELEMENT_STATES;
extern const char* NAME_ELEMENT_STATIC_FRICTION;
extern const char* NAME_ELEMENT_STENCIL_CLEAR;
extern const char* NAME_ELEMENT_STENCIL_FUNC;
extern const char* NAME_ELEMENT_STENCIL_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_STENCIL_MASK;
extern const char* NAME_ELEMENT_STENCIL_MASK_SEPARATE;
extern const char* NAME_ELEMENT_STENCIL_OP;
extern const char* NAME_ELEMENT_STENCIL_OP_SEPARATE;
extern const char* NAME_ELEMENT_STENCIL_TARGET;
extern const char* NAME_ELEMENT_STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_STIFFNESS;
extern const char* NAME_ELEMENT_STRING;
extern const char* NAME_ELEMENT_SUBJECT;
extern const char* NAME_ELEMENT_SUBSET;
extern const char* NAME_ELEMENT_SUM;
extern const char* NAME_ELEMENT_SURFACE;
extern const char* NAME_ELEMENT_SURFACES;
extern const char* NAME_ELEMENT_SURFACE_CURVES;
extern const char* NAME_ELEMENT_SURFACE_TYPE____CYLINDER;
extern const char* NAME_ELEMENT_SWEPT_SURFACE;
extern const char* NAME_ELEMENT_SWING_CONE_AND_TWIST;
extern const char* NAME_ELEMENT_TAN;
extern const char* NAME_ELEMENT_TANH;
extern const char* NAME_ELEMENT_TARGET;
extern const char* NAME_ELEMENT_TARGETS;
extern const char* NAME_ELEMENT_TARGET_VALUE;
extern const char* NAME_ELEMENT_TECHNIQUE;
extern const char* NAME_ELEMENT_TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_TECHNIQUE_COMMON____FORMULA_TECHNIQUE_TYPE;
extern const char* NAME_ELEMENT_TECHNIQUE_COMMON____KINEMATICS_MODEL_TECHNIQUE_TYPE;
extern const char* NAME_ELEMENT_TECHNIQUE_COMMON____KINEMATICS_TECHNIQUE_TYPE;
extern const char* NAME_ELEMENT_TECHNIQUE_COMMON____MOTION_TECHNIQUE_TYPE;
extern const char* NAME_ELEMENT_TECHNIQUE_HINT;
extern const char* NAME_ELEMENT_TECHNIQUE_OVERRIDE;
extern const char* NAME_ELEMENT_TECHNIQUE____TECHNIQUE_TYPE;
extern const char* NAME_ELEMENT_TENDSTO;
extern const char* NAME_ELEMENT_TEXCOMBINER;
extern const char* NAME_ELEMENT_TEXCOORD;
extern const char* NAME_ELEMENT_TEXENV;
extern const char* NAME_ELEMENT_TEXTURE;
extern const char* NAME_ELEMENT_TEXTURE1D;
extern const char* NAME_ELEMENT_TEXTURE1D_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE2D;
extern const char* NAME_ELEMENT_TEXTURE2D_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE3D;
extern const char* NAME_ELEMENT_TEXTURE3D_ENABLE;
extern const char* NAME_ELEMENT_TEXTURECUBE;
extern const char* NAME_ELEMENT_TEXTURECUBE_ENABLE;
extern const char* NAME_ELEMENT_TEXTUREDEPTH;
extern const char* NAME_ELEMENT_TEXTUREDEPTH_ENABLE;
extern const char* NAME_ELEMENT_TEXTURERECT;
extern const char* NAME_ELEMENT_TEXTURERECT_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE_ENV_COLOR;
extern const char* NAME_ELEMENT_TEXTURE_ENV_MODE;
extern const char* NAME_ELEMENT_TEXTURE_PIPELINE;
extern const char* NAME_ELEMENT_TIMES;
extern const char* NAME_ELEMENT_TIME_STEP;
extern const char* NAME_ELEMENT_TITLE;
extern const char* NAME_ELEMENT_TOKEN_ARRAY;
extern const char* NAME_ELEMENT_TORUS;
extern const char* NAME_ELEMENT_TRANSLATE;
extern const char* NAME_ELEMENT_TRANSPARENCY;
extern const char* NAME_ELEMENT_TRANSPARENT;
extern const char* NAME_ELEMENT_TRANSPOSE;
extern const char* NAME_ELEMENT_TRIANGLES;
extern const char* NAME_ELEMENT_TRIFANS;
extern const char* NAME_ELEMENT_TRISTRIPS;
extern const char* NAME_ELEMENT_TRUE;
extern const char* NAME_ELEMENT_UNION;
extern const char* NAME_ELEMENT_UNIT;
extern const char* NAME_ELEMENT_UNNORMALIZED;
extern const char* NAME_ELEMENT_UPLIMIT;
extern const char* NAME_ELEMENT_UP_AXIS;
extern const char* NAME_ELEMENT_USERTYPE;
extern const char* NAME_ELEMENT_USERTYPE____CG_USER_TYPE;
extern const char* NAME_ELEMENT_V;
extern const char* NAME_ELEMENT_VALUE;
extern const char* NAME_ELEMENT_VALUE____COMMON_FLOAT_OR_PARAM_TYPE;
extern const char* NAME_ELEMENT_VALUE____FX_SAMPLER1D_TYPE;
extern const char* NAME_ELEMENT_VALUE____FX_SAMPLER2D_TYPE;
extern const char* NAME_ELEMENT_VALUE____FX_SAMPLER3D_TYPE;
extern const char* NAME_ELEMENT_VALUE____FX_SAMPLERCUBE_TYPE;
extern const char* NAME_ELEMENT_VALUE____FX_SAMPLERDEPTH_TYPE;
extern const char* NAME_ELEMENT_VALUE____FX_SAMPLERRECT_TYPE;
extern const char* NAME_ELEMENT_VALUE____GLES_TEXTURE_PIPELINE_TYPE;
extern const char* NAME_ELEMENT_VARIANCE;
extern const char* NAME_ELEMENT_VCOUNT;
extern const char* NAME_ELEMENT_VEC2;
extern const char* NAME_ELEMENT_VEC3;
extern const char* NAME_ELEMENT_VEC4;
extern const char* NAME_ELEMENT_VECTOR;
extern const char* NAME_ELEMENT_VECTORPRODUCT;
extern const char* NAME_ELEMENT_VELOCITY;
extern const char* NAME_ELEMENT_VERTEX_WEIGHTS;
extern const char* NAME_ELEMENT_VERTICES;
extern const char* NAME_ELEMENT_VISUAL_SCENE;
extern const char* NAME_ELEMENT_WIRES;
extern const char* NAME_ELEMENT_WRAP_P;
extern const char* NAME_ELEMENT_WRAP_S;
extern const char* NAME_ELEMENT_WRAP_S____FX_SAMPLER_WRAP_ENUM;
extern const char* NAME_ELEMENT_WRAP_S____GLES_SAMPLER_WRAP_ENUM;
extern const char* NAME_ELEMENT_WRAP_T;
extern const char* NAME_ELEMENT_WRAP_T____FX_SAMPLER_WRAP_ENUM;
extern const char* NAME_ELEMENT_WRAP_T____GLES_SAMPLER_WRAP_ENUM;
extern const char* NAME_ELEMENT_XFOV;
extern const char* NAME_ELEMENT_XMAG;
extern const char* NAME_ELEMENT_XOR;
extern const char* NAME_ELEMENT_YFOV;
extern const char* NAME_ELEMENT_YMAG;
extern const char* NAME_ELEMENT_ZFAIL;
extern const char* NAME_ELEMENT_ZFAR;
extern const char* NAME_ELEMENT_ZNEAR;
extern const char* NAME_ELEMENT_ZPASS;


const StringHash HASH_ATTRIBUTE_ALTIMG = 109293623;
const StringHash HASH_ATTRIBUTE_ALTTEXT = 138128532;
const StringHash HASH_ATTRIBUTE_ARRAY_INDEX = 255382088;
const StringHash HASH_ATTRIBUTE_AUTO_GENERATE = 76012821;
const StringHash HASH_ATTRIBUTE_AXIS = 429827;
const StringHash HASH_ATTRIBUTE_BASE = 67145573;
const StringHash HASH_ATTRIBUTE_BASELINE = 144452293;
const StringHash HASH_ATTRIBUTE_BODY = 431545;
const StringHash HASH_ATTRIBUTE_CAMERA_NODE = 136173157;
const StringHash HASH_ATTRIBUTE_CHANNELS = 243617443;
const StringHash HASH_ATTRIBUTE_CLASS = 6957219;
const StringHash HASH_ATTRIBUTE_CLOSED = 111372724;
const StringHash HASH_ATTRIBUTE_CLOSED_U = 57262277;
const StringHash HASH_ATTRIBUTE_CLOSED_V = 57262278;
const StringHash HASH_ATTRIBUTE_CLOSURE = 171355365;
const StringHash HASH_ATTRIBUTE_CONSTRAINT = 180279812;
const StringHash HASH_ATTRIBUTE_CONVEX_HULL_OF = 167766694;
const StringHash HASH_ATTRIBUTE_COUNT = 6974548;
const StringHash HASH_ATTRIBUTE_DEFINITIONURL = 5848844;
const StringHash HASH_ATTRIBUTE_DEGREE = 111929525;
const StringHash HASH_ATTRIBUTE_DEGREE_U = 199802309;
const StringHash HASH_ATTRIBUTE_DEGREE_V = 199802310;
const StringHash HASH_ATTRIBUTE_DEPTH = 6997928;
const StringHash HASH_ATTRIBUTE_DIGITS = 112189619;
const StringHash HASH_ATTRIBUTE_DISPLAY = 185234153;
const StringHash HASH_ATTRIBUTE_ENABLE = 113539365;
const StringHash HASH_ATTRIBUTE_ENCODING = 77965959;
const StringHash HASH_ATTRIBUTE_END = 27716;
const StringHash HASH_ATTRIBUTE_ENTRY = 7101337;
const StringHash HASH_ATTRIBUTE_FACE = 444309;
const StringHash HASH_ATTRIBUTE_FORMAT = 114725764;
const StringHash HASH_ATTRIBUTE_HEIGHT = 116129268;
const StringHash HASH_ATTRIBUTE_HREF = 456886;
const StringHash HASH_ATTRIBUTE_ID = 1780;
const StringHash HASH_ATTRIBUTE_INDEX = 7359176;
const StringHash HASH_ATTRIBUTE_INPUT_SEMANTIC = 256703331;
const StringHash HASH_ATTRIBUTE_INPUT_SET = 130685332;
const StringHash HASH_ATTRIBUTE_JOINT = 7430228;
const StringHash HASH_ATTRIBUTE_LANGUAGE = 139374837;
const StringHash HASH_ATTRIBUTE_LAYER = 7507906;
const StringHash HASH_ATTRIBUTE_LENGTH = 120344232;
const StringHash HASH_ATTRIBUTE_LEVELS = 120376371;
const StringHash HASH_ATTRIBUTE_LINK = 471115;
const StringHash HASH_ATTRIBUTE_MACROS = 121088355;
const StringHash HASH_ATTRIBUTE_MAGNITUDE = 240175317;
const StringHash HASH_ATTRIBUTE_MATERIAL = 145524812;
const StringHash HASH_ATTRIBUTE_MAXINCLUSIVE = 251940997;
const StringHash HASH_ATTRIBUTE_METER = 7588546;
const StringHash HASH_ATTRIBUTE_METHOD = 121417556;
const StringHash HASH_ATTRIBUTE_MININCLUSIVE = 259829893;
const StringHash HASH_ATTRIBUTE_MIP = 29696;
const StringHash HASH_ATTRIBUTE_MIPS_GENERATE = 4922645;
const StringHash HASH_ATTRIBUTE_MIP_INDEX = 106970824;
const StringHash HASH_ATTRIBUTE_MODE = 476581;
const StringHash HASH_ATTRIBUTE_NAME = 477237;
const StringHash HASH_ATTRIBUTE_NARGS = 7637219;
const StringHash HASH_ATTRIBUTE_NODE = 480677;
const StringHash HASH_ATTRIBUTE_OCCURRENCE = 211811637;
const StringHash HASH_ATTRIBUTE_OFFSET = 123525572;
const StringHash HASH_ATTRIBUTE_OPAQUE = 124160181;
const StringHash HASH_ATTRIBUTE_OPERAND = 107776052;
const StringHash HASH_ATTRIBUTE_OPERATOR = 113806850;
const StringHash HASH_ATTRIBUTE_OPTIONS = 108725795;
const StringHash HASH_ATTRIBUTE_ORDER = 7768770;
const StringHash HASH_ATTRIBUTE_OVERFLOW = 214488583;
const StringHash HASH_ATTRIBUTE_PARAM = 7768189;
const StringHash HASH_ATTRIBUTE_PARENT = 124292180;
const StringHash HASH_ATTRIBUTE_PASS = 485539;
const StringHash HASH_ATTRIBUTE_PLATFORM = 42652157;
const StringHash HASH_ATTRIBUTE_PLATFORMS = 145563747;
const StringHash HASH_ATTRIBUTE_POST_BEHAVIOR = 209873810;
const StringHash HASH_ATTRIBUTE_PRECISION = 195095006;
const StringHash HASH_ATTRIBUTE_PRE_BEHAVIOR = 210795490;
const StringHash HASH_ATTRIBUTE_PROFILE = 127258709;
const StringHash HASH_ATTRIBUTE_PROXY = 7837433;
const StringHash HASH_ATTRIBUTE_RANGE = 7898325;
const StringHash HASH_ATTRIBUTE_REF = 30902;
const StringHash HASH_ATTRIBUTE_RESIZABLE = 168821221;
const StringHash HASH_ATTRIBUTE_RIGID_BODY = 262281833;
const StringHash HASH_ATTRIBUTE_SAMPLER = 159675058;
const StringHash HASH_ATTRIBUTE_SCALE = 7968805;
const StringHash HASH_ATTRIBUTE_SCOPE = 7972453;
const StringHash HASH_ATTRIBUTE_SEMANTIC = 205020515;
const StringHash HASH_ATTRIBUTE_SET = 31172;
const StringHash HASH_ATTRIBUTE_SHARE = 7989381;
const StringHash HASH_ATTRIBUTE_SID = 31220;
const StringHash HASH_ATTRIBUTE_SLICE = 8007573;
const StringHash HASH_ATTRIBUTE_SOURCE = 128370837;
const StringHash HASH_ATTRIBUTE_SPACE = 8021909;
const StringHash HASH_ATTRIBUTE_STAGE = 8038357;
const StringHash HASH_ATTRIBUTE_START = 8038548;
const StringHash HASH_ATTRIBUTE_STRIDE = 128683941;
const StringHash HASH_ATTRIBUTE_STYLE = 8044581;
const StringHash HASH_ATTRIBUTE_SYMBOL = 128989532;
const StringHash HASH_ATTRIBUTE_TARGET = 128486852;
const StringHash HASH_ATTRIBUTE_TEXCOORD = 216686884;
const StringHash HASH_ATTRIBUTE_TEXTURE = 181386485;
const StringHash HASH_ATTRIBUTE_TYPE = 508005;
const StringHash HASH_ATTRIBUTE_TYPENAME = 7094773;
const StringHash HASH_ATTRIBUTE_URL = 31884;
const StringHash HASH_ATTRIBUTE_VALUE = 8160181;
const StringHash HASH_ATTRIBUTE_VERSION = 214540334;
const StringHash HASH_ATTRIBUTE_WIDTH = 8256424;
const StringHash HASH_ATTRIBUTE_XMLNS = 8340307;
const StringHash HASH_ATTRIBUTE_XREF = 522422;
const StringHash HASH_ELEMENT_ABS = 26515;
const StringHash HASH_ELEMENT_ACCELERATION = 183579774;
const StringHash HASH_ELEMENT_ACCELERATION____COMMON_FLOAT2_OR_PARAM_TYPE = 59157029;
const StringHash HASH_ELEMENT_ACCELERATION____COMMON_FLOAT_OR_PARAM_TYPE = 8635317;
const StringHash HASH_ELEMENT_ACCESSOR = 161263634;
const StringHash HASH_ELEMENT_ACTIVE = 108703941;
const StringHash HASH_ELEMENT_ALPHA = 6829793;
const StringHash HASH_ELEMENT_ALPHA_FUNC = 242440483;
const StringHash HASH_ELEMENT_ALPHA_TEST_ENABLE = 46564773;
const StringHash HASH_ELEMENT_ALPHA____GLES_TEXCOMBINER_COMMAND_ALPHA_TYPE = 42801989;
const StringHash HASH_ELEMENT_ALTITUDE = 61914405;
const StringHash HASH_ELEMENT_AMBIENT = 137952308;
const StringHash HASH_ELEMENT_AMBIENT____FX_COMMON_COLOR_OR_TEXTURE_TYPE = 236140581;
const StringHash HASH_ELEMENT_AND = 26692;
const StringHash HASH_ELEMENT_ANGLE = 6835749;
const StringHash HASH_ELEMENT_ANGULAR = 139379426;
const StringHash HASH_ELEMENT_ANGULAR_VELOCITY = 135963289;
const StringHash HASH_ELEMENT_ANIMATION = 3721230;
const StringHash HASH_ELEMENT_ANIMATION_CLIP = 21376896;
const StringHash HASH_ELEMENT_ANNOTATE = 89566757;
const StringHash HASH_ELEMENT_ANNOTATION = 112077582;
const StringHash HASH_ELEMENT_ANNOTATION_XML = 186040604;
const StringHash HASH_ELEMENT_APPLY = 6846265;
const StringHash HASH_ELEMENT_APPROX = 109541736;
const StringHash HASH_ELEMENT_ARCCOS = 109615715;
const StringHash HASH_ELEMENT_ARCCOSH = 143238904;
const StringHash HASH_ELEMENT_ARCCOT = 109615716;
const StringHash HASH_ELEMENT_ARCCOTH = 143238856;
const StringHash HASH_ELEMENT_ARCCSC = 109615763;
const StringHash HASH_ELEMENT_ARCCSCH = 143239672;
const StringHash HASH_ELEMENT_ARCSEC = 109619635;
const StringHash HASH_ELEMENT_ARCSECH = 143301624;
const StringHash HASH_ELEMENT_ARCSIN = 109619710;
const StringHash HASH_ELEMENT_ARCSINH = 143302696;
const StringHash HASH_ELEMENT_ARCTAN = 109619838;
const StringHash HASH_ELEMENT_ARCTANH = 143304744;
const StringHash HASH_ELEMENT_ARG = 26759;
const StringHash HASH_ELEMENT_ARGUMENT = 149700308;
const StringHash HASH_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENT_ALPHA_TYPE = 174473285;
const StringHash HASH_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENT_RGB_TYPE = 239244261;
const StringHash HASH_ELEMENT_ARRAY = 6854793;
const StringHash HASH_ELEMENT_ARRAY____CG_ARRAY_TYPE = 162592565;
const StringHash HASH_ELEMENT_ARRAY____GLSL_ARRAY_TYPE = 55443237;
const StringHash HASH_ELEMENT_ARTICULATED_SYSTEM = 135376637;
const StringHash HASH_ELEMENT_ASPECT_RATIO = 14868815;
const StringHash HASH_ELEMENT_ASSET = 6859204;
const StringHash HASH_ELEMENT_ATTACHMENT = 127513076;
const StringHash HASH_ELEMENT_ATTACHMENT_END = 33539060;
const StringHash HASH_ELEMENT_ATTACHMENT_FULL = 268190012;
const StringHash HASH_ELEMENT_ATTACHMENT_START = 267589988;
const StringHash HASH_ELEMENT_AUTHOR = 109883234;
const StringHash HASH_ELEMENT_AUTHORING_TOOL = 142204124;
const StringHash HASH_ELEMENT_AUTHOR_EMAIL = 47749276;
const StringHash HASH_ELEMENT_AUTHOR_WEBSITE = 18285125;
const StringHash HASH_ELEMENT_AXIS = HASH_ATTRIBUTE_AXIS;
const StringHash HASH_ELEMENT_AXIS_INFO = 9808959;
const StringHash HASH_ELEMENT_AXIS_INFO____KINEMATICS_AXIS_INFO_TYPE = 253749125;
const StringHash HASH_ELEMENT_AXIS_INFO____MOTION_AXIS_INFO_TYPE = 145904533;
const StringHash HASH_ELEMENT_AXIS____AXIS_TYPE = 97200357;
const StringHash HASH_ELEMENT_AXIS____COMMON_SIDREF_OR_PARAM_TYPE = 181522629;
const StringHash HASH_ELEMENT_AXIS____FLOAT3_TYPE = 171671765;
const StringHash HASH_ELEMENT_BACK = 427931;
const StringHash HASH_ELEMENT_BINARY = 110119065;
const StringHash HASH_ELEMENT_BINARY__HEX = 161372744;
const StringHash HASH_ELEMENT_BIND = 430148;
const StringHash HASH_ELEMENT_BIND_ATTRIBUTE = 197509765;
const StringHash HASH_ELEMENT_BIND_JOINT_AXIS = 21230595;
const StringHash HASH_ELEMENT_BIND_KINEMATICS_MODEL = 165577292;
const StringHash HASH_ELEMENT_BIND_MATERIAL = 27797804;
const StringHash HASH_ELEMENT_BIND_MATERIAL_TYPE____TECHNIQUE_COMMON = 235076158;
const StringHash HASH_ELEMENT_BIND_SHAPE_MATRIX = 80689944;
const StringHash HASH_ELEMENT_BIND_UNIFORM = 249535725;
const StringHash HASH_ELEMENT_BIND_VERTEX_INPUT = 217827748;
const StringHash HASH_ELEMENT_BIND____KINEMATICS_BIND_TYPE = 221529717;
const StringHash HASH_ELEMENT_BLEND_COLOR = 171020066;
const StringHash HASH_ELEMENT_BLEND_ENABLE = 53919109;
const StringHash HASH_ELEMENT_BLEND_EQUATION = 175901966;
const StringHash HASH_ELEMENT_BLEND_EQUATION_SEPARATE = 213461493;
const StringHash HASH_ELEMENT_BLEND_FUNC = 77810307;
const StringHash HASH_ELEMENT_BLEND_FUNC_SEPARATE = 146822149;
const StringHash HASH_ELEMENT_BLINN = 6893646;
const StringHash HASH_ELEMENT_BOOL = 431708;
const StringHash HASH_ELEMENT_BOOL2 = 6907378;
const StringHash HASH_ELEMENT_BOOL2X1 = 157678033;
const StringHash HASH_ELEMENT_BOOL2X2 = 157678034;
const StringHash HASH_ELEMENT_BOOL2X3 = 157678035;
const StringHash HASH_ELEMENT_BOOL2X4 = 157678036;
const StringHash HASH_ELEMENT_BOOL3 = 6907379;
const StringHash HASH_ELEMENT_BOOL3X1 = 157678289;
const StringHash HASH_ELEMENT_BOOL3X2 = 157678290;
const StringHash HASH_ELEMENT_BOOL3X3 = 157678291;
const StringHash HASH_ELEMENT_BOOL3X4 = 157678292;
const StringHash HASH_ELEMENT_BOOL4 = 6907380;
const StringHash HASH_ELEMENT_BOOL4X1 = 157678545;
const StringHash HASH_ELEMENT_BOOL4X2 = 157678546;
const StringHash HASH_ELEMENT_BOOL4X3 = 157678547;
const StringHash HASH_ELEMENT_BOOL4X4 = 157678548;
const StringHash HASH_ELEMENT_BOOL_ARRAY = 39718633;
const StringHash HASH_ELEMENT_BORDER_COLOR = 47245730;
const StringHash HASH_ELEMENT_BOX = 26984;
const StringHash HASH_ELEMENT_BREP = 432320;
const StringHash HASH_ELEMENT_BVAR = 433282;
const StringHash HASH_ELEMENT_BVEC2 = 6933346;
const StringHash HASH_ELEMENT_BVEC3 = 6933347;
const StringHash HASH_ELEMENT_BVEC4 = 6933348;
const StringHash HASH_ELEMENT_CAMERA = 110640257;
const StringHash HASH_ELEMENT_CAPSULE = 159886405;
const StringHash HASH_ELEMENT_CARD = 432260;
const StringHash HASH_ELEMENT_CARTESIANPRODUCT = 224054116;
const StringHash HASH_ELEMENT_CEILING = 163590183;
const StringHash HASH_ELEMENT_CG_PASS_TYPE____EVALUATE = 201526149;
const StringHash HASH_ELEMENT_CG_PASS_TYPE____PROGRAM = 168682365;
const StringHash HASH_ELEMENT_CG_PASS_TYPE____STATES = 5048947;
const StringHash HASH_ELEMENT_CHANNEL = 166221020;
const StringHash HASH_ELEMENT_CI = 1689;
const StringHash HASH_ELEMENT_CIRCLE = 111184421;
const StringHash HASH_ELEMENT_CLIP_PLANE = 107054405;
const StringHash HASH_ELEMENT_CLIP_PLANE_ENABLE = 62664181;
const StringHash HASH_ELEMENT_CN = 1694;
const StringHash HASH_ELEMENT_CODE = 435621;
const StringHash HASH_ELEMENT_CODOMAIN = 95826270;
const StringHash HASH_ELEMENT_COLLADA = 138479041;
const StringHash HASH_ELEMENT_COLOR = 6972258;
const StringHash HASH_ELEMENT_COLOR_CLEAR = 137644258;
const StringHash HASH_ELEMENT_COLOR_LOGIC_OP_ENABLE = 188045397;
const StringHash HASH_ELEMENT_COLOR_MASK = 109354667;
const StringHash HASH_ELEMENT_COLOR_MATERIAL = 244976620;
const StringHash HASH_ELEMENT_COLOR_MATERIAL_ENABLE = 203733285;
const StringHash HASH_ELEMENT_COLOR_MATERIAL__FACE = 15715221;
const StringHash HASH_ELEMENT_COLOR_MATERIAL__MODE = 15747493;
const StringHash HASH_ELEMENT_COLOR_TARGET = 965444;
const StringHash HASH_ELEMENT_COLOR____TARGETABLE_FLOAT3_TYPE = 238169205;
const StringHash HASH_ELEMENT_COMMENTS = 105104147;
const StringHash HASH_ELEMENT_COMPILER = 105317474;
const StringHash HASH_ELEMENT_COMPLEXES = 74622115;
const StringHash HASH_ELEMENT_COMPOSE = 174356213;
const StringHash HASH_ELEMENT_CONDITION = 78693950;
const StringHash HASH_ELEMENT_CONE = 435781;
const StringHash HASH_ELEMENT_CONJUGATE = 85701317;
const StringHash HASH_ELEMENT_CONNECT_PARAM = 67355581;
const StringHash HASH_ELEMENT_CONSTANT = 106603252;
const StringHash HASH_ELEMENT_CONSTANT_ATTENUATION = 96122782;
const StringHash HASH_ELEMENT_CONSTANT____GLES_TEXTURE_CONSTANT_TYPE = 9903477;
const StringHash HASH_ELEMENT_CONTRIBUTOR = 143896786;
const StringHash HASH_ELEMENT_CONTROLLER = 194286738;
const StringHash HASH_ELEMENT_CONTROL_VERTICES = 118372691;
const StringHash HASH_ELEMENT_CONVEX_MESH = 214980952;
const StringHash HASH_ELEMENT_COPYRIGHT = 134780820;
const StringHash HASH_ELEMENT_COS = 27235;
const StringHash HASH_ELEMENT_COSH = 435864;
const StringHash HASH_ELEMENT_COT = 27236;
const StringHash HASH_ELEMENT_COTH = 435880;
const StringHash HASH_ELEMENT_COVERAGE = 114065781;
const StringHash HASH_ELEMENT_CREATED = 176917204;
const StringHash HASH_ELEMENT_CREATE_2D = 193648644;
const StringHash HASH_ELEMENT_CREATE_2D__ARRAY = 234166457;
const StringHash HASH_ELEMENT_CREATE_2D__FORMAT = 254709844;
const StringHash HASH_ELEMENT_CREATE_2D__FORMAT__HINT = 125534212;
const StringHash HASH_ELEMENT_CREATE_2D__INIT_FROM = 12070029;
const StringHash HASH_ELEMENT_CREATE_3D = 193648660;
const StringHash HASH_ELEMENT_CREATE_3D__ARRAY = 234166713;
const StringHash HASH_ELEMENT_CREATE_3D__FORMAT = 254713940;
const StringHash HASH_ELEMENT_CREATE_3D__FORMAT__HINT = 125521924;
const StringHash HASH_ELEMENT_CREATE_3D__INIT_FROM = 28847245;
const StringHash HASH_ELEMENT_CREATE_3D__SIZE = 64772373;
const StringHash HASH_ELEMENT_CREATE_CUBE = 182394885;
const StringHash HASH_ELEMENT_CREATE_CUBE__ARRAY = 178952345;
const StringHash HASH_ELEMENT_CREATE_CUBE__FORMAT = 183931428;
const StringHash HASH_ELEMENT_CREATE_CUBE__FORMAT__HINT = 79920756;
const StringHash HASH_ELEMENT_CREATE_CUBE__INIT_FROM = 10450701;
const StringHash HASH_ELEMENT_CREATE_CUBE__SIZE = 27902213;
const StringHash HASH_ELEMENT_CSC = 27283;
const StringHash HASH_ELEMENT_CSCH = 436632;
const StringHash HASH_ELEMENT_CSYMBOL = 179321148;
const StringHash HASH_ELEMENT_CULL_FACE = 52800853;
const StringHash HASH_ELEMENT_CULL_FACE_ENABLE = 134131333;
const StringHash HASH_ELEMENT_CURL = 437388;
const StringHash HASH_ELEMENT_CURVE = 6998469;
const StringHash HASH_ELEMENT_CURVES = 111975619;
const StringHash HASH_ELEMENT_CYLINDER = 3165298;
const StringHash HASH_ELEMENT_CYLINDER____CYLINDER_TYPE = 34346325;
const StringHash HASH_ELEMENT_DAMPING = 176451623;
const StringHash HASH_ELEMENT_DECELERATION = 170079358;
const StringHash HASH_ELEMENT_DECELERATION____COMMON_FLOAT2_OR_PARAM_TYPE = 182889077;
const StringHash HASH_ELEMENT_DECELERATION____COMMON_FLOAT_OR_PARAM_TYPE = 205112229;
const StringHash HASH_ELEMENT_DECLARE = 179972325;
const StringHash HASH_ELEMENT_DEGREE = HASH_ATTRIBUTE_DEGREE;
const StringHash HASH_ELEMENT_DENSITY = 180723929;
const StringHash HASH_ELEMENT_DEPTH_BOUNDS = 54189651;
const StringHash HASH_ELEMENT_DEPTH_BOUNDS_ENABLE = 103390949;
const StringHash HASH_ELEMENT_DEPTH_CLAMP_ENABLE = 118122469;
const StringHash HASH_ELEMENT_DEPTH_CLEAR = 238349346;
const StringHash HASH_ELEMENT_DEPTH_FUNC = 182679603;
const StringHash HASH_ELEMENT_DEPTH_MASK = 182752491;
const StringHash HASH_ELEMENT_DEPTH_RANGE = 235062133;
const StringHash HASH_ELEMENT_DEPTH_TARGET = 325412;
const StringHash HASH_ELEMENT_DEPTH_TEST_ENABLE = 197424741;
const StringHash HASH_ELEMENT_DEST = 437412;
const StringHash HASH_ELEMENT_DEST_ALPHA = 173185601;
const StringHash HASH_ELEMENT_DEST_RGB = 212495986;
const StringHash HASH_ELEMENT_DETERMINANT = 156667540;
const StringHash HASH_ELEMENT_DIFF = 438214;
const StringHash HASH_ELEMENT_DIFFUSE = 184343797;
const StringHash HASH_ELEMENT_DIRECTION = 146457950;
const StringHash HASH_ELEMENT_DIRECTIONAL = 180710604;
const StringHash HASH_ELEMENT_DITHER_ENABLE = 248419589;
const StringHash HASH_ELEMENT_DIVERGENCE = 210441301;
const StringHash HASH_ELEMENT_DIVIDE = 112250789;
const StringHash HASH_ELEMENT_DOMAIN = 112605182;
const StringHash HASH_ELEMENT_DOMAINOFAPPLICATION = 20142414;
const StringHash HASH_ELEMENT_DRAW = 440455;
const StringHash HASH_ELEMENT_DYNAMIC = 201622419;
const StringHash HASH_ELEMENT_DYNAMIC_FRICTION = 12843982;
const StringHash HASH_ELEMENT_EDGES = 7056835;
const StringHash HASH_ELEMENT_EFFECT = 113036196;
const StringHash HASH_ELEMENT_EFFECTOR_INFO = 231300639;
const StringHash HASH_ELEMENT_ELLIPSE = 204670965;
const StringHash HASH_ELEMENT_EMISSION = 67803806;
const StringHash HASH_ELEMENT_EMPTYSET = 75239172;
const StringHash HASH_ELEMENT_ENABLED = 206017236;
const StringHash HASH_ELEMENT_ENUM = 443837;
const StringHash HASH_ELEMENT_ENUM____GLES_ENUMERATION_TYPE = 4394245;
const StringHash HASH_ELEMENT_ENUM____GL_ENUMERATION_TYPE = 98481701;
const StringHash HASH_ELEMENT_ENUM____STRING = 57204183;
const StringHash HASH_ELEMENT_EQ = 1729;
const StringHash HASH_ELEMENT_EQUATION = 146320030;
const StringHash HASH_ELEMENT_EQUIVALENT = 12772500;
const StringHash HASH_ELEMENT_EULERGAMMA = 210489361;
const StringHash HASH_ELEMENT_EVALUATE = 209960549;
const StringHash HASH_ELEMENT_EVALUATE_SCENE = 132785701;
const StringHash HASH_ELEMENT_EXACT = 7137188;
const StringHash HASH_ELEMENT_EXISTS = 114231987;
const StringHash HASH_ELEMENT_EXP = 27888;
const StringHash HASH_ELEMENT_EXPONENTIALE = 244679269;
const StringHash HASH_ELEMENT_EXTRA = 7142273;
const StringHash HASH_ELEMENT_FACE = HASH_ATTRIBUTE_FACE;
const StringHash HASH_ELEMENT_FACES = 7109059;
const StringHash HASH_ELEMENT_FACTORIAL = 179757836;
const StringHash HASH_ELEMENT_FACTOROF = 128675734;
const StringHash HASH_ELEMENT_FAIL = 444412;
const StringHash HASH_ELEMENT_FALLOFF_ANGLE = 148208005;
const StringHash HASH_ELEMENT_FALLOFF_EXPONENT = 206620580;
const StringHash HASH_ELEMENT_FALSE = 7111573;
const StringHash HASH_ELEMENT_FIXED = 7147188;
const StringHash HASH_ELEMENT_FIXED2 = 114355058;
const StringHash HASH_ELEMENT_FIXED2X1 = 15433569;
const StringHash HASH_ELEMENT_FIXED2X2 = 15433570;
const StringHash HASH_ELEMENT_FIXED2X3 = 15433571;
const StringHash HASH_ELEMENT_FIXED2X4 = 15433572;
const StringHash HASH_ELEMENT_FIXED3 = 114355059;
const StringHash HASH_ELEMENT_FIXED3X1 = 15432801;
const StringHash HASH_ELEMENT_FIXED3X2 = 15432802;
const StringHash HASH_ELEMENT_FIXED3X3 = 15432803;
const StringHash HASH_ELEMENT_FIXED3X4 = 15432804;
const StringHash HASH_ELEMENT_FIXED4 = 114355060;
const StringHash HASH_ELEMENT_FIXED4X1 = 15433057;
const StringHash HASH_ELEMENT_FIXED4X2 = 15433058;
const StringHash HASH_ELEMENT_FIXED4X3 = 15433059;
const StringHash HASH_ELEMENT_FIXED4X4 = 15433060;
const StringHash HASH_ELEMENT_FLOAT = 7157124;
const StringHash HASH_ELEMENT_FLOAT1X1 = 56131169;
const StringHash HASH_ELEMENT_FLOAT1X2 = 56131170;
const StringHash HASH_ELEMENT_FLOAT1X3 = 56131171;
const StringHash HASH_ELEMENT_FLOAT1X4 = 56131172;
const StringHash HASH_ELEMENT_FLOAT2 = 114514034;
const StringHash HASH_ELEMENT_FLOAT2X1 = 56131425;
const StringHash HASH_ELEMENT_FLOAT2X2 = 56131426;
const StringHash HASH_ELEMENT_FLOAT2X3 = 56131427;
const StringHash HASH_ELEMENT_FLOAT2X4 = 56131428;
const StringHash HASH_ELEMENT_FLOAT3 = 114514035;
const StringHash HASH_ELEMENT_FLOAT3X1 = 56130657;
const StringHash HASH_ELEMENT_FLOAT3X2 = 56130658;
const StringHash HASH_ELEMENT_FLOAT3X3 = 56130659;
const StringHash HASH_ELEMENT_FLOAT3X4 = 56130660;
const StringHash HASH_ELEMENT_FLOAT4 = 114514036;
const StringHash HASH_ELEMENT_FLOAT4X1 = 56130913;
const StringHash HASH_ELEMENT_FLOAT4X2 = 56130914;
const StringHash HASH_ELEMENT_FLOAT4X3 = 56130915;
const StringHash HASH_ELEMENT_FLOAT4X4 = 56130916;
const StringHash HASH_ELEMENT_FLOAT_ARRAY = 171289865;
const StringHash HASH_ELEMENT_FLOAT____FLOAT_TYPE = 36108549;
const StringHash HASH_ELEMENT_FLOOR = 7157346;
const StringHash HASH_ELEMENT_FOCAL = 7166332;
const StringHash HASH_ELEMENT_FOG_COLOR = 224022578;
const StringHash HASH_ELEMENT_FOG_COORD_SRC = 198521027;
const StringHash HASH_ELEMENT_FOG_DENSITY = 183033321;
const StringHash HASH_ELEMENT_FOG_ENABLE = 96644853;
const StringHash HASH_ELEMENT_FOG_END = 224222244;
const StringHash HASH_ELEMENT_FOG_MODE = 97928053;
const StringHash HASH_ELEMENT_FOG_START = 225101252;
const StringHash HASH_ELEMENT_FORALL = 114722860;
const StringHash HASH_ELEMENT_FORCE_FIELD = 187798708;
const StringHash HASH_ELEMENT_FORMAT = HASH_ATTRIBUTE_FORMAT;
const StringHash HASH_ELEMENT_FORMULA = 225004609;
const StringHash HASH_ELEMENT_FRAME_OBJECT = 75597844;
const StringHash HASH_ELEMENT_FRAME_ORIGIN = 78675534;
const StringHash HASH_ELEMENT_FRAME_TCP = 138086176;
const StringHash HASH_ELEMENT_FRAME_TIP = 138086528;
const StringHash HASH_ELEMENT_FRONT = 7181908;
const StringHash HASH_ELEMENT_FRONT_FACE = 94576373;
const StringHash HASH_ELEMENT_FUNC = 449603;
const StringHash HASH_ELEMENT_FX_COLORTARGET_TYPE____PARAM = 260596893;
const StringHash HASH_ELEMENT_FX_COMMON_COLOR_OR_TEXTURE_TYPE____COLOR = 143195282;
const StringHash HASH_ELEMENT_FX_COMMON_COLOR_OR_TEXTURE_TYPE____PARAM = 146289037;
const StringHash HASH_ELEMENT_FX_COMMON_FLOAT_OR_PARAM_TYPE____FLOAT = 254415476;
const StringHash HASH_ELEMENT_FX_COMMON_FLOAT_OR_PARAM_TYPE____PARAM = 252948365;
const StringHash HASH_ELEMENT_GCD = 28052;
const StringHash HASH_ELEMENT_GEOGRAPHIC_LOCATION = 246600334;
const StringHash HASH_ELEMENT_GEOMETRY = 207867209;
const StringHash HASH_ELEMENT_GEQ = 28097;
const StringHash HASH_ELEMENT_GLES2_PASS_TYPE____EVALUATE = 29894053;
const StringHash HASH_ELEMENT_GLES2_PASS_TYPE____STATES = 241647955;
const StringHash HASH_ELEMENT_GLES2_PROGRAM_TYPE____BIND_ATTRIBUTE = 106646005;
const StringHash HASH_ELEMENT_GLES2_PROGRAM_TYPE____BIND_UNIFORM = 128066157;
const StringHash HASH_ELEMENT_GLES2_SHADER_TYPE____SOURCES = 76669971;
const StringHash HASH_ELEMENT_GLSL_PROGRAM_TYPE____BIND_ATTRIBUTE = 217019733;
const StringHash HASH_ELEMENT_GLSL_PROGRAM_TYPE____BIND_UNIFORM = 185120349;
const StringHash HASH_ELEMENT_GRAD = 452724;
const StringHash HASH_ELEMENT_GRAVITY = 243847385;
const StringHash HASH_ELEMENT_GT = 1764;
const StringHash HASH_ELEMENT_H = 104;
const StringHash HASH_ELEMENT_HALF = 452646;
const StringHash HASH_ELEMENT_HALF2 = 7242386;
const StringHash HASH_ELEMENT_HALF2X1 = 243440081;
const StringHash HASH_ELEMENT_HALF2X2 = 243440082;
const StringHash HASH_ELEMENT_HALF2X3 = 243440083;
const StringHash HASH_ELEMENT_HALF2X4 = 243440084;
const StringHash HASH_ELEMENT_HALF3 = 7242387;
const StringHash HASH_ELEMENT_HALF3X1 = 243440337;
const StringHash HASH_ELEMENT_HALF3X2 = 243440338;
const StringHash HASH_ELEMENT_HALF3X3 = 243440339;
const StringHash HASH_ELEMENT_HALF3X4 = 243440340;
const StringHash HASH_ELEMENT_HALF4 = 7242388;
const StringHash HASH_ELEMENT_HALF4X1 = 243440593;
const StringHash HASH_ELEMENT_HALF4X2 = 243440594;
const StringHash HASH_ELEMENT_HALF4X3 = 243440595;
const StringHash HASH_ELEMENT_HALF4X4 = 243440596;
const StringHash HASH_ELEMENT_HALF_EXTENTS = 168995299;
const StringHash HASH_ELEMENT_HEIGHT = HASH_ATTRIBUTE_HEIGHT;
const StringHash HASH_ELEMENT_HEX = 28360;
const StringHash HASH_ELEMENT_HINT = 454740;
const StringHash HASH_ELEMENT_HOLLOW = 116798311;
const StringHash HASH_ELEMENT_HYPERBOLA = 113837601;
const StringHash HASH_ELEMENT_IDENT = 7318612;
const StringHash HASH_ELEMENT_IDREF_ARRAY = 202706457;
const StringHash HASH_ELEMENT_IMAGE = 7354325;
const StringHash HASH_ELEMENT_IMAGER = 117669314;
const StringHash HASH_ELEMENT_IMAGE_TYPE____INIT_FROM = 17610941;
const StringHash HASH_ELEMENT_IMAGE____FUNCTIONS_TYPE = 77179909;
const StringHash HASH_ELEMENT_IMAGE____IMAGE_TYPE = 137664597;
const StringHash HASH_ELEMENT_IMAGINARY = 132135081;
const StringHash HASH_ELEMENT_IMAGINARYI = 235113097;
const StringHash HASH_ELEMENT_IMPLIES = 4665267;
const StringHash HASH_ELEMENT_IMPORT = 117733012;
const StringHash HASH_ELEMENT_IN = 1790;
const StringHash HASH_ELEMENT_INCLUDE = 4864981;
const StringHash HASH_ELEMENT_INDEX = HASH_ATTRIBUTE_INDEX;
const StringHash HASH_ELEMENT_INDEX_OF_REFRACTION = 242674622;
const StringHash HASH_ELEMENT_INERTIA = 5020289;
const StringHash HASH_ELEMENT_INFINITY = 80762809;
const StringHash HASH_ELEMENT_INIT_FROM = 10856717;
const StringHash HASH_ELEMENT_INLINE = 117780549;
const StringHash HASH_ELEMENT_INPUT = 7362500;
const StringHash HASH_ELEMENT_INPUT____INPUT_LOCAL_OFFSET_TYPE = 42264325;
const StringHash HASH_ELEMENT_INPUT____INPUT_LOCAL_TYPE = 267311813;
const StringHash HASH_ELEMENT_INSTANCE_ANIMATION = 228086430;
const StringHash HASH_ELEMENT_INSTANCE_ARTICULATED_SYSTEM = 62493949;
const StringHash HASH_ELEMENT_INSTANCE_CAMERA = 255854209;
const StringHash HASH_ELEMENT_INSTANCE_CONTROLLER = 26967202;
const StringHash HASH_ELEMENT_INSTANCE_EFFECT = 253030820;
const StringHash HASH_ELEMENT_INSTANCE_EFFECT_TYPE____SETPARAM = 234607005;
const StringHash HASH_ELEMENT_INSTANCE_FORCE_FIELD = 195760404;
const StringHash HASH_ELEMENT_INSTANCE_FORMULA = 132967633;
const StringHash HASH_ELEMENT_INSTANCE_GEOMETRY = 75089129;
const StringHash HASH_ELEMENT_INSTANCE_IMAGE = 16168053;
const StringHash HASH_ELEMENT_INSTANCE_JOINT = 17554932;
const StringHash HASH_ELEMENT_INSTANCE_KINEMATICS_MODEL = 193744236;
const StringHash HASH_ELEMENT_INSTANCE_KINEMATICS_SCENE = 188845461;
const StringHash HASH_ELEMENT_INSTANCE_LIGHT = 17333844;
const StringHash HASH_ELEMENT_INSTANCE_MATERIAL = 9871340;
const StringHash HASH_ELEMENT_INSTANCE_MATERIAL_TYPE____BIND = 213654068;
const StringHash HASH_ELEMENT_INSTANCE_MATERIAL____INSTANCE_MATERIAL_TYPE = 239273653;
const StringHash HASH_ELEMENT_INSTANCE_NODE = 168885653;
const StringHash HASH_ELEMENT_INSTANCE_PHYSICS_MATERIAL = 190501244;
const StringHash HASH_ELEMENT_INSTANCE_PHYSICS_MODEL = 98657756;
const StringHash HASH_ELEMENT_INSTANCE_PHYSICS_SCENE = 99263781;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY = 95007321;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY_TYPE____TECHNIQUE_COMMON = 223958254;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC = 15147027;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME = 258084517;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE = 64146181;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW = 121584071;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_CONSTRAINT = 252820964;
const StringHash HASH_ELEMENT_INSTANCE_VISUAL_SCENE = 235998149;
const StringHash HASH_ELEMENT_INT = 28756;
const StringHash HASH_ELEMENT_INT2 = 460146;
const StringHash HASH_ELEMENT_INT2X1 = 117799345;
const StringHash HASH_ELEMENT_INT2X2 = 117799346;
const StringHash HASH_ELEMENT_INT2X3 = 117799347;
const StringHash HASH_ELEMENT_INT2X4 = 117799348;
const StringHash HASH_ELEMENT_INT3 = 460147;
const StringHash HASH_ELEMENT_INT3X1 = 117799601;
const StringHash HASH_ELEMENT_INT3X2 = 117799602;
const StringHash HASH_ELEMENT_INT3X3 = 117799603;
const StringHash HASH_ELEMENT_INT3X4 = 117799604;
const StringHash HASH_ELEMENT_INT4 = 460148;
const StringHash HASH_ELEMENT_INT4X1 = 117799857;
const StringHash HASH_ELEMENT_INT4X2 = 117799858;
const StringHash HASH_ELEMENT_INT4X3 = 117799859;
const StringHash HASH_ELEMENT_INT4X4 = 117799860;
const StringHash HASH_ELEMENT_INTEGERS = 95148947;
const StringHash HASH_ELEMENT_INTERPENETRATE = 23533925;
const StringHash HASH_ELEMENT_INTERSECT = 181005300;
const StringHash HASH_ELEMENT_INTERVAL = 95198076;
const StringHash HASH_ELEMENT_INT_ARRAY = 173598937;
const StringHash HASH_ELEMENT_INT____INT_TYPE = 265553365;
const StringHash HASH_ELEMENT_INT____INT_TYPE____MATHML = 32378988;
const StringHash HASH_ELEMENT_INVERSE = 6080997;
const StringHash HASH_ELEMENT_INVERT = 117820564;
const StringHash HASH_ELEMENT_IVEC2 = 7392098;
const StringHash HASH_ELEMENT_IVEC3 = 7392099;
const StringHash HASH_ELEMENT_IVEC4 = 7392100;
const StringHash HASH_ELEMENT_JERK = 461963;
const StringHash HASH_ELEMENT_JERK____COMMON_FLOAT2_OR_PARAM_TYPE = 145305061;
const StringHash HASH_ELEMENT_JERK____COMMON_FLOAT_OR_PARAM_TYPE = 203250629;
const StringHash HASH_ELEMENT_JOINT = HASH_ATTRIBUTE_JOINT;
const StringHash HASH_ELEMENT_JOINTS = 118883763;
const StringHash HASH_ELEMENT_KEYWORDS = 219049891;
const StringHash HASH_ELEMENT_KINEMATICS = 205492195;
const StringHash HASH_ELEMENT_KINEMATICS_MODEL = 92132700;
const StringHash HASH_ELEMENT_KINEMATICS_SCENE = 89527717;
const StringHash HASH_ELEMENT_LAMBDA = 120076449;
const StringHash HASH_ELEMENT_LAMBERT = 42175716;
const StringHash HASH_ELEMENT_LAPLACIAN = 120057342;
const StringHash HASH_ELEMENT_LATITUDE = 145800325;
const StringHash HASH_ELEMENT_LAYER = HASH_ATTRIBUTE_LAYER;
const StringHash HASH_ELEMENT_LCM = 29341;
const StringHash HASH_ELEMENT_LEQ = 29377;
const StringHash HASH_ELEMENT_LIBRARY_ANIMATIONS = 223353555;
const StringHash HASH_ELEMENT_LIBRARY_ANIMATION_CLIPS = 210579923;
const StringHash HASH_ELEMENT_LIBRARY_ARTICULATED_SYSTEMS = 177341699;
const StringHash HASH_ELEMENT_LIBRARY_CAMERAS = 17507619;
const StringHash HASH_ELEMENT_LIBRARY_CONTROLLERS = 117752259;
const StringHash HASH_ELEMENT_LIBRARY_EFFECTS = 38033171;
const StringHash HASH_ELEMENT_LIBRARY_FORCE_FIELDS = 262260019;
const StringHash HASH_ELEMENT_LIBRARY_FORMULAS = 236615907;
const StringHash HASH_ELEMENT_LIBRARY_GEOMETRIES = 219269923;
const StringHash HASH_ELEMENT_LIBRARY_IMAGES = 175895315;
const StringHash HASH_ELEMENT_LIBRARY_IMAGES__IMAGE__INIT_FROM__HEX = 131380824;
const StringHash HASH_ELEMENT_LIBRARY_JOINTS = 195922787;
const StringHash HASH_ELEMENT_LIBRARY_KINEMATICS_MODELS = 202890947;
const StringHash HASH_ELEMENT_LIBRARY_KINEMATICS_SCENES = 145478195;
const StringHash HASH_ELEMENT_LIBRARY_LIGHTS = 196563299;
const StringHash HASH_ELEMENT_LIBRARY_MATERIALS = 35999283;
const StringHash HASH_ELEMENT_LIBRARY_NODES = 230609443;
const StringHash HASH_ELEMENT_LIBRARY_PHYSICS_MATERIALS = 149953987;
const StringHash HASH_ELEMENT_LIBRARY_PHYSICS_MODELS = 247630259;
const StringHash HASH_ELEMENT_LIBRARY_PHYSICS_SCENES = 236889923;
const StringHash HASH_ELEMENT_LIBRARY_VISUAL_SCENES = 1834835;
const StringHash HASH_ELEMENT_LIGHT = 7536116;
const StringHash HASH_ELEMENT_LIGHTING_ENABLE = 140439397;
const StringHash HASH_ELEMENT_LIGHT_AMBIENT = 113574516;
const StringHash HASH_ELEMENT_LIGHT_CONSTANT_ATTENUATION = 107287662;
const StringHash HASH_ELEMENT_LIGHT_DIFFUSE = 134293173;
const StringHash HASH_ELEMENT_LIGHT_ENABLE = 44804229;
const StringHash HASH_ELEMENT_LIGHT_LINEAR_ATTENUATION = 14779134;
const StringHash HASH_ELEMENT_LIGHT_MODEL_AMBIENT = 111126580;
const StringHash HASH_ELEMENT_LIGHT_MODEL_COLOR_CONTROL = 90408300;
const StringHash HASH_ELEMENT_LIGHT_MODEL_LOCAL_VIEWER_ENABLE = 147233621;
const StringHash HASH_ELEMENT_LIGHT_MODEL_TWO_SIDE_ENABLE = 26336277;
const StringHash HASH_ELEMENT_LIGHT_POSITION = 157224718;
const StringHash HASH_ELEMENT_LIGHT_QUADRATIC_ATTENUATION = 111314510;
const StringHash HASH_ELEMENT_LIGHT_SPECULAR = 157623042;
const StringHash HASH_ELEMENT_LIGHT_SPOT_CUTOFF = 263462582;
const StringHash HASH_ELEMENT_LIGHT_SPOT_DIRECTION = 34428126;
const StringHash HASH_ELEMENT_LIGHT_SPOT_EXPONENT = 132068740;
const StringHash HASH_ELEMENT_LIGHT_TYPE____TECHNIQUE_COMMON = 208516750;
const StringHash HASH_ELEMENT_LIGHT__TECHNIQUE_COMMON__AMBIENT = 163690020;
const StringHash HASH_ELEMENT_LIMIT = 7537668;
const StringHash HASH_ELEMENT_LIMITS = 120602803;
const StringHash HASH_ELEMENT_LIMITS____JOINT_LIMITS_TYPE = 47783013;
const StringHash HASH_ELEMENT_LIMITS____KINEMATICS_LIMITS_TYPE = 52071685;
const StringHash HASH_ELEMENT_LINE = 471109;
const StringHash HASH_ELEMENT_LINEAR = 120605570;
const StringHash HASH_ELEMENT_LINEAR_ATTENUATION = 80764702;
const StringHash HASH_ELEMENT_LINES = 7537859;
const StringHash HASH_ELEMENT_LINESTRIPS = 212647987;
const StringHash HASH_ELEMENT_LINE_SMOOTH_ENABLE = 179460837;
const StringHash HASH_ELEMENT_LINE_STIPPLE = 228605509;
const StringHash HASH_ELEMENT_LINE_STIPPLE_ENABLE = 264740261;
const StringHash HASH_ELEMENT_LINE_WIDTH = 191548392;
const StringHash HASH_ELEMENT_LINK = HASH_ATTRIBUTE_LINK;
const StringHash HASH_ELEMENT_LINKER = 120607170;
const StringHash HASH_ELEMENT_LIST = 471204;
const StringHash HASH_ELEMENT_LN = 1838;
const StringHash HASH_ELEMENT_LOCKED = 120955316;
const StringHash HASH_ELEMENT_LOG = 29527;
const StringHash HASH_ELEMENT_LOGBASE = 56461541;
const StringHash HASH_ELEMENT_LOGIC_OP = 98538320;
const StringHash HASH_ELEMENT_LOGIC_OP_ENABLE = 140332597;
const StringHash HASH_ELEMENT_LONGITUDE = 81840325;
const StringHash HASH_ELEMENT_LOOKAT = 121004420;
const StringHash HASH_ELEMENT_LOWLIMIT = 115541300;
const StringHash HASH_ELEMENT_LT = 1844;
const StringHash HASH_ELEMENT_MAGFILTER = 231754162;
const StringHash HASH_ELEMENT_MASK = 473243;
const StringHash HASH_ELEMENT_MASS = 473251;
const StringHash HASH_ELEMENT_MASS_FRAME = 156942485;
const StringHash HASH_ELEMENT_MAT2 = 473202;
const StringHash HASH_ELEMENT_MAT3 = 473203;
const StringHash HASH_ELEMENT_MAT4 = 473204;
const StringHash HASH_ELEMENT_MATERIAL = HASH_ATTRIBUTE_MATERIAL;
const StringHash HASH_ELEMENT_MATERIAL_AMBIENT = 212902564;
const StringHash HASH_ELEMENT_MATERIAL_DIFFUSE = 108299877;
const StringHash HASH_ELEMENT_MATERIAL_EMISSION = 210711006;
const StringHash HASH_ELEMENT_MATERIAL_SHININESS = 135694787;
const StringHash HASH_ELEMENT_MATERIAL_SPECULAR = 239021794;
const StringHash HASH_ELEMENT_MATH = 473256;
const StringHash HASH_ELEMENT_MATRIX = 121157896;
const StringHash HASH_ELEMENT_MATRIXROW = 194022631;
const StringHash HASH_ELEMENT_MATRIX____MATRIX_TYPE = 1413909;
const StringHash HASH_ELEMENT_MATRIX____MATRIX_TYPE____MATHML = 119786972;
const StringHash HASH_ELEMENT_MAX = 29576;
const StringHash HASH_ELEMENT_MAX_ANISOTROPY = 42865225;
const StringHash HASH_ELEMENT_MAX____ARITH_TYPE = 262588213;
const StringHash HASH_ELEMENT_MAX____COMMON_FLOAT_OR_PARAM_TYPE = 17987797;
const StringHash HASH_ELEMENT_MAX____MINMAX_TYPE = 213754373;
const StringHash HASH_ELEMENT_MAX____TARGETABLE_FLOAT3_TYPE = 73550357;
const StringHash HASH_ELEMENT_MEAN = 473982;
const StringHash HASH_ELEMENT_MEDIAN = 121352062;
const StringHash HASH_ELEMENT_MESH = 474264;
const StringHash HASH_ELEMENT_MIN = 29694;
const StringHash HASH_ELEMENT_MINFILTER = 80760514;
const StringHash HASH_ELEMENT_MINUS = 7603651;
const StringHash HASH_ELEMENT_MIN____ARITH_TYPE = 262229301;
const StringHash HASH_ELEMENT_MIN____COMMON_FLOAT_OR_PARAM_TYPE = 252871509;
const StringHash HASH_ELEMENT_MIN____MINMAX_TYPE = 218464773;
const StringHash HASH_ELEMENT_MIN____TARGETABLE_FLOAT3_TYPE = 73449493;
const StringHash HASH_ELEMENT_MIPFILTER = 114314946;
const StringHash HASH_ELEMENT_MIPS = 475251;
const StringHash HASH_ELEMENT_MIP_BIAS = 6654147;
const StringHash HASH_ELEMENT_MIP_MAX_LEVEL = 174370636;
const StringHash HASH_ELEMENT_MIP_MIN_LEVEL = 140816172;
const StringHash HASH_ELEMENT_MODE = HASH_ATTRIBUTE_MODE;
const StringHash HASH_ELEMENT_MODEL_VIEW_MATRIX = 136322840;
const StringHash HASH_ELEMENT_MODE____MODE_TYPE = 267893381;
const StringHash HASH_ELEMENT_MODIFIED = 95406324;
const StringHash HASH_ELEMENT_MODIFIER = 95406210;
const StringHash HASH_ELEMENT_MOMENT = 122043476;
const StringHash HASH_ELEMENT_MOMENTABOUT = 97776900;
const StringHash HASH_ELEMENT_MORPH = 7629160;
const StringHash HASH_ELEMENT_MOTION = 122073182;
const StringHash HASH_ELEMENT_MULTISAMPLE_ENABLE = 202117781;
const StringHash HASH_ELEMENT_NAME_ARRAY = 190697657;
const StringHash HASH_ELEMENT_NATURALNUMBERS = 220138979;
const StringHash HASH_ELEMENT_NEQ = 29889;
const StringHash HASH_ELEMENT_NEWPARAM = 216436541;
const StringHash HASH_ELEMENT_NEWPARAM____CG_NEWPARAM_TYPE = 21109781;
const StringHash HASH_ELEMENT_NEWPARAM____FORMULA_NEWPARAM_TYPE = 145709109;
const StringHash HASH_ELEMENT_NEWPARAM____FX_COMMON_NEWPARAM_TYPE = 190035477;
const StringHash HASH_ELEMENT_NEWPARAM____FX_NEWPARAM_TYPE = 21125909;
const StringHash HASH_ELEMENT_NEWPARAM____GLES_NEWPARAM_TYPE = 173388917;
const StringHash HASH_ELEMENT_NEWPARAM____GLSL_NEWPARAM_TYPE = 173341557;
const StringHash HASH_ELEMENT_NEWPARAM____KINEMATICS_NEWPARAM_TYPE = 19307045;
const StringHash HASH_ELEMENT_NODE = HASH_ATTRIBUTE_NODE;
const StringHash HASH_ELEMENT_NORMALIZE_ENABLE = 104996709;
const StringHash HASH_ELEMENT_NOT = 30052;
const StringHash HASH_ELEMENT_NOTANUMBER = 140340834;
const StringHash HASH_ELEMENT_NOTIN = 7695102;
const StringHash HASH_ELEMENT_NOTPRSUBSET = 165606068;
const StringHash HASH_ELEMENT_NOTSUBSET = 195816612;
const StringHash HASH_ELEMENT_NURBS = 7719059;
const StringHash HASH_ELEMENT_NURBS_SURFACE = 251682437;
const StringHash HASH_ELEMENT_NURBS_SURFACE_TYPE____CONTROL_VERTICES = 79476835;
const StringHash HASH_ELEMENT_NURBS_TYPE____CONTROL_VERTICES = 29842867;
const StringHash HASH_ELEMENT_OPTICS = 124235683;
const StringHash HASH_ELEMENT_OPTICS__TECHNIQUE_COMMON = 170249678;
const StringHash HASH_ELEMENT_OR = 1890;
const StringHash HASH_ELEMENT_ORIENT = 124320852;
const StringHash HASH_ELEMENT_ORIGIN = 124321278;
const StringHash HASH_ELEMENT_ORIGIN____FLOAT3_TYPE = 189482549;
const StringHash HASH_ELEMENT_ORIGIN____ORIGIN_TYPE = 83917925;
const StringHash HASH_ELEMENT_ORTHOGRAPHIC = 165790115;
const StringHash HASH_ELEMENT_OTHERWISE = 248100405;
const StringHash HASH_ELEMENT_OUTERPRODUCT = 16324404;
const StringHash HASH_ELEMENT_P = 112;
const StringHash HASH_ELEMENT_PARABOLA = 143102785;
const StringHash HASH_ELEMENT_PARAM = HASH_ATTRIBUTE_PARAM;
const StringHash HASH_ELEMENT_PARAM____COMMON_PARAM_TYPE = 266514677;
const StringHash HASH_ELEMENT_PARAM____KINEMATICS_PARAM_TYPE = 236899269;
const StringHash HASH_ELEMENT_PARAM____NCNAME = 212141541;
const StringHash HASH_ELEMENT_PARAM____PARAM_TYPE = 101215141;
const StringHash HASH_ELEMENT_PARTIALDIFF = 252978790;
const StringHash HASH_ELEMENT_PASS = HASH_ATTRIBUTE_PASS;
const StringHash HASH_ELEMENT_PASS____CG_PASS_TYPE = 130061013;
const StringHash HASH_ELEMENT_PASS____GLES2_PASS_TYPE = 62070357;
const StringHash HASH_ELEMENT_PCURVES = 111975603;
const StringHash HASH_ELEMENT_PERSPECTIVE = 114063717;
const StringHash HASH_ELEMENT_PH = 1896;
const StringHash HASH_ELEMENT_PHONG = 7796295;
const StringHash HASH_ELEMENT_PHYSICS_MATERIAL = 22186316;
const StringHash HASH_ELEMENT_PHYSICS_MATERIAL_TYPE____TECHNIQUE_COMMON = 40576062;
const StringHash HASH_ELEMENT_PHYSICS_MODEL = 6604124;
const StringHash HASH_ELEMENT_PHYSICS_SCENE = 6882725;
const StringHash HASH_ELEMENT_PHYSICS_SCENE_TYPE____TECHNIQUE_COMMON = 156512798;
const StringHash HASH_ELEMENT_PI = 1897;
const StringHash HASH_ELEMENT_PIECE = 7797653;
const StringHash HASH_ELEMENT_PIECEWISE = 194819685;
const StringHash HASH_ELEMENT_PLANE = 7809093;
const StringHash HASH_ELEMENT_PLUS = 488387;
const StringHash HASH_ELEMENT_POINT = 7823444;
const StringHash HASH_ELEMENT_POINT_DISTANCE_ATTENUATION = 139457678;
const StringHash HASH_ELEMENT_POINT_FADE_THRESHOLD_SIZE = 101782757;
const StringHash HASH_ELEMENT_POINT_SIZE = 94500613;
const StringHash HASH_ELEMENT_POINT_SIZE_ENABLE = 263916069;
const StringHash HASH_ELEMENT_POINT_SIZE_MAX = 118555256;
const StringHash HASH_ELEMENT_POINT_SIZE_MIN = 118555150;
const StringHash HASH_ELEMENT_POINT_SMOOTH_ENABLE = 182602229;
const StringHash HASH_ELEMENT_POLYGONS = 104850211;
const StringHash HASH_ELEMENT_POLYGON_MODE = 55843397;
const StringHash HASH_ELEMENT_POLYGON_MODE__FACE = 169991637;
const StringHash HASH_ELEMENT_POLYGON_MODE__MODE = 170016741;
const StringHash HASH_ELEMENT_POLYGON_OFFSET = 73611924;
const StringHash HASH_ELEMENT_POLYGON_OFFSET_FILL_ENABLE = 194749109;
const StringHash HASH_ELEMENT_POLYGON_OFFSET_LINE_ENABLE = 201697125;
const StringHash HASH_ELEMENT_POLYGON_OFFSET_POINT_ENABLE = 98702757;
const StringHash HASH_ELEMENT_POLYGON_SMOOTH_ENABLE = 242755781;
const StringHash HASH_ELEMENT_POLYGON_STIPPLE_ENABLE = 56638309;
const StringHash HASH_ELEMENT_POLYLIST = 104871892;
const StringHash HASH_ELEMENT_POWER = 7826882;
const StringHash HASH_ELEMENT_PRIMES = 125371331;
const StringHash HASH_ELEMENT_PRISMATIC = 10747235;
const StringHash HASH_ELEMENT_PRODUCT = 127253460;
const StringHash HASH_ELEMENT_PROFILE_BRIDGE = 170646869;
const StringHash HASH_ELEMENT_PROFILE_CG = 218491431;
const StringHash HASH_ELEMENT_PROFILE_CG_TYPE____TECHNIQUE = 250185845;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER = 63511394;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER__BIND_UNIFORM = 148568957;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER__BIND_UNIFORM__PARAM = 180957389;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__PROGRAM__SHADER__SOURCES = 165335091;
const StringHash HASH_ELEMENT_PROFILE_COMMON = 171910622;
const StringHash HASH_ELEMENT_PROFILE_COMMON_TYPE____TECHNIQUE = 268247941;
const StringHash HASH_ELEMENT_PROFILE_COMMON__TECHNIQUE__CONSTANT = 100962372;
const StringHash HASH_ELEMENT_PROFILE_GLES = 99286435;
const StringHash HASH_ELEMENT_PROFILE_GLES2 = 246405682;
const StringHash HASH_ELEMENT_PROFILE_GLES2_TYPE____NEWPARAM = 102302621;
const StringHash HASH_ELEMENT_PROFILE_GLES2_TYPE____TECHNIQUE = 103651493;
const StringHash HASH_ELEMENT_PROFILE_GLES2__NEWPARAM__USERTYPE = 171715285;
const StringHash HASH_ELEMENT_PROFILE_GLES2__NEWPARAM__USERTYPE__SETPARAM = 107414221;
const StringHash HASH_ELEMENT_PROFILE_GLES2__NEWPARAM__USERTYPE__SETPARAM__ARRAY = 52999641;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__PROGRAM__BIND_UNIFORM__PARAM = 226722957;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_COLOR = 215581810;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_ENABLE = 230058725;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION = 178873230;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE = 173089957;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__ALPHA = 38173937;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__RGB = 168983186;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC = 30263891;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE = 13443813;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_ALPHA = 64010401;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_RGB = 239333458;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_ALPHA = 133140033;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_RGB = 145215842;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC__DEST = 177759988;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__BLEND_FUNC__SRC = 95008291;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__COLOR_MASK = 57548347;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__CULL_FACE = 173491541;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__CULL_FACE_ENABLE = 13643317;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_FUNC = 915603;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_MASK = 922699;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_RANGE = 15387093;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DEPTH_TEST_ENABLE = 90996869;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__DITHER_ENABLE = 113625125;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__FRONT_FACE = 13475429;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__LINE_WIDTH = 4475560;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__POINT_SIZE = 13465173;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__POLYGON_OFFSET = 81253908;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__POLYGON_OFFSET_FILL_ENABLE = 189746741;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 181763717;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SAMPLE_COVERAGE_ENABLE = 227428453;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SCISSOR = 28048594;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__SCISSOR_TEST_ENABLE = 42291061;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC = 89115635;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE = 14117573;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__BACK = 195226971;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__FRONT = 170605284;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__MASK = 195252827;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__REF = 213523990;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC__FUNC = 236589491;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC__MASK = 236712811;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_FUNC__REF = 266441734;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK = 89139499;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE = 251390773;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__FACE = 97397925;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__MASK = 97506219;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP = 28659824;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE = 158324069;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FACE = 70914805;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FAIL = 70914716;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZFAIL = 61706684;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZPASS = 61619939;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP__FAIL = 100702092;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP__ZFAIL = 4571292;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_OP__ZPASS = 4485059;
const StringHash HASH_ELEMENT_PROFILE_GLES2__TECHNIQUE__PASS__STATES__STENCIL_TEST_ENABLE = 26192629;
const StringHash HASH_ELEMENT_PROFILE_GLES_TYPE____TECHNIQUE = 38468469;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS = 175547763;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__EVALUATE = 152107973;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES = 1046915;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_FUNC = 122634915;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_FUNC__FUNC = 1743603;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_FUNC__VALUE = 26765493;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__ALPHA_TEST_ENABLE = 25632213;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_ENABLE = 256849653;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_FUNC = 232739075;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_FUNC__DEST = 111869348;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__BLEND_FUNC__SRC = 6987667;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CLIP_PLANE = 222125253;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CLIP_PLANE_ENABLE = 108115365;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__COLOR_LOGIC_OP_ENABLE = 63510981;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__COLOR_MASK = 255870251;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__COLOR_MATERIAL_ENABLE = 77106549;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CULL_FACE = 194011813;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__CULL_FACE_ENABLE = 214061829;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_FUNC = 60788659;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_MASK = 60845419;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_RANGE = 164445157;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DEPTH_TEST_ENABLE = 210497173;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__DITHER_ENABLE = 53871301;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_COLOR = 97388450;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_DENSITY = 253846905;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_ENABLE = 217986421;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_END = 80072676;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_MODE = 207454181;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FOG_START = 100600916;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__FRONT_FACE = 215999861;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHTING_ENABLE = 15409813;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_AMBIENT = 190683060;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_CONSTANT_ATTENUATION = 200044702;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_DIFFUSE = 127512949;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_ENABLE = 242364405;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_LINEAR_ATTENUATION = 203033006;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_MODEL_AMBIENT = 151459828;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_MODEL_TWO_SIDE_ENABLE = 155195205;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_POSITION = 14054302;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_QUADRATIC_ATTENUATION = 238373886;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPECULAR = 15499250;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPOT_CUTOFF = 142328262;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPOT_DIRECTION = 167389230;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LIGHT_SPOT_EXPONENT = 142754884;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LINE_SMOOTH_ENABLE = 106420181;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LINE_WIDTH = 70659176;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__LOGIC_OP = 207024000;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_AMBIENT = 65813284;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_DIFFUSE = 254287333;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_EMISSION = 256281006;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_SHININESS = 210698387;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MATERIAL_SPECULAR = 200717938;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MODEL_VIEW_MATRIX = 32081928;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__MULTISAMPLE_ENABLE = 125018149;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__NORMALIZE_ENABLE = 262056165;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_DISTANCE_ATTENUATION = 46575134;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_FADE_THRESHOLD_SIZE = 196642629;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SIZE = 215960965;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SIZE_MAX = 226991304;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SIZE_MIN = 226991294;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POINT_SMOOTH_ENABLE = 13295637;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POLYGON_OFFSET = 166347876;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__POLYGON_OFFSET_FILL_ENABLE = 68337829;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__PROJECTION_MATRIX = 202003896;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__RESCALE_NORMAL_ENABLE = 65598261;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 205993509;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_ONE_ENABLE = 17867045;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SAMPLE_COVERAGE_ENABLE = 152580869;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SCISSOR = 102252018;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SCISSOR_TEST_ENABLE = 131502165;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__SHADE_MODEL = 14034428;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC = 123115395;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC__FUNC = 2313683;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC__MASK = 2404107;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_FUNC__REF = 151126470;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_MASK = 123172187;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP = 231167776;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP__FAIL = 160600284;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP__ZFAIL = 153466220;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_OP__ZPASS = 153360947;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STATES__STENCIL_TEST_ENABLE = 16412629;
const StringHash HASH_ELEMENT_PROFILE_GLSL = 99286140;
const StringHash HASH_ELEMENT_PROFILE_GLSL_TYPE____TECHNIQUE = 189463061;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS = 212247907;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__EVALUATE = 153504709;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__PROGRAM__BIND_UNIFORM__PARAM = 67391149;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES = 1044275;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_FUNC = 122648595;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_FUNC__FUNC = 1746019;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_FUNC__VALUE = 27001269;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__ALPHA_TEST_ENABLE = 25458901;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_COLOR = 234855602;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_ENABLE = 265938677;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION = 86340078;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE = 192412773;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__ALPHA = 69857473;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_EQUATION_SEPARATE__RGB = 103028498;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC = 232761299;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE = 102097861;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_ALPHA = 87587201;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__DEST_RGB = 258279570;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_ALPHA = 92406353;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC_SEPARATE__SRC_RGB = 79019746;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC__DEST = 111807348;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__BLEND_FUNC__SRC = 225087731;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CLIP_PLANE = 222145141;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CLIP_PLANE_ENABLE = 108978341;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__COLOR_LOGIC_OP_ENABLE = 180950757;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__COLOR_MASK = 255898555;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__COLOR_MATERIAL_ENABLE = 228099605;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CULL_FACE = 43017157;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__CULL_FACE_ENABLE = 214058421;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_FUNC = 60791043;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_MASK = 60849115;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_RANGE = 164384997;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DEPTH_TEST_ENABLE = 210405269;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__DITHER_ENABLE = 6095557;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_COLOR = 13501138;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_DENSITY = 254161017;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_ENABLE = 217990181;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_END = 61919204;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_MODE = 185434005;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FOG_START = 16714532;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__FRONT_FACE = 216006629;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHTING_ENABLE = 199959029;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_AMBIENT = 204904372;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_CONSTANT_ATTENUATION = 89943902;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_DIFFUSE = 116568437;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_ENABLE = 243572725;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_LINEAR_ATTENUATION = 202422702;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_MODEL_AMBIENT = 141298692;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_MODEL_TWO_SIDE_ENABLE = 71166565;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_POSITION = 109474798;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_QUADRATIC_ATTENUATION = 120933214;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPECULAR = 106725346;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPOT_CUTOFF = 142273734;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPOT_DIRECTION = 254421054;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LIGHT_SPOT_EXPONENT = 153961540;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LINE_SMOOTH_ENABLE = 29452245;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LINE_WIDTH = 70662872;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__LOGIC_OP = 182906800;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_AMBIENT = 65818004;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_DIFFUSE = 254307157;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_EMISSION = 255976622;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_SHININESS = 217690259;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MATERIAL_SPECULAR = 200400242;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MODEL_VIEW_MATRIX = 32173832;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__MULTISAMPLE_ENABLE = 124063781;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__NORMALIZE_ENABLE = 262058581;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_DISTANCE_ATTENUATION = 76983854;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_FADE_THRESHOLD_SIZE = 148735813;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SIZE = 215948245;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SIZE_MAX = 198679704;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SIZE_MIN = 198679790;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POINT_SMOOTH_ENABLE = 70115349;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POLYGON_OFFSET = 257574004;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__POLYGON_OFFSET_FILL_ENABLE = 46317653;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__PROJECTION_MATRIX = 202063032;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__RESCALE_NORMAL_ENABLE = 183039061;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 219952677;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SAMPLE_ALPHA_TO_ONE_ENABLE = 46182613;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SAMPLE_COVERAGE_ENABLE = 152577397;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SCISSOR = 124993010;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SCISSOR_TEST_ENABLE = 79532117;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__SHADE_MODEL = 13979388;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC = 123750275;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE = 135366709;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__BACK = 56889259;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__FRONT = 105191268;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__MASK = 56996011;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC_SEPARATE__REF = 53899318;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC__FUNC = 3513811;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC__MASK = 3571467;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_FUNC__REF = 151219398;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK = 123839835;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE = 104728613;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__FACE = 227266485;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_MASK_SEPARATE__MASK = 227291323;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP = 231170544;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE = 175575621;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FACE = 21157333;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__FAIL = 21157308;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZFAIL = 69329900;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP_SEPARATE__ZPASS = 69225651;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP__FAIL = 160605708;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP__ZFAIL = 153540716;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_OP__ZPASS = 153581363;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STATES__STENCIL_TEST_ENABLE = 60256213;
const StringHash HASH_ELEMENT_PROGRAM = 127264781;
const StringHash HASH_ELEMENT_PROGRAM____GLES2_PROGRAM_TYPE = 228317749;
const StringHash HASH_ELEMENT_PROGRAM____GLSL_PROGRAM_TYPE = 194037413;
const StringHash HASH_ELEMENT_PROJECTION_MATRIX = 266718536;
const StringHash HASH_ELEMENT_PRSUBSET = 162242228;
const StringHash HASH_ELEMENT_QUADRATIC_ATTENUATION = 89712814;
const StringHash HASH_ELEMENT_QUOTIENT = 208338388;
const StringHash HASH_ELEMENT_RADIUS = 126333123;
const StringHash HASH_ELEMENT_RADIUS____FLOAT2_TYPE = 120887477;
const StringHash HASH_ELEMENT_RADIUS____FLOAT3_TYPE = 132421813;
const StringHash HASH_ELEMENT_RADIUS____FLOAT_TYPE = 228828133;
const StringHash HASH_ELEMENT_RATIONALS = 184959155;
const StringHash HASH_ELEMENT_REAL = 494460;
const StringHash HASH_ELEMENT_REALS = 7911475;
const StringHash HASH_ELEMENT_REF = HASH_ATTRIBUTE_REF;
const StringHash HASH_ELEMENT_REFLECTIVE = 45955861;
const StringHash HASH_ELEMENT_REFLECTIVITY = 221979145;
const StringHash HASH_ELEMENT_REF_ATTACHMENT = 207403124;
const StringHash HASH_ELEMENT_REF____ANYURI = 232374057;
const StringHash HASH_ELEMENT_REM = 30909;
const StringHash HASH_ELEMENT_RENDER = 126634690;
const StringHash HASH_ELEMENT_RENDERABLE = 181400933;
const StringHash HASH_ELEMENT_RENDER__INSTANCE_MATERIAL = 58321916;
const StringHash HASH_ELEMENT_RENDER__INSTANCE_MATERIAL__BIND = 55338052;
const StringHash HASH_ELEMENT_RESCALE_NORMAL_ENABLE = 190127845;
const StringHash HASH_ELEMENT_RESTITUTION = 12871406;
const StringHash HASH_ELEMENT_REVISION = 214997470;
const StringHash HASH_ELEMENT_REVOLUTE = 215366437;
const StringHash HASH_ELEMENT_RGB = 30930;
const StringHash HASH_ELEMENT_RIGID_BODY = HASH_ATTRIBUTE_RIGID_BODY;
const StringHash HASH_ELEMENT_RIGID_BODY_TYPE____TECHNIQUE_COMMON = 224653070;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC = 174112899;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME = 90547925;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE = 55069861;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW = 230332791;
const StringHash HASH_ELEMENT_RIGID_CONSTRAINT = 85611988;
const StringHash HASH_ELEMENT_RIGID_CONSTRAINT_TYPE____TECHNIQUE_COMMON = 250035454;
const StringHash HASH_ELEMENT_RIGID_CONSTRAINT__TECHNIQUE_COMMON__LIMITS = 7967907;
const StringHash HASH_ELEMENT_RIGID_CONSTRAINT__TECHNIQUE_COMMON__LIMITS__LINEAR = 230482626;
const StringHash HASH_ELEMENT_ROOT = 497252;
const StringHash HASH_ELEMENT_ROTATE = 127314085;
const StringHash HASH_ELEMENT_SAMPLER = HASH_ATTRIBUTE_SAMPLER;
const StringHash HASH_ELEMENT_SAMPLER1D = 74628308;
const StringHash HASH_ELEMENT_SAMPLER2D = 74628324;
const StringHash HASH_ELEMENT_SAMPLER2D____FX_SAMPLER2D_TYPE = 240184869;
const StringHash HASH_ELEMENT_SAMPLER2D____GLES_SAMPLER_TYPE = 176312149;
const StringHash HASH_ELEMENT_SAMPLER3D = 74628340;
const StringHash HASH_ELEMENT_SAMPLERCUBE = 46075157;
const StringHash HASH_ELEMENT_SAMPLERDEPTH = 200336040;
const StringHash HASH_ELEMENT_SAMPLERRECT = 46067188;
const StringHash HASH_ELEMENT_SAMPLER_IMAGE = 266093925;
const StringHash HASH_ELEMENT_SAMPLER_STATES = 250454323;
const StringHash HASH_ELEMENT_SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 47727685;
const StringHash HASH_ELEMENT_SAMPLE_ALPHA_TO_ONE_ENABLE = 251778565;
const StringHash HASH_ELEMENT_SAMPLE_COVERAGE = 46033909;
const StringHash HASH_ELEMENT_SAMPLE_COVERAGE_ENABLE = 241354933;
const StringHash HASH_ELEMENT_SAMPLE_COVERAGE__VALUE = 9100309;
const StringHash HASH_ELEMENT_SCALARPRODUCT = 237867844;
const StringHash HASH_ELEMENT_SCALE = HASH_ATTRIBUTE_SCALE;
const StringHash HASH_ELEMENT_SCENE = 7969861;
const StringHash HASH_ELEMENT_SCISSOR = 161524242;
const StringHash HASH_ELEMENT_SCISSOR_TEST_ENABLE = 177084853;
const StringHash HASH_ELEMENT_SDEV = 498374;
const StringHash HASH_ELEMENT_SEC = 31155;
const StringHash HASH_ELEMENT_SECH = 498584;
const StringHash HASH_ELEMENT_SELECTOR = 204189426;
const StringHash HASH_ELEMENT_SEMANTIC = HASH_ATTRIBUTE_SEMANTIC;
const StringHash HASH_ELEMENT_SEMANTICS = 59102819;
const StringHash HASH_ELEMENT_SEMANTIC____NCNAME = 256101141;
const StringHash HASH_ELEMENT_SEMANTIC____TOKEN = 45947246;
const StringHash HASH_ELEMENT_SEP = 31168;
const StringHash HASH_ELEMENT_SET = HASH_ATTRIBUTE_SET;
const StringHash HASH_ELEMENT_SETDIFF = 164278198;
const StringHash HASH_ELEMENT_SETPARAM = 213290989;
const StringHash HASH_ELEMENT_SETPARAM____CG_SETPARAM_TYPE = 236475781;
const StringHash HASH_ELEMENT_SETPARAM____FORMULA_SETPARAM_TYPE = 145470693;
const StringHash HASH_ELEMENT_SETPARAM____KINEMATICS_SETPARAM_TYPE = 24267349;
const StringHash HASH_ELEMENT_SHADER = 127826626;
const StringHash HASH_ELEMENT_SHADER____GLES2_SHADER_TYPE = 150828309;
const StringHash HASH_ELEMENT_SHADER____GLSL_SHADER_TYPE = 123766085;
const StringHash HASH_ELEMENT_SHADE_MODEL = 188464412;
const StringHash HASH_ELEMENT_SHAPE = 7989349;
const StringHash HASH_ELEMENT_SHELLS = 127845171;
const StringHash HASH_ELEMENT_SHININESS = 5256531;
const StringHash HASH_ELEMENT_SIDREF = 92116630;
const StringHash HASH_ELEMENT_SIDREF_ARRAY = 254086681;
const StringHash HASH_ELEMENT_SIN = 31230;
const StringHash HASH_ELEMENT_SINH = 499784;
const StringHash HASH_ELEMENT_SIZE = 499973;
const StringHash HASH_ELEMENT_SIZE_EXACT = 190531236;
const StringHash HASH_ELEMENT_SIZE_RATIO = 191835135;
const StringHash HASH_ELEMENT_SKELETON = 29544190;
const StringHash HASH_ELEMENT_SKEW = 500167;
const StringHash HASH_ELEMENT_SKIN = 500222;
const StringHash HASH_ELEMENT_SOLIDS = 128331699;
const StringHash HASH_ELEMENT_SOURCE = HASH_ATTRIBUTE_SOURCE;
const StringHash HASH_ELEMENT_SOURCES = 174885299;
const StringHash HASH_ELEMENT_SOURCES____FX_SOURCES_TYPE = 162385413;
const StringHash HASH_ELEMENT_SOURCE_DATA = 166708257;
const StringHash HASH_ELEMENT_SOURCE_TYPE____TECHNIQUE_COMMON = 212928718;
const StringHash HASH_ELEMENT_SPECULAR = 112903458;
const StringHash HASH_ELEMENT_SPEED = 8022964;
const StringHash HASH_ELEMENT_SPEED____COMMON_FLOAT2_OR_PARAM_TYPE = 171925365;
const StringHash HASH_ELEMENT_SPEED____COMMON_FLOAT_OR_PARAM_TYPE = 235417781;
const StringHash HASH_ELEMENT_SPHERE = 128380037;
const StringHash HASH_ELEMENT_SPLINE = 128397381;
const StringHash HASH_ELEMENT_SPLINE_TYPE____CONTROL_VERTICES = 246011107;
const StringHash HASH_ELEMENT_SPOT = 501604;
const StringHash HASH_ELEMENT_SPRING = 128421959;
const StringHash HASH_ELEMENT_SPRING__LINEAR = 62916466;
const StringHash HASH_ELEMENT_SRC = 31363;
const StringHash HASH_ELEMENT_SRC_ALPHA = 156781665;
const StringHash HASH_ELEMENT_SRC_RGB = 176777378;
const StringHash HASH_ELEMENT_STATES = 128617155;
const StringHash HASH_ELEMENT_STATIC_FRICTION = 4890926;
const StringHash HASH_ELEMENT_STENCIL_CLEAR = 217473554;
const StringHash HASH_ELEMENT_STENCIL_FUNC = 164569299;
const StringHash HASH_ELEMENT_STENCIL_FUNC_SEPARATE = 241541829;
const StringHash HASH_ELEMENT_STENCIL_MASK = 164690955;
const StringHash HASH_ELEMENT_STENCIL_MASK_SEPARATE = 61416245;
const StringHash HASH_ELEMENT_STENCIL_OP = 77189280;
const StringHash HASH_ELEMENT_STENCIL_OP_SEPARATE = 23242661;
const StringHash HASH_ELEMENT_STENCIL_TARGET = 208409604;
const StringHash HASH_ELEMENT_STENCIL_TEST_ENABLE = 159579189;
const StringHash HASH_ELEMENT_STIFFNESS = 265106947;
const StringHash HASH_ELEMENT_STRING = 128684103;
const StringHash HASH_ELEMENT_SUBJECT = 179899348;
const StringHash HASH_ELEMENT_SUBSET = 128686532;
const StringHash HASH_ELEMENT_SUM = 31421;
const StringHash HASH_ELEMENT_SURFACE = 180930533;
const StringHash HASH_ELEMENT_SURFACES = 210533987;
const StringHash HASH_ELEMENT_SURFACE_CURVES = 153150163;
const StringHash HASH_ELEMENT_SURFACE_TYPE____CYLINDER = 188830578;
const StringHash HASH_ELEMENT_SWEPT_SURFACE = 1359237;
const StringHash HASH_ELEMENT_SWING_CONE_AND_TWIST = 103048868;
const StringHash HASH_ELEMENT_TAN = 31358;
const StringHash HASH_ELEMENT_TANH = 501832;
const StringHash HASH_ELEMENT_TARGET = HASH_ATTRIBUTE_TARGET;
const StringHash HASH_ELEMENT_TARGETS = 176741571;
const StringHash HASH_ELEMENT_TARGET_VALUE = 264572533;
const StringHash HASH_ELEMENT_TECHNIQUE = 167080453;
const StringHash HASH_ELEMENT_TECHNIQUE_COMMON = 181609502;
const StringHash HASH_ELEMENT_TECHNIQUE_COMMON____FORMULA_TECHNIQUE_TYPE = 217602677;
const StringHash HASH_ELEMENT_TECHNIQUE_COMMON____KINEMATICS_MODEL_TECHNIQUE_TYPE = 210314469;
const StringHash HASH_ELEMENT_TECHNIQUE_COMMON____KINEMATICS_TECHNIQUE_TYPE = 163390181;
const StringHash HASH_ELEMENT_TECHNIQUE_COMMON____MOTION_TECHNIQUE_TYPE = 13161605;
const StringHash HASH_ELEMENT_TECHNIQUE_HINT = 4897140;
const StringHash HASH_ELEMENT_TECHNIQUE_OVERRIDE = 199869589;
const StringHash HASH_ELEMENT_TECHNIQUE____TECHNIQUE_TYPE = 84653125;
const StringHash HASH_ELEMENT_TENDSTO = 180665055;
const StringHash HASH_ELEMENT_TEXCOMBINER = 105007714;
const StringHash HASH_ELEMENT_TEXCOORD = HASH_ATTRIBUTE_TEXCOORD;
const StringHash HASH_ELEMENT_TEXENV = 128773206;
const StringHash HASH_ELEMENT_TEXTURE = HASH_ATTRIBUTE_TEXTURE;
const StringHash HASH_ELEMENT_TEXTURE1D = 264041108;
const StringHash HASH_ELEMENT_TEXTURE1D_ENABLE = 244011605;
const StringHash HASH_ELEMENT_TEXTURE2D = 264041124;
const StringHash HASH_ELEMENT_TEXTURE2D_ENABLE = 244011861;
const StringHash HASH_ELEMENT_TEXTURE3D = 264041140;
const StringHash HASH_ELEMENT_TEXTURE3D_ENABLE = 244012117;
const StringHash HASH_ELEMENT_TEXTURECUBE = 217269973;
const StringHash HASH_ELEMENT_TEXTURECUBE_ENABLE = 177215061;
const StringHash HASH_ELEMENT_TEXTUREDEPTH = 255090248;
const StringHash HASH_ELEMENT_TEXTUREDEPTH_ENABLE = 117546261;
const StringHash HASH_ELEMENT_TEXTURERECT = 217097780;
const StringHash HASH_ELEMENT_TEXTURERECT_ENABLE = 181279813;
const StringHash HASH_ELEMENT_TEXTURE_ENV_COLOR = 145348738;
const StringHash HASH_ELEMENT_TEXTURE_ENV_MODE = 244006133;
const StringHash HASH_ELEMENT_TEXTURE_PIPELINE = 195376869;
const StringHash HASH_ELEMENT_TIMES = 8061891;
const StringHash HASH_ELEMENT_TIME_STEP = 62312896;
const StringHash HASH_ELEMENT_TITLE = 8063781;
const StringHash HASH_ELEMENT_TOKEN_ARRAY = 69466441;
const StringHash HASH_ELEMENT_TORUS = 8088003;
const StringHash HASH_ELEMENT_TRANSLATE = 140137253;
const StringHash HASH_ELEMENT_TRANSPARENCY = 19939593;
const StringHash HASH_ELEMENT_TRANSPARENT = 169018372;
const StringHash HASH_ELEMENT_TRANSPOSE = 140127509;
const StringHash HASH_ELEMENT_TRIANGLES = 260356419;
const StringHash HASH_ELEMENT_TRIFANS = 193972259;
const StringHash HASH_ELEMENT_TRISTRIPS = 11275235;
const StringHash HASH_ELEMENT_TRUE = 506293;
const StringHash HASH_ELEMENT_UNION = 8147038;
const StringHash HASH_ELEMENT_UNIT = 509188;
const StringHash HASH_ELEMENT_UNNORMALIZED = 80242212;
const StringHash HASH_ELEMENT_UPLIMIT = 208864372;
const StringHash HASH_ELEMENT_UP_AXIS = 207982451;
const StringHash HASH_ELEMENT_USERTYPE = 164218789;
const StringHash HASH_ELEMENT_USERTYPE____CG_USER_TYPE = 241128037;
const StringHash HASH_ELEMENT_V = 118;
const StringHash HASH_ELEMENT_VALUE = HASH_ATTRIBUTE_VALUE;
const StringHash HASH_ELEMENT_VALUE____COMMON_FLOAT_OR_PARAM_TYPE = 118425765;
const StringHash HASH_ELEMENT_VALUE____FX_SAMPLER1D_TYPE = 247987765;
const StringHash HASH_ELEMENT_VALUE____FX_SAMPLER2D_TYPE = 231210549;
const StringHash HASH_ELEMENT_VALUE____FX_SAMPLER3D_TYPE = 214433333;
const StringHash HASH_ELEMENT_VALUE____FX_SAMPLERCUBE_TYPE = 134800341;
const StringHash HASH_ELEMENT_VALUE____FX_SAMPLERDEPTH_TYPE = 222337061;
const StringHash HASH_ELEMENT_VALUE____FX_SAMPLERRECT_TYPE = 165207509;
const StringHash HASH_ELEMENT_VALUE____GLES_TEXTURE_PIPELINE_TYPE = 95302997;
const StringHash HASH_ELEMENT_VARIANCE = 143622997;
const StringHash HASH_ELEMENT_VCOUNT = 130706516;
const StringHash HASH_ELEMENT_VEC2 = 510818;
const StringHash HASH_ELEMENT_VEC3 = 510819;
const StringHash HASH_ELEMENT_VEC4 = 510820;
const StringHash HASH_ELEMENT_VECTOR = 130788194;
const StringHash HASH_ELEMENT_VECTORPRODUCT = 189730116;
const StringHash HASH_ELEMENT_VELOCITY = 204842873;
const StringHash HASH_ELEMENT_VERTEX_WEIGHTS = 19207187;
const StringHash HASH_ELEMENT_VERTICES = 211484163;
const StringHash HASH_ELEMENT_VISUAL_SCENE = 129577413;
const StringHash HASH_ELEMENT_WIRES = 8259779;
const StringHash HASH_ELEMENT_WRAP_P = 132679264;
const StringHash HASH_ELEMENT_WRAP_S = 132679267;
const StringHash HASH_ELEMENT_WRAP_S____FX_SAMPLER_WRAP_ENUM = 72449261;
const StringHash HASH_ELEMENT_WRAP_S____GLES_SAMPLER_WRAP_ENUM = 205200669;
const StringHash HASH_ELEMENT_WRAP_T = 132679268;
const StringHash HASH_ELEMENT_WRAP_T____FX_SAMPLER_WRAP_ENUM = 156335325;
const StringHash HASH_ELEMENT_WRAP_T____GLES_SAMPLER_WRAP_ENUM = 205201949;
const StringHash HASH_ELEMENT_XFOV = 519526;
const StringHash HASH_ELEMENT_XMAG = 521079;
const StringHash HASH_ELEMENT_XOR = 32610;
const StringHash HASH_ELEMENT_YFOV = 523622;
const StringHash HASH_ELEMENT_YMAG = 525175;
const StringHash HASH_ELEMENT_ZFAIL = 8439804;
const StringHash HASH_ELEMENT_ZFAR = 527490;
const StringHash HASH_ELEMENT_ZNEAR = 8473474;
const StringHash HASH_ELEMENT_ZPASS = 8480931;
const StringHash STATE_MACHINE_ROOT = 88705076;
const StringHash STATE_MACHINE_END = 123005284;


struct COLLADA__AttributeData
{
    static const COLLADA__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_BASE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__version_enum version;
    COLLADABU::URI base;
};

struct altitude__AttributeData
{
    static const altitude__AttributeData DEFAULT;

    ENUM__altitude_mode_enum mode;
};

struct unit__AttributeData
{
    static const unit__AttributeData DEFAULT;

    float meter;
    const ParserChar* name;
};

struct extra__AttributeData
{
    static const extra__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* type;
};

struct technique____technique_type__AttributeData
{
    static const technique____technique_type__AttributeData DEFAULT;

    const ParserChar* profile;
};

struct library_animations__AttributeData
{
    static const library_animations__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct animation__AttributeData
{
    static const animation__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct source__AttributeData
{
    static const source__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct token_array__AttributeData
{
    static const token_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct IDREF_array__AttributeData
{
    static const IDREF_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct Name_array__AttributeData
{
    static const Name_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct bool_array__AttributeData
{
    static const bool_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct float_array__AttributeData
{
    static const float_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
    uint8 digits;
    sint16 magnitude;
};

struct int_array__AttributeData
{
    static const int_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
    sint64 minInclusive;
    sint64 maxInclusive;
};

struct SIDREF_array__AttributeData
{
    static const SIDREF_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct accessor__AttributeData
{
    static const accessor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_SOURCE_PRESENT = 0x2;

    uint32 present_attributes;

    uint64 count;
    uint64 offset;
    COLLADABU::URI source;
    uint64 stride;
};

struct param____param_type__AttributeData
{
    static const param____param_type__AttributeData DEFAULT;

    const ParserChar* name;
    const ParserChar* sid;
    const ParserChar* semantic;
    const ParserChar* type;
};

struct sampler__AttributeData
{
    static const sampler__AttributeData DEFAULT;

    const ParserChar* id;
    ENUM__sampler_behavior_enum pre_behavior;
    ENUM__sampler_behavior_enum post_behavior;
};

struct input____input_local_type__AttributeData
{
    static const input____input_local_type__AttributeData DEFAULT;

    const ParserChar* semantic;
    const ParserChar* source;
};

struct channel__AttributeData
{
    static const channel__AttributeData DEFAULT;

    const ParserChar* source;
    const ParserChar* target;
};

struct library_animation_clips__AttributeData
{
    static const library_animation_clips__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct animation_clip__AttributeData
{
    static const animation_clip__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_END_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    float start;
    float end;
};

struct instance_animation__AttributeData
{
    static const instance_animation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_formula__AttributeData
{
    static const instance_formula__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* sid;
    const ParserChar* name;
    COLLADABU::URI url;
};

struct setparam____formula_setparam_type__AttributeData
{
    static const setparam____formula_setparam_type__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct connect_param__AttributeData
{
    static const connect_param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct library_cameras__AttributeData
{
    static const library_cameras__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct camera__AttributeData
{
    static const camera__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct xmag__AttributeData
{
    static const xmag__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct ymag__AttributeData
{
    static const ymag__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct aspect_ratio__AttributeData
{
    static const aspect_ratio__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct znear__AttributeData
{
    static const znear__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct zfar__AttributeData
{
    static const zfar__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct xfov__AttributeData
{
    static const xfov__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct yfov__AttributeData
{
    static const yfov__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_controllers__AttributeData
{
    static const library_controllers__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct controller__AttributeData
{
    static const controller__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct skin__AttributeData
{
    static const skin__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SOURCE_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI source;
};

struct vertex_weights__AttributeData
{
    static const vertex_weights__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 count;
};

struct input____input_local_offset_type__AttributeData
{
    static const input____input_local_offset_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_OFFSET_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_SET_PRESENT = 0x2;

    uint32 present_attributes;

    uint64 offset;
    const ParserChar* semantic;
    const ParserChar* source;
    uint64 set;
};

struct morph__AttributeData
{
    static const morph__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SOURCE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__morph_method_enum method;
    COLLADABU::URI source;
};

struct library_geometries__AttributeData
{
    static const library_geometries__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct geometry__AttributeData
{
    static const geometry__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct convex_mesh__AttributeData
{
    static const convex_mesh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_CONVEX_HULL_OF_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI convex_hull_of;
};

struct vertices__AttributeData
{
    static const vertices__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct lines__AttributeData
{
    static const lines__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct linestrips__AttributeData
{
    static const linestrips__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct polygons__AttributeData
{
    static const polygons__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct polylist__AttributeData
{
    static const polylist__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct triangles__AttributeData
{
    static const triangles__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct trifans__AttributeData
{
    static const trifans__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct tristrips__AttributeData
{
    static const tristrips__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct spline__AttributeData
{
    static const spline__AttributeData DEFAULT;

    bool closed;
};

struct curve__AttributeData
{
    static const curve__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct nurbs__AttributeData
{
    static const nurbs__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEGREE_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 degree;
    bool closed;
};

struct surface__AttributeData
{
    static const surface__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct nurbs_surface__AttributeData
{
    static const nurbs_surface__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEGREE_U_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_DEGREE_V_PRESENT = 0x2;

    uint32 present_attributes;

    uint64 degree_u;
    bool closed_u;
    uint64 degree_v;
    bool closed_v;
};

struct edges__AttributeData
{
    static const edges__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    sint32 count;
};

struct wires__AttributeData
{
    static const wires__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct faces__AttributeData
{
    static const faces__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct pcurves__AttributeData
{
    static const pcurves__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct shells__AttributeData
{
    static const shells__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct solids__AttributeData
{
    static const solids__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct library_effects__AttributeData
{
    static const library_effects__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct effect__AttributeData
{
    static const effect__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct annotate__AttributeData
{
    static const annotate__AttributeData DEFAULT;

    const ParserChar* name;
};

struct newparam____fx_newparam_type__AttributeData
{
    static const newparam____fx_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_image__AttributeData
{
    static const instance_image__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct profile_COMMON__AttributeData
{
    static const profile_COMMON__AttributeData DEFAULT;

    const ParserChar* id;
};

struct newparam____fx_common_newparam_type__AttributeData
{
    static const newparam____fx_common_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_common_type____technique__AttributeData
{
    static const profile_common_type____technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct fx_common_color_or_texture_type____color__AttributeData
{
    static const fx_common_color_or_texture_type____color__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct fx_common_color_or_texture_type____param__AttributeData
{
    static const fx_common_color_or_texture_type____param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct texture__AttributeData
{
    static const texture__AttributeData DEFAULT;

    const ParserChar* texture;
    const ParserChar* texcoord;
};

struct fx_common_float_or_param_type____float__AttributeData
{
    static const fx_common_float_or_param_type____float__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct fx_common_float_or_param_type____param__AttributeData
{
    static const fx_common_float_or_param_type____param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct transparent__AttributeData
{
    static const transparent__AttributeData DEFAULT;

    ENUM__fx_opaque_enum opaque;
};

struct profile_BRIDGE__AttributeData
{
    static const profile_BRIDGE__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* platform;
    COLLADABU::URI url;
};

struct profile_GLES2__AttributeData
{
    static const profile_GLES2__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_PLATFORMS_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* language;
    GeneratedSaxParser::XSList<ParserString> platforms;
};

struct include__AttributeData
{
    static const include__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* sid;
    COLLADABU::URI url;
};

struct code__AttributeData
{
    static const code__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_gles2_type____newparam__AttributeData
{
    static const profile_gles2_type____newparam__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_GLES2__newparam__usertype__AttributeData
{
    static const profile_GLES2__newparam__usertype__AttributeData DEFAULT;

    const ParserChar* _typename;
};

struct profile_GLES2__newparam__usertype__setparam__AttributeData
{
    static const profile_GLES2__newparam__usertype__setparam__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_GLES2__newparam__usertype__setparam__array__AttributeData
{
    static const profile_GLES2__newparam__usertype__setparam__array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct profile_gles2_type____technique__AttributeData
{
    static const profile_gles2_type____technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct pass____gles2_pass_type__AttributeData
{
    static const pass____gles2_pass_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_GLES2__technique__pass__states__blend_color__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_equation__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_equation__AttributeData DEFAULT;

    ENUM__gl_blend_equation_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_equation_separate__rgb__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_equation_separate__rgb__AttributeData DEFAULT;

    ENUM__gl_blend_equation_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_equation_separate__alpha__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_equation_separate__alpha__AttributeData DEFAULT;

    ENUM__gl_blend_equation_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_func__src__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_func__src__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_func__dest__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_func__dest__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_func_separate__src_rgb__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_func_separate__src_rgb__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_func_separate__dest_rgb__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_func_separate__dest_rgb__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_func_separate__src_alpha__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_func_separate__src_alpha__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_func_separate__dest_alpha__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_func_separate__dest_alpha__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__color_mask__AttributeData
{
    static const profile_GLES2__technique__pass__states__color_mask__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__cull_face__AttributeData
{
    static const profile_GLES2__technique__pass__states__cull_face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__depth_func__AttributeData
{
    static const profile_GLES2__technique__pass__states__depth_func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__depth_mask__AttributeData
{
    static const profile_GLES2__technique__pass__states__depth_mask__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__depth_range__AttributeData
{
    static const profile_GLES2__technique__pass__states__depth_range__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__front_face__AttributeData
{
    static const profile_GLES2__technique__pass__states__front_face__AttributeData DEFAULT;

    ENUM__gl_front_face_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__line_width__AttributeData
{
    static const profile_GLES2__technique__pass__states__line_width__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__polygon_offset__AttributeData
{
    static const profile_GLES2__technique__pass__states__polygon_offset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__point_size__AttributeData
{
    static const profile_GLES2__technique__pass__states__point_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct sample_coverage__value__AttributeData
{
    static const sample_coverage__value__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
};

struct invert__AttributeData
{
    static const invert__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__scissor__AttributeData
{
    static const profile_GLES2__technique__pass__states__scissor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func__func__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func__func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func__ref__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func__mask__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func_separate__front__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func_separate__front__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func_separate__back__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func_separate__back__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func_separate__ref__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func_separate__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_func_separate__mask__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_func_separate__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_mask__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_mask__AttributeData DEFAULT;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_mask_separate__face__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_mask_separate__face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_mask_separate__mask__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_mask_separate__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op__fail__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op__fail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op__zfail__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op__zfail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op__zpass__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op__zpass__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op_separate__face__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op_separate__face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op_separate__fail__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op_separate__fail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op_separate__zfail__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op_separate__zfail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_op_separate__zpass__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_op_separate__zpass__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__blend_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__blend_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__cull_face_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__cull_face_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__depth_test_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__depth_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__dither_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__dither_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__polygon_offset_fill_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__polygon_offset_fill_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct point_size_enable__AttributeData
{
    static const point_size_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__sample_alpha_to_coverage_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__sample_alpha_to_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__sample_coverage_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__sample_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__scissor_test_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__scissor_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES2__technique__pass__states__stencil_test_enable__AttributeData
{
    static const profile_GLES2__technique__pass__states__stencil_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct shader____gles2_shader_type__AttributeData
{
    static const shader____gles2_shader_type__AttributeData DEFAULT;

    ENUM__fx_pipeline_stage_enum stage;
};

struct gles2_shader_type____sources__AttributeData
{
    static const gles2_shader_type____sources__AttributeData DEFAULT;

    const ParserChar* entry;
};

struct import__AttributeData
{
    static const import__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct compiler__AttributeData
{
    static const compiler__AttributeData DEFAULT;

    const ParserChar* platform;
    const ParserChar* target;
    const ParserChar* options;
};

struct binary__hex__AttributeData
{
    static const binary__hex__AttributeData DEFAULT;

    const ParserChar* format;
};

struct linker__AttributeData
{
    static const linker__AttributeData DEFAULT;

    const ParserChar* platform;
    const ParserChar* target;
    const ParserChar* options;
};

struct gles2_program_type____bind_attribute__AttributeData
{
    static const gles2_program_type____bind_attribute__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct gles2_program_type____bind_uniform__AttributeData
{
    static const gles2_program_type____bind_uniform__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct profile_GLES2__technique__pass__program__bind_uniform__param__AttributeData
{
    static const profile_GLES2__technique__pass__program__bind_uniform__param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct color_target__AttributeData
{
    static const color_target__AttributeData DEFAULT;

    uint64 index;
    uint64 mip;
    ENUM__image_face_enum face;
    uint64 slice;
};

struct fx_colortarget_type____param__AttributeData
{
    static const fx_colortarget_type____param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct depth_target__AttributeData
{
    static const depth_target__AttributeData DEFAULT;

    uint64 index;
    uint64 mip;
    ENUM__image_face_enum face;
    uint64 slice;
};

struct stencil_target__AttributeData
{
    static const stencil_target__AttributeData DEFAULT;

    uint64 index;
    uint64 mip;
    ENUM__image_face_enum face;
    uint64 slice;
};

struct color_clear__AttributeData
{
    static const color_clear__AttributeData DEFAULT;

    uint64 index;
};

struct stencil_clear__AttributeData
{
    static const stencil_clear__AttributeData DEFAULT;

    uint64 index;
};

struct depth_clear__AttributeData
{
    static const depth_clear__AttributeData DEFAULT;

    uint64 index;
};

struct profile_GLSL__AttributeData
{
    static const profile_GLSL__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* platform;
};

struct newparam____glsl_newparam_type__AttributeData
{
    static const newparam____glsl_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct array____glsl_array_type__AttributeData
{
    static const array____glsl_array_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct profile_glsl_type____technique__AttributeData
{
    static const profile_glsl_type____technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct profile_GLSL__technique__pass__AttributeData
{
    static const profile_GLSL__technique__pass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_GLSL__technique__pass__states__alpha_func__func__AttributeData
{
    static const profile_GLSL__technique__pass__states__alpha_func__func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__alpha_func__value__AttributeData
{
    static const profile_GLSL__technique__pass__states__alpha_func__value__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_func__src__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_func__src__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_func__dest__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_func__dest__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_func_separate__src_rgb__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_func_separate__src_rgb__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_func_separate__dest_rgb__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_func_separate__dest_rgb__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_func_separate__src_alpha__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_func_separate__src_alpha__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_func_separate__dest_alpha__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_func_separate__dest_alpha__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_equation__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_equation__AttributeData DEFAULT;

    ENUM__gl_blend_equation_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_equation_separate__rgb__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_equation_separate__rgb__AttributeData DEFAULT;

    ENUM__gl_blend_equation_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_equation_separate__alpha__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_equation_separate__alpha__AttributeData DEFAULT;

    ENUM__gl_blend_equation_enum value;
    const ParserChar* param;
};

struct color_material__face__AttributeData
{
    static const color_material__face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct color_material__mode__AttributeData
{
    static const color_material__mode__AttributeData DEFAULT;

    ENUM__gl_material_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__cull_face__AttributeData
{
    static const profile_GLSL__technique__pass__states__cull_face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__depth_func__AttributeData
{
    static const profile_GLSL__technique__pass__states__depth_func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__fog_mode__AttributeData
{
    static const profile_GLSL__technique__pass__states__fog_mode__AttributeData DEFAULT;

    ENUM__gl_fog_enum value;
    const ParserChar* param;
};

struct fog_coord_src__AttributeData
{
    static const fog_coord_src__AttributeData DEFAULT;

    ENUM__gl_fog_coord_src_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__front_face__AttributeData
{
    static const profile_GLSL__technique__pass__states__front_face__AttributeData DEFAULT;

    ENUM__gl_front_face_enum value;
    const ParserChar* param;
};

struct light_model_color_control__AttributeData
{
    static const light_model_color_control__AttributeData DEFAULT;

    ENUM__gl_light_model_color_control_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__logic_op__AttributeData
{
    static const profile_GLSL__technique__pass__states__logic_op__AttributeData DEFAULT;

    ENUM__gl_logic_op_enum value;
    const ParserChar* param;
};

struct polygon_mode__face__AttributeData
{
    static const polygon_mode__face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct polygon_mode__mode__AttributeData
{
    static const polygon_mode__mode__AttributeData DEFAULT;

    ENUM__gl_polygon_mode_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__shade_model__AttributeData
{
    static const profile_GLSL__technique__pass__states__shade_model__AttributeData DEFAULT;

    ENUM__gl_shade_model_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func__func__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func__func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func__ref__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func__mask__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op__fail__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op__fail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op__zfail__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op__zfail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op__zpass__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op__zpass__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func_separate__front__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func_separate__front__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func_separate__back__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func_separate__back__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func_separate__ref__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func_separate__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_func_separate__mask__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_func_separate__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op_separate__face__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op_separate__face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op_separate__fail__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op_separate__fail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op_separate__zfail__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op_separate__zfail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_op_separate__zpass__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_op_separate__zpass__AttributeData DEFAULT;

    ENUM__gl_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_mask_separate__face__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_mask_separate__face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_mask_separate__mask__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_mask_separate__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__light_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_ambient__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_diffuse__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_specular__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_position__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_position__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_constant_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_constant_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_linear_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_linear_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_quadratic_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_quadratic_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_spot_cutoff__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_spot_cutoff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_spot_direction__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_spot_direction__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__light_spot_exponent__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_spot_exponent__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct texture1D__AttributeData
{
    static const texture1D__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct texture2D__AttributeData
{
    static const texture2D__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct texture3D__AttributeData
{
    static const texture3D__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct textureCUBE__AttributeData
{
    static const textureCUBE__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct textureRECT__AttributeData
{
    static const textureRECT__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct textureDEPTH__AttributeData
{
    static const textureDEPTH__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct texture1D_enable__AttributeData
{
    static const texture1D_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct texture2D_enable__AttributeData
{
    static const texture2D_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct texture3D_enable__AttributeData
{
    static const texture3D_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct textureCUBE_enable__AttributeData
{
    static const textureCUBE_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct textureRECT_enable__AttributeData
{
    static const textureRECT_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct textureDEPTH_enable__AttributeData
{
    static const textureDEPTH_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct texture_env_color__AttributeData
{
    static const texture_env_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct texture_env_mode__AttributeData
{
    static const texture_env_mode__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__clip_plane__AttributeData
{
    static const profile_GLSL__technique__pass__states__clip_plane__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__clip_plane_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__clip_plane_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__states__blend_color__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__color_mask__AttributeData
{
    static const profile_GLSL__technique__pass__states__color_mask__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
};

struct depth_bounds__AttributeData
{
    static const depth_bounds__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__depth_mask__AttributeData
{
    static const profile_GLSL__technique__pass__states__depth_mask__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__depth_range__AttributeData
{
    static const profile_GLSL__technique__pass__states__depth_range__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__fog_density__AttributeData
{
    static const profile_GLSL__technique__pass__states__fog_density__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__fog_start__AttributeData
{
    static const profile_GLSL__technique__pass__states__fog_start__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__fog_end__AttributeData
{
    static const profile_GLSL__technique__pass__states__fog_end__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__fog_color__AttributeData
{
    static const profile_GLSL__technique__pass__states__fog_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__light_model_ambient__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_model_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__lighting_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__lighting_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct line_stipple__AttributeData
{
    static const line_stipple__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__line_width__AttributeData
{
    static const profile_GLSL__technique__pass__states__line_width__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__material_ambient__AttributeData
{
    static const profile_GLSL__technique__pass__states__material_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__material_diffuse__AttributeData
{
    static const profile_GLSL__technique__pass__states__material_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__material_emission__AttributeData
{
    static const profile_GLSL__technique__pass__states__material_emission__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__material_shininess__AttributeData
{
    static const profile_GLSL__technique__pass__states__material_shininess__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__material_specular__AttributeData
{
    static const profile_GLSL__technique__pass__states__material_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__model_view_matrix__AttributeData
{
    static const profile_GLSL__technique__pass__states__model_view_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__point_distance_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__states__point_distance_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__point_fade_threshold_size__AttributeData
{
    static const profile_GLSL__technique__pass__states__point_fade_threshold_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__point_size__AttributeData
{
    static const profile_GLSL__technique__pass__states__point_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__point_size_min__AttributeData
{
    static const profile_GLSL__technique__pass__states__point_size_min__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__point_size_max__AttributeData
{
    static const profile_GLSL__technique__pass__states__point_size_max__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__polygon_offset__AttributeData
{
    static const profile_GLSL__technique__pass__states__polygon_offset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__projection_matrix__AttributeData
{
    static const profile_GLSL__technique__pass__states__projection_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__scissor__AttributeData
{
    static const profile_GLSL__technique__pass__states__scissor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_mask__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_mask__AttributeData DEFAULT;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__alpha_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__alpha_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__blend_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__blend_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__color_logic_op_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__color_logic_op_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__color_material_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__color_material_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__cull_face_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__cull_face_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct depth_bounds_enable__AttributeData
{
    static const depth_bounds_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct depth_clamp_enable__AttributeData
{
    static const depth_clamp_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__depth_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__depth_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__dither_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__dither_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__fog_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__fog_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct light_model_local_viewer_enable__AttributeData
{
    static const light_model_local_viewer_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__light_model_two_side_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__light_model_two_side_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__line_smooth_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__line_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct line_stipple_enable__AttributeData
{
    static const line_stipple_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct logic_op_enable__AttributeData
{
    static const logic_op_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__multisample_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__multisample_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__normalize_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__normalize_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__point_smooth_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__point_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__polygon_offset_fill_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__polygon_offset_fill_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_offset_line_enable__AttributeData
{
    static const polygon_offset_line_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_offset_point_enable__AttributeData
{
    static const polygon_offset_point_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_smooth_enable__AttributeData
{
    static const polygon_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_stipple_enable__AttributeData
{
    static const polygon_stipple_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__rescale_normal_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__rescale_normal_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__sample_alpha_to_coverage_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__sample_alpha_to_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__sample_alpha_to_one_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__sample_alpha_to_one_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__sample_coverage_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__sample_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__scissor_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__scissor_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__states__stencil_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__states__stencil_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct shader____glsl_shader_type__AttributeData
{
    static const shader____glsl_shader_type__AttributeData DEFAULT;

    ENUM__fx_pipeline_stage_enum stage;
};

struct glsl_program_type____bind_attribute__AttributeData
{
    static const glsl_program_type____bind_attribute__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct glsl_program_type____bind_uniform__AttributeData
{
    static const glsl_program_type____bind_uniform__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct profile_GLSL__technique__pass__program__bind_uniform__param__AttributeData
{
    static const profile_GLSL__technique__pass__program__bind_uniform__param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_CG__AttributeData
{
    static const profile_CG__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* platform;
};

struct newparam____cg_newparam_type__AttributeData
{
    static const newparam____cg_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct array____cg_array_type__AttributeData
{
    static const array____cg_array_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
    bool resizable;
};

struct usertype____cg_user_type__AttributeData
{
    static const usertype____cg_user_type__AttributeData DEFAULT;

    const ParserChar* _typename;
    const ParserChar* source;
};

struct setparam____cg_setparam_type__AttributeData
{
    static const setparam____cg_setparam_type__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_cg_type____technique__AttributeData
{
    static const profile_cg_type____technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct pass____cg_pass_type__AttributeData
{
    static const pass____cg_pass_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_CG__technique__pass__program__shader__AttributeData
{
    static const profile_CG__technique__pass__program__shader__AttributeData DEFAULT;

    ENUM__fx_pipeline_stage_enum stage;
};

struct profile_CG__technique__pass__program__shader__sources__AttributeData
{
    static const profile_CG__technique__pass__program__shader__sources__AttributeData DEFAULT;

    const ParserChar* entry;
};

struct profile_CG__technique__pass__program__shader__bind_uniform__AttributeData
{
    static const profile_CG__technique__pass__program__shader__bind_uniform__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct profile_CG__technique__pass__program__shader__bind_uniform__param__AttributeData
{
    static const profile_CG__technique__pass__program__shader__bind_uniform__param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_GLES__AttributeData
{
    static const profile_GLES__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* platform;
};

struct newparam____gles_newparam_type__AttributeData
{
    static const newparam____gles_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct texcoord__AttributeData
{
    static const texcoord__AttributeData DEFAULT;

    const ParserChar* semantic;
};

struct profile_gles_type____technique__AttributeData
{
    static const profile_gles_type____technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct profile_GLES__technique__pass__AttributeData
{
    static const profile_GLES__technique__pass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_GLES__technique__pass__states__alpha_func__func__AttributeData
{
    static const profile_GLES__technique__pass__states__alpha_func__func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__alpha_func__value__AttributeData
{
    static const profile_GLES__technique__pass__states__alpha_func__value__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__blend_func__src__AttributeData
{
    static const profile_GLES__technique__pass__states__blend_func__src__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__blend_func__dest__AttributeData
{
    static const profile_GLES__technique__pass__states__blend_func__dest__AttributeData DEFAULT;

    ENUM__gl_blend_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__clip_plane__AttributeData
{
    static const profile_GLES__technique__pass__states__clip_plane__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__color_mask__AttributeData
{
    static const profile_GLES__technique__pass__states__color_mask__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__cull_face__AttributeData
{
    static const profile_GLES__technique__pass__states__cull_face__AttributeData DEFAULT;

    ENUM__gl_face_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__depth_func__AttributeData
{
    static const profile_GLES__technique__pass__states__depth_func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__depth_mask__AttributeData
{
    static const profile_GLES__technique__pass__states__depth_mask__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__depth_range__AttributeData
{
    static const profile_GLES__technique__pass__states__depth_range__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__fog_color__AttributeData
{
    static const profile_GLES__technique__pass__states__fog_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__fog_density__AttributeData
{
    static const profile_GLES__technique__pass__states__fog_density__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__fog_mode__AttributeData
{
    static const profile_GLES__technique__pass__states__fog_mode__AttributeData DEFAULT;

    ENUM__gl_fog_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__fog_start__AttributeData
{
    static const profile_GLES__technique__pass__states__fog_start__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__fog_end__AttributeData
{
    static const profile_GLES__technique__pass__states__fog_end__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__front_face__AttributeData
{
    static const profile_GLES__technique__pass__states__front_face__AttributeData DEFAULT;

    ENUM__gl_front_face_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__logic_op__AttributeData
{
    static const profile_GLES__technique__pass__states__logic_op__AttributeData DEFAULT;

    ENUM__gl_logic_op_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__light_ambient__AttributeData
{
    static const profile_GLES__technique__pass__states__light_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_diffuse__AttributeData
{
    static const profile_GLES__technique__pass__states__light_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_specular__AttributeData
{
    static const profile_GLES__technique__pass__states__light_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_position__AttributeData
{
    static const profile_GLES__technique__pass__states__light_position__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_constant_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__states__light_constant_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_linear_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__states__light_linear_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_quadratic_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__states__light_quadratic_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_spot_cutoff__AttributeData
{
    static const profile_GLES__technique__pass__states__light_spot_cutoff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_spot_direction__AttributeData
{
    static const profile_GLES__technique__pass__states__light_spot_direction__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_spot_exponent__AttributeData
{
    static const profile_GLES__technique__pass__states__light_spot_exponent__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__light_model_ambient__AttributeData
{
    static const profile_GLES__technique__pass__states__light_model_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__line_width__AttributeData
{
    static const profile_GLES__technique__pass__states__line_width__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__material_ambient__AttributeData
{
    static const profile_GLES__technique__pass__states__material_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__material_diffuse__AttributeData
{
    static const profile_GLES__technique__pass__states__material_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__material_emission__AttributeData
{
    static const profile_GLES__technique__pass__states__material_emission__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__material_shininess__AttributeData
{
    static const profile_GLES__technique__pass__states__material_shininess__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__material_specular__AttributeData
{
    static const profile_GLES__technique__pass__states__material_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__model_view_matrix__AttributeData
{
    static const profile_GLES__technique__pass__states__model_view_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__point_distance_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__states__point_distance_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__point_fade_threshold_size__AttributeData
{
    static const profile_GLES__technique__pass__states__point_fade_threshold_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__point_size__AttributeData
{
    static const profile_GLES__technique__pass__states__point_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__point_size_min__AttributeData
{
    static const profile_GLES__technique__pass__states__point_size_min__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__point_size_max__AttributeData
{
    static const profile_GLES__technique__pass__states__point_size_max__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__polygon_offset__AttributeData
{
    static const profile_GLES__technique__pass__states__polygon_offset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__projection_matrix__AttributeData
{
    static const profile_GLES__technique__pass__states__projection_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__scissor__AttributeData
{
    static const profile_GLES__technique__pass__states__scissor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__shade_model__AttributeData
{
    static const profile_GLES__technique__pass__states__shade_model__AttributeData DEFAULT;

    ENUM__gl_shade_model_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_func__func__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_func__func__AttributeData DEFAULT;

    ENUM__gl_func_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_func__ref__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_func__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_func__mask__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_func__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_mask__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_mask__AttributeData DEFAULT;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_op__fail__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_op__fail__AttributeData DEFAULT;

    ENUM__gles_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_op__zfail__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_op__zfail__AttributeData DEFAULT;

    ENUM__gles_stencil_op_enum value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_op__zpass__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_op__zpass__AttributeData DEFAULT;

    ENUM__gles_stencil_op_enum value;
    const ParserChar* param;
};

struct value____gles_texture_pipeline_type__AttributeData
{
    static const value____gles_texture_pipeline_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct constant____gles_texture_constant_type__AttributeData
{
    static const constant____gles_texture_constant_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct RGB__AttributeData
{
    static const RGB__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SCALE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__gles_texcombiner_operator_rgb_enum _operator;
    float scale;
};

struct argument____gles_texcombiner_argument_rgb_type__AttributeData
{
    static const argument____gles_texcombiner_argument_rgb_type__AttributeData DEFAULT;

    ENUM__gles_texcombiner_source_enum source;
    ENUM__gles_texcombiner_operand_rgb_enum operand;
    const ParserChar* sampler;
};

struct alpha____gles_texcombiner_command_alpha_type__AttributeData
{
    static const alpha____gles_texcombiner_command_alpha_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SCALE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__gles_texcombiner_operator_alpha_enum _operator;
    float scale;
};

struct argument____gles_texcombiner_argument_alpha_type__AttributeData
{
    static const argument____gles_texcombiner_argument_alpha_type__AttributeData DEFAULT;

    ENUM__gles_texcombiner_source_enum source;
    ENUM__gles_texcombiner_operand_alpha_enum operand;
    const ParserChar* sampler;
};

struct texenv__AttributeData
{
    static const texenv__AttributeData DEFAULT;

    ENUM__gles_texenv_mode_enum _operator;
    const ParserChar* sampler;
};

struct profile_GLES__technique__pass__states__alpha_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__alpha_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__blend_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__blend_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__clip_plane_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__clip_plane_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__color_logic_op_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__color_logic_op_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__color_material_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__color_material_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__cull_face_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__cull_face_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__depth_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__depth_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__dither_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__dither_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__fog_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__fog_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__light_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__light_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__states__lighting_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__lighting_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__light_model_two_side_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__light_model_two_side_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__line_smooth_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__line_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__multisample_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__multisample_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__normalize_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__normalize_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__point_smooth_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__point_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__polygon_offset_fill_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__polygon_offset_fill_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__rescale_normal_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__rescale_normal_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__sample_alpha_to_coverage_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__sample_alpha_to_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__sample_alpha_to_one_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__sample_alpha_to_one_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__sample_coverage_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__sample_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__scissor_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__scissor_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__states__stencil_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__states__stencil_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct library_force_fields__AttributeData
{
    static const library_force_fields__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct force_field__AttributeData
{
    static const force_field__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct library_images__AttributeData
{
    static const library_images__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct image____image_type__AttributeData
{
    static const image____image_type__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
    const ParserChar* name;
};

struct renderable__AttributeData
{
    static const renderable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SHARE_PRESENT = 0x1;

    uint32 present_attributes;

    bool share;
};

struct image_type____init_from__AttributeData
{
    static const image_type____init_from__AttributeData DEFAULT;

    bool mips_generate;
};

struct library_images__image__init_from__hex__AttributeData
{
    static const library_images__image__init_from__hex__AttributeData DEFAULT;

    const ParserChar* format;
};

struct size_exact__AttributeData
{
    static const size_exact__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_WIDTH_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HEIGHT_PRESENT = 0x2;

    uint32 present_attributes;

    uint32 width;
    uint32 height;
};

struct size_ratio__AttributeData
{
    static const size_ratio__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_WIDTH_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HEIGHT_PRESENT = 0x2;

    uint32 present_attributes;

    float width;
    float height;
};

struct mips__AttributeData
{
    static const mips__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LEVELS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_AUTO_GENERATE_PRESENT = 0x2;

    uint32 present_attributes;

    uint32 levels;
    bool auto_generate;
};

struct unnormalized__AttributeData
{
    static const unnormalized__AttributeData DEFAULT;


    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct create_2d__array__AttributeData
{
    static const create_2d__array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct create_2d__format__hint__AttributeData
{
    static const create_2d__format__hint__AttributeData DEFAULT;

    ENUM__image_format_hint_channels_enum channels;
    ENUM__image_format_hint_range_enum range;
    ENUM__image_format_hint_precision_enum precision;
    const ParserChar* space;
};

struct create_2d__init_from__AttributeData
{
    static const create_2d__init_from__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_MIP_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint32 mip_index;
    uint32 array_index;
};

struct create_3d__size__AttributeData
{
    static const create_3d__size__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_WIDTH_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HEIGHT_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_DEPTH_PRESENT = 0x4;

    uint32 present_attributes;

    uint32 width;
    uint32 height;
    uint32 depth;
};

struct create_3d__array__AttributeData
{
    static const create_3d__array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint32 length;
};

struct create_3d__format__hint__AttributeData
{
    static const create_3d__format__hint__AttributeData DEFAULT;

    ENUM__image_format_hint_channels_enum channels;
    ENUM__image_format_hint_range_enum range;
    ENUM__image_format_hint_precision_enum precision;
    const ParserChar* space;
};

struct create_3d__init_from__AttributeData
{
    static const create_3d__init_from__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEPTH_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_MIP_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    uint32 depth;
    uint32 mip_index;
    uint32 array_index;
};

struct create_cube__size__AttributeData
{
    static const create_cube__size__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_WIDTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint32 width;
};

struct create_cube__array__AttributeData
{
    static const create_cube__array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint32 length;
};

struct create_cube__format__hint__AttributeData
{
    static const create_cube__format__hint__AttributeData DEFAULT;

    ENUM__image_format_hint_channels_enum channels;
    ENUM__image_format_hint_range_enum range;
    ENUM__image_format_hint_precision_enum precision;
    const ParserChar* space;
};

struct create_cube__init_from__AttributeData
{
    static const create_cube__init_from__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_MIP_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__image_face_enum face;
    uint32 mip_index;
    uint32 array_index;
};

struct library_lights__AttributeData
{
    static const library_lights__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct light__AttributeData
{
    static const light__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct color____targetable_float3_type__AttributeData
{
    static const color____targetable_float3_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct constant_attenuation__AttributeData
{
    static const constant_attenuation__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct linear_attenuation__AttributeData
{
    static const linear_attenuation__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct quadratic_attenuation__AttributeData
{
    static const quadratic_attenuation__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct falloff_angle__AttributeData
{
    static const falloff_angle__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct falloff_exponent__AttributeData
{
    static const falloff_exponent__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_materials__AttributeData
{
    static const library_materials__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct material__AttributeData
{
    static const material__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct instance_effect__AttributeData
{
    static const instance_effect__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct technique_hint__AttributeData
{
    static const technique_hint__AttributeData DEFAULT;

    const ParserChar* platform;
    const ParserChar* profile;
    const ParserChar* ref;
};

struct instance_effect_type____setparam__AttributeData
{
    static const instance_effect_type____setparam__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct sampler_image__AttributeData
{
    static const sampler_image__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct library_nodes__AttributeData
{
    static const library_nodes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct node__AttributeData
{
    static const node__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LAYER_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* sid;
    ENUM__node_enum type;
    GeneratedSaxParser::XSList<ParserString> layer;
};

struct lookat__AttributeData
{
    static const lookat__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct matrix____matrix_type__AttributeData
{
    static const matrix____matrix_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct rotate__AttributeData
{
    static const rotate__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct scale__AttributeData
{
    static const scale__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct skew__AttributeData
{
    static const skew__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct translate__AttributeData
{
    static const translate__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_camera__AttributeData
{
    static const instance_camera__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_controller__AttributeData
{
    static const instance_controller__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_material____instance_material_type__AttributeData
{
    static const instance_material____instance_material_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_TARGET_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* symbol;
    COLLADABU::URI target;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_material_type____bind__AttributeData
{
    static const instance_material_type____bind__AttributeData DEFAULT;

    const ParserChar* semantic;
    const ParserChar* target;
};

struct bind_vertex_input__AttributeData
{
    static const bind_vertex_input__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INPUT_SET_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* semantic;
    const ParserChar* input_semantic;
    uint64 input_set;
};

struct instance_geometry__AttributeData
{
    static const instance_geometry__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_light__AttributeData
{
    static const instance_light__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_node__AttributeData
{
    static const instance_node__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_PROXY_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_URL_PRESENT = 0x2;

    uint32 present_attributes;

    COLLADABU::URI proxy;
    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct library_physics_materials__AttributeData
{
    static const library_physics_materials__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct physics_material__AttributeData
{
    static const physics_material__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct dynamic_friction__AttributeData
{
    static const dynamic_friction__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct restitution__AttributeData
{
    static const restitution__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct static_friction__AttributeData
{
    static const static_friction__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_physics_models__AttributeData
{
    static const library_physics_models__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct physics_model__AttributeData
{
    static const physics_model__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct rigid_body__AttributeData
{
    static const rigid_body__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
    const ParserChar* name;
};

struct rigid_body__technique_common__dynamic__AttributeData
{
    static const rigid_body__technique_common__dynamic__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct mass__AttributeData
{
    static const mass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct inertia__AttributeData
{
    static const inertia__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_physics_material__AttributeData
{
    static const instance_physics_material__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct rigid_body__technique_common__shape__hollow__AttributeData
{
    static const rigid_body__technique_common__shape__hollow__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct density__AttributeData
{
    static const density__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct rigid_constraint__AttributeData
{
    static const rigid_constraint__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct ref_attachment__AttributeData
{
    static const ref_attachment__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_RIGID_BODY_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI rigid_body;
};

struct attachment__AttributeData
{
    static const attachment__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_RIGID_BODY_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI rigid_body;
};

struct enabled__AttributeData
{
    static const enabled__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct interpenetrate__AttributeData
{
    static const interpenetrate__AttributeData DEFAULT;

    const ParserChar* sid;
};

typedef inertia__AttributeData min____targetable_float3_type__AttributeData;

typedef inertia__AttributeData max____targetable_float3_type__AttributeData;

struct stiffness__AttributeData
{
    static const stiffness__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct damping__AttributeData
{
    static const damping__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct target_value__AttributeData
{
    static const target_value__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_physics_model__AttributeData
{
    static const instance_physics_model__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_PARENT_PRESENT = 0x2;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
    COLLADABU::URI parent;
};

struct instance_force_field__AttributeData
{
    static const instance_force_field__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_rigid_body__AttributeData
{
    static const instance_rigid_body__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_TARGET_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* body;
    const ParserChar* sid;
    const ParserChar* name;
    COLLADABU::URI target;
};

struct instance_rigid_body__technique_common__dynamic__AttributeData
{
    static const instance_rigid_body__technique_common__dynamic__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_rigid_body__technique_common__shape__hollow__AttributeData
{
    static const instance_rigid_body__technique_common__shape__hollow__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_rigid_constraint__AttributeData
{
    static const instance_rigid_constraint__AttributeData DEFAULT;

    const ParserChar* constraint;
    const ParserChar* sid;
    const ParserChar* name;
};

struct library_physics_scenes__AttributeData
{
    static const library_physics_scenes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct physics_scene__AttributeData
{
    static const physics_scene__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct gravity__AttributeData
{
    static const gravity__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct time_step__AttributeData
{
    static const time_step__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_visual_scenes__AttributeData
{
    static const library_visual_scenes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct visual_scene__AttributeData
{
    static const visual_scene__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct evaluate_scene__AttributeData
{
    static const evaluate_scene__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
    const ParserChar* name;
    bool enable;
};

struct render__AttributeData
{
    static const render__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_CAMERA_NODE_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* sid;
    const ParserChar* name;
    COLLADABU::URI camera_node;
};

struct render__instance_material__AttributeData
{
    static const render__instance_material__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
};

struct technique_override__AttributeData
{
    static const technique_override__AttributeData DEFAULT;

    const ParserChar* ref;
    const ParserChar* pass;
};

struct render__instance_material__bind__AttributeData
{
    static const render__instance_material__bind__AttributeData DEFAULT;

    const ParserChar* semantic;
    const ParserChar* target;
};

struct library_joints__AttributeData
{
    static const library_joints__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct joint__AttributeData
{
    static const joint__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* sid;
};

struct prismatic__AttributeData
{
    static const prismatic__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct axis____axis_type__AttributeData
{
    static const axis____axis_type__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct min____minmax_type__AttributeData
{
    static const min____minmax_type__AttributeData DEFAULT;

    const ParserChar* name;
    const ParserChar* sid;
};

typedef min____minmax_type__AttributeData max____minmax_type__AttributeData;

struct revolute__AttributeData
{
    static const revolute__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_kinematics_models__AttributeData
{
    static const library_kinematics_models__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct kinematics_model__AttributeData
{
    static const kinematics_model__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct newparam____kinematics_newparam_type__AttributeData
{
    static const newparam____kinematics_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_joint__AttributeData
{
    static const instance_joint__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct link__AttributeData
{
    static const link__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct attachment_full__AttributeData
{
    static const attachment_full__AttributeData DEFAULT;

    const ParserChar* joint;
};

struct attachment_start__AttributeData
{
    static const attachment_start__AttributeData DEFAULT;

    const ParserChar* joint;
};

struct attachment_end__AttributeData
{
    static const attachment_end__AttributeData DEFAULT;

    const ParserChar* joint;
};

struct formula__AttributeData
{
    static const formula__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* sid;
};

struct newparam____formula_newparam_type__AttributeData
{
    static const newparam____formula_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct math__AttributeData
{
    static const math__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_ALTIMG_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* baseline;
    ENUM__mathml__overflow overflow;
    COLLADABU::URI altimg;
    const ParserChar* alttext;
    const ParserChar* type;
    const ParserChar* name;
    const ParserChar* height;
    const ParserChar* width;
    const ParserChar* macros;
    ENUM__mathml__display display;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct cn__AttributeData
{
    static const cn__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_BASE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x2;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x4;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x8;

    uint32 present_attributes;

    uint64 base;
    ENUM__mathml__cn__type type;
    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct ci__AttributeData
{
    static const ci__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* type;
    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct csymbol__AttributeData
{
    static const csymbol__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct abs__AttributeData
{
    static const abs__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct conjugate__AttributeData
{
    static const conjugate__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct factorial__AttributeData
{
    static const factorial__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arg__AttributeData
{
    static const arg__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct real__AttributeData
{
    static const real__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct imaginary__AttributeData
{
    static const imaginary__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct floor__AttributeData
{
    static const floor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct ceiling__AttributeData
{
    static const ceiling__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct quotient__AttributeData
{
    static const quotient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct divide__AttributeData
{
    static const divide__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct rem__AttributeData
{
    static const rem__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct minus__AttributeData
{
    static const minus__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct plus__AttributeData
{
    static const plus__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct times__AttributeData
{
    static const times__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct power__AttributeData
{
    static const power__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct root__AttributeData
{
    static const root__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

typedef root__AttributeData max____Arith_type__AttributeData;

typedef root__AttributeData min____Arith_type__AttributeData;

struct gcd__AttributeData
{
    static const gcd__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct lcm__AttributeData
{
    static const lcm__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct sum__AttributeData
{
    static const sum__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct product__AttributeData
{
    static const product__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct compose__AttributeData
{
    static const compose__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct domain__AttributeData
{
    static const domain__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct codomain__AttributeData
{
    static const codomain__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

typedef codomain__AttributeData image____Functions_type__AttributeData;

struct domainofapplication__AttributeData
{
    static const domainofapplication__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct ident__AttributeData
{
    static const ident__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct and__AttributeData
{
    static const and__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct or__AttributeData
{
    static const or__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct xor__AttributeData
{
    static const xor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct not__AttributeData
{
    static const not__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct exists__AttributeData
{
    static const exists__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct forall__AttributeData
{
    static const forall__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct implies__AttributeData
{
    static const implies__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct naturalnumbers__AttributeData
{
    static const naturalnumbers__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct primes__AttributeData
{
    static const primes__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct integers__AttributeData
{
    static const integers__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct rationals__AttributeData
{
    static const rationals__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct reals__AttributeData
{
    static const reals__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct complexes__AttributeData
{
    static const complexes__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct emptyset__AttributeData
{
    static const emptyset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct exponentiale__AttributeData
{
    static const exponentiale__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct imaginaryi__AttributeData
{
    static const imaginaryi__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct pi__AttributeData
{
    static const pi__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct eulergamma__AttributeData
{
    static const eulergamma__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct true__AttributeData
{
    static const true__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct false__AttributeData
{
    static const false__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct infinity__AttributeData
{
    static const infinity__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct notanumber__AttributeData
{
    static const notanumber__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct set__AttributeData
{
    static const set__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    const ParserChar* type;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct list__AttributeData
{
    static const list__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    ENUM__mathml__list__order order;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct union__AttributeData
{
    static const union__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct intersect__AttributeData
{
    static const intersect__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct in__AttributeData
{
    static const in__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct notin__AttributeData
{
    static const notin__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct subset__AttributeData
{
    static const subset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct prsubset__AttributeData
{
    static const prsubset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct notsubset__AttributeData
{
    static const notsubset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct notprsubset__AttributeData
{
    static const notprsubset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct setdiff__AttributeData
{
    static const setdiff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct card__AttributeData
{
    static const card__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct cartesianproduct__AttributeData
{
    static const cartesianproduct__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct eq__AttributeData
{
    static const eq__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct neq__AttributeData
{
    static const neq__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct leq__AttributeData
{
    static const leq__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct lt__AttributeData
{
    static const lt__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct geq__AttributeData
{
    static const geq__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct gt__AttributeData
{
    static const gt__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct equivalent__AttributeData
{
    static const equivalent__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct approx__AttributeData
{
    static const approx__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct factorof__AttributeData
{
    static const factorof__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct exp__AttributeData
{
    static const exp__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct ln__AttributeData
{
    static const ln__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct log__AttributeData
{
    static const log__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct logbase__AttributeData
{
    static const logbase__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct sin__AttributeData
{
    static const sin__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct cos__AttributeData
{
    static const cos__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct tan__AttributeData
{
    static const tan__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct sec__AttributeData
{
    static const sec__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct csc__AttributeData
{
    static const csc__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct cot__AttributeData
{
    static const cot__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arcsin__AttributeData
{
    static const arcsin__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arccos__AttributeData
{
    static const arccos__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arctan__AttributeData
{
    static const arctan__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arcsec__AttributeData
{
    static const arcsec__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arccsc__AttributeData
{
    static const arccsc__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arccot__AttributeData
{
    static const arccot__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct sinh__AttributeData
{
    static const sinh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct cosh__AttributeData
{
    static const cosh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct tanh__AttributeData
{
    static const tanh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct sech__AttributeData
{
    static const sech__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct csch__AttributeData
{
    static const csch__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct coth__AttributeData
{
    static const coth__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arccosh__AttributeData
{
    static const arccosh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arccoth__AttributeData
{
    static const arccoth__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arccsch__AttributeData
{
    static const arccsch__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arcsech__AttributeData
{
    static const arcsech__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arcsinh__AttributeData
{
    static const arcsinh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct arctanh__AttributeData
{
    static const arctanh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct int____int_type____mathml__AttributeData
{
    static const int____int_type____mathml__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct diff__AttributeData
{
    static const diff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct partialdiff__AttributeData
{
    static const partialdiff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct limit__AttributeData
{
    static const limit__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct lowlimit__AttributeData
{
    static const lowlimit__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct uplimit__AttributeData
{
    static const uplimit__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct tendsto__AttributeData
{
    static const tendsto__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* type;
    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct vector__AttributeData
{
    static const vector__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct matrix____matrix_type____mathml__AttributeData
{
    static const matrix____matrix_type____mathml__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct matrixrow__AttributeData
{
    static const matrixrow__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct determinant__AttributeData
{
    static const determinant__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct transpose__AttributeData
{
    static const transpose__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct selector__AttributeData
{
    static const selector__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct vectorproduct__AttributeData
{
    static const vectorproduct__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct scalarproduct__AttributeData
{
    static const scalarproduct__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct outerproduct__AttributeData
{
    static const outerproduct__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct divergence__AttributeData
{
    static const divergence__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct grad__AttributeData
{
    static const grad__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct curl__AttributeData
{
    static const curl__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct laplacian__AttributeData
{
    static const laplacian__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct mean__AttributeData
{
    static const mean__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct sdev__AttributeData
{
    static const sdev__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct variance__AttributeData
{
    static const variance__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct median__AttributeData
{
    static const median__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct mode____mode_type__AttributeData
{
    static const mode____mode_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct moment__AttributeData
{
    static const moment__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct momentabout__AttributeData
{
    static const momentabout__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct apply__AttributeData
{
    static const apply__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct interval__AttributeData
{
    static const interval__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    ENUM__mathml__interval__closure closure;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct inverse__AttributeData
{
    static const inverse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct condition__AttributeData
{
    static const condition__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
};

struct declare__AttributeData
{
    static const declare__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_NARGS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x2;

    uint32 present_attributes;

    const ParserChar* type;
    const ParserChar* scope;
    uint64 nargs;
    ENUM__mathml__declare__occurrence occurrence;
    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
};

struct lambda__AttributeData
{
    static const lambda__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct piecewise__AttributeData
{
    static const piecewise__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct bvar__AttributeData
{
    static const bvar__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct degree__AttributeData
{
    static const degree__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct semantics__AttributeData
{
    static const semantics__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_DEFINITIONURL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x2;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x4;

    uint32 present_attributes;

    const ParserChar* encoding;
    COLLADABU::URI definitionURL;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct annotation__AttributeData
{
    static const annotation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    const ParserChar* encoding;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct annotation_xml__AttributeData
{
    static const annotation_xml__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    const ParserChar* encoding;
    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct otherwise__AttributeData
{
    static const otherwise__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE__CLASS_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_HREF_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<ParserString> _class;
    const ParserChar* style;
    const ParserChar* xref;
    const ParserChar* id;
    COLLADABU::URI href;

    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct library_articulated_systems__AttributeData
{
    static const library_articulated_systems__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct articulated_system__AttributeData
{
    static const articulated_system__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct instance_kinematics_model__AttributeData
{
    static const instance_kinematics_model__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct bind____kinematics_bind_type__AttributeData
{
    static const bind____kinematics_bind_type__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct param____kinematics_param_type__AttributeData
{
    static const param____kinematics_param_type__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct setparam____kinematics_setparam_type__AttributeData
{
    static const setparam____kinematics_setparam_type__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct axis_info____kinematics_axis_info_type__AttributeData
{
    static const axis_info____kinematics_axis_info_type__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
    const ParserChar* axis;
};

struct index__AttributeData
{
    static const index__AttributeData DEFAULT;

    const ParserChar* semantic;
};

struct frame_origin__AttributeData
{
    static const frame_origin__AttributeData DEFAULT;

    const ParserChar* link;
};

struct frame_tip__AttributeData
{
    static const frame_tip__AttributeData DEFAULT;

    const ParserChar* link;
};

struct frame_tcp__AttributeData
{
    static const frame_tcp__AttributeData DEFAULT;

    const ParserChar* link;
};

struct frame_object__AttributeData
{
    static const frame_object__AttributeData DEFAULT;

    const ParserChar* link;
};

struct instance_articulated_system__AttributeData
{
    static const instance_articulated_system__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* sid;
    COLLADABU::URI url;
    const ParserChar* name;
};

struct axis_info____motion_axis_info_type__AttributeData
{
    static const axis_info____motion_axis_info_type__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* axis;
    const ParserChar* name;
};

struct effector_info__AttributeData
{
    static const effector_info__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct library_kinematics_scenes__AttributeData
{
    static const library_kinematics_scenes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct kinematics_scene__AttributeData
{
    static const kinematics_scene__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct library_formulas__AttributeData
{
    static const library_formulas__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct instance_physics_scene__AttributeData
{
    static const instance_physics_scene__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_visual_scene__AttributeData
{
    static const instance_visual_scene__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_kinematics_scene__AttributeData
{
    static const instance_kinematics_scene__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct bind_kinematics_model__AttributeData
{
    static const bind_kinematics_model__AttributeData DEFAULT;

    const ParserChar* node;
};

struct bind_joint_axis__AttributeData
{
    static const bind_joint_axis__AttributeData DEFAULT;

    const ParserChar* target;
};



} // namespace
#endif
