/*
    Copyright (c) 2008-2013 NetAllied Systems GmbH

    This file is part of COLLADASaxFrameworkLoader.

    Licensed under the MIT Open Source License,
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/



#ifndef __COLLADASAXFWL14_COLLADAPARSERAUTOGEN14ATTRIBUTES_H__
#define __COLLADASAXFWL14_COLLADAPARSERAUTOGEN14ATTRIBUTES_H__


#include "COLLADASaxFWLColladaParserAutoGen14Enums.h"


namespace COLLADASaxFWL14
{


extern const char* NAME_NAMESPACE_COLLADA;
const StringHash HASH_NAMESPACE_COLLADA = 221035537;
extern const char* NAME_NAMESPACE_http___www_w3_org_2001_XMLSchema;
const StringHash HASH_NAMESPACE_http___www_w3_org_2001_XMLSchema = 9996177;
extern const char* NAME_NAMESPACE_http___www_w3_org_XML_1998_namespace;
const StringHash HASH_NAMESPACE_http___www_w3_org_XML_1998_namespace = 153133749;


extern const char* NAME_ATTRIBUTE_base;
extern const char* NAME_ATTRIBUTE_body;
extern const char* NAME_ATTRIBUTE_camera_node;
extern const char* NAME_ATTRIBUTE_closed;
extern const char* NAME_ATTRIBUTE_constraint;
extern const char* NAME_ATTRIBUTE_convex_hull_of;
extern const char* NAME_ATTRIBUTE_count;
extern const char* NAME_ATTRIBUTE_depth;
extern const char* NAME_ATTRIBUTE_digits;
extern const char* NAME_ATTRIBUTE_end;
extern const char* NAME_ATTRIBUTE_face;
extern const char* NAME_ATTRIBUTE_format;
extern const char* NAME_ATTRIBUTE_height;
extern const char* NAME_ATTRIBUTE_id;
extern const char* NAME_ATTRIBUTE_index;
extern const char* NAME_ATTRIBUTE_input_semantic;
extern const char* NAME_ATTRIBUTE_input_set;
extern const char* NAME_ATTRIBUTE_layer;
extern const char* NAME_ATTRIBUTE_length;
extern const char* NAME_ATTRIBUTE_magnitude;
extern const char* NAME_ATTRIBUTE_material;
extern const char* NAME_ATTRIBUTE_maxInclusive;
extern const char* NAME_ATTRIBUTE_meter;
extern const char* NAME_ATTRIBUTE_method;
extern const char* NAME_ATTRIBUTE_minInclusive;
extern const char* NAME_ATTRIBUTE_mip;
extern const char* NAME_ATTRIBUTE_name;
extern const char* NAME_ATTRIBUTE_offset;
extern const char* NAME_ATTRIBUTE_opaque;
extern const char* NAME_ATTRIBUTE_operand;
extern const char* NAME_ATTRIBUTE_operator;
extern const char* NAME_ATTRIBUTE_param;
extern const char* NAME_ATTRIBUTE_parent;
extern const char* NAME_ATTRIBUTE_platform;
extern const char* NAME_ATTRIBUTE_profile;
extern const char* NAME_ATTRIBUTE_program;
extern const char* NAME_ATTRIBUTE_ref;
extern const char* NAME_ATTRIBUTE_rigid_body;
extern const char* NAME_ATTRIBUTE_scale;
extern const char* NAME_ATTRIBUTE_semantic;
extern const char* NAME_ATTRIBUTE_set;
extern const char* NAME_ATTRIBUTE_sid;
extern const char* NAME_ATTRIBUTE_slice;
extern const char* NAME_ATTRIBUTE_source;
extern const char* NAME_ATTRIBUTE_stage;
extern const char* NAME_ATTRIBUTE_start;
extern const char* NAME_ATTRIBUTE_stride;
extern const char* NAME_ATTRIBUTE_symbol;
extern const char* NAME_ATTRIBUTE_target;
extern const char* NAME_ATTRIBUTE_texcoord;
extern const char* NAME_ATTRIBUTE_texture;
extern const char* NAME_ATTRIBUTE_type;
extern const char* NAME_ATTRIBUTE_unit;
extern const char* NAME_ATTRIBUTE_url;
extern const char* NAME_ATTRIBUTE_value;
extern const char* NAME_ATTRIBUTE_version;
extern const char* NAME_ATTRIBUTE_width;
extern const char* NAME_ATTRIBUTE_xmlns;
extern const char* NAME_ELEMENT_ACCESSOR;
extern const char* NAME_ELEMENT_ALL;
extern const char* NAME_ELEMENT_ALPHA;
extern const char* NAME_ELEMENT_ALPHA_FUNC;
extern const char* NAME_ELEMENT_ALPHA_TEST_ENABLE;
extern const char* NAME_ELEMENT_ALPHA____GLES_TEXCOMBINER_COMMANDALPHA_TYPE;
extern const char* NAME_ELEMENT_AMBIENT;
extern const char* NAME_ELEMENT_AMBIENT____COMMON_COLOR_OR_TEXTURE_TYPE;
extern const char* NAME_ELEMENT_ANGULAR;
extern const char* NAME_ELEMENT_ANGULAR_VELOCITY;
extern const char* NAME_ELEMENT_ANIMATION;
extern const char* NAME_ELEMENT_ANIMATION_CLIP;
extern const char* NAME_ELEMENT_ANIMATION__SOURCE__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_ANNOTATE;
extern const char* NAME_ELEMENT_ARGUMENT;
extern const char* NAME_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENTALPHA_TYPE;
extern const char* NAME_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENTRGB_TYPE;
extern const char* NAME_ELEMENT_ARRAY;
extern const char* NAME_ELEMENT_ARRAY____CG_NEWARRAY_TYPE;
extern const char* NAME_ELEMENT_ARRAY____CG_SETARRAY_TYPE;
extern const char* NAME_ELEMENT_ARRAY____GLSL_NEWARRAY_TYPE;
extern const char* NAME_ELEMENT_ARRAY____GLSL_SETARRAY_TYPE;
extern const char* NAME_ELEMENT_ASPECT_RATIO;
extern const char* NAME_ELEMENT_ASSET;
extern const char* NAME_ELEMENT_ATTACHMENT;
extern const char* NAME_ELEMENT_AUTHOR;
extern const char* NAME_ELEMENT_AUTHORING_TOOL;
extern const char* NAME_ELEMENT_AUTO_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_BACK;
extern const char* NAME_ELEMENT_BIND;
extern const char* NAME_ELEMENT_BIND_MATERIAL;
extern const char* NAME_ELEMENT_BIND_MATERIAL__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_BIND_SHAPE_MATRIX;
extern const char* NAME_ELEMENT_BIND_VERTEX_INPUT;
extern const char* NAME_ELEMENT_BLEND_COLOR;
extern const char* NAME_ELEMENT_BLEND_ENABLE;
extern const char* NAME_ELEMENT_BLEND_EQUATION;
extern const char* NAME_ELEMENT_BLEND_EQUATION_SEPARATE;
extern const char* NAME_ELEMENT_BLEND_EQUATION_SEPARATE__ALPHA;
extern const char* NAME_ELEMENT_BLEND_FUNC;
extern const char* NAME_ELEMENT_BLEND_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_BLINN;
extern const char* NAME_ELEMENT_BOOL;
extern const char* NAME_ELEMENT_BOOL1;
extern const char* NAME_ELEMENT_BOOL1X1;
extern const char* NAME_ELEMENT_BOOL1X2;
extern const char* NAME_ELEMENT_BOOL1X3;
extern const char* NAME_ELEMENT_BOOL1X4;
extern const char* NAME_ELEMENT_BOOL2;
extern const char* NAME_ELEMENT_BOOL2X1;
extern const char* NAME_ELEMENT_BOOL2X2;
extern const char* NAME_ELEMENT_BOOL2X3;
extern const char* NAME_ELEMENT_BOOL2X4;
extern const char* NAME_ELEMENT_BOOL2____BOOL2;
extern const char* NAME_ELEMENT_BOOL2____CG_BOOL2;
extern const char* NAME_ELEMENT_BOOL2____GLSL_BOOL2;
extern const char* NAME_ELEMENT_BOOL3;
extern const char* NAME_ELEMENT_BOOL3X1;
extern const char* NAME_ELEMENT_BOOL3X2;
extern const char* NAME_ELEMENT_BOOL3X3;
extern const char* NAME_ELEMENT_BOOL3X4;
extern const char* NAME_ELEMENT_BOOL3____BOOL3;
extern const char* NAME_ELEMENT_BOOL3____CG_BOOL3;
extern const char* NAME_ELEMENT_BOOL3____GLSL_BOOL3;
extern const char* NAME_ELEMENT_BOOL4;
extern const char* NAME_ELEMENT_BOOL4X1;
extern const char* NAME_ELEMENT_BOOL4X2;
extern const char* NAME_ELEMENT_BOOL4X3;
extern const char* NAME_ELEMENT_BOOL4X4;
extern const char* NAME_ELEMENT_BOOL4____BOOL4;
extern const char* NAME_ELEMENT_BOOL4____CG_BOOL4;
extern const char* NAME_ELEMENT_BOOL4____GLSL_BOOL4;
extern const char* NAME_ELEMENT_BOOL_ARRAY;
extern const char* NAME_ELEMENT_BOOL____BOOL;
extern const char* NAME_ELEMENT_BOOL____CG_BOOL;
extern const char* NAME_ELEMENT_BOOL____GLSL_BOOL;
extern const char* NAME_ELEMENT_BORDER_COLOR;
extern const char* NAME_ELEMENT_BOX;
extern const char* NAME_ELEMENT_CAMERA;
extern const char* NAME_ELEMENT_CAPSULE;
extern const char* NAME_ELEMENT_CG_SURFACE_TYPE____GENERATOR;
extern const char* NAME_ELEMENT_CHANNEL;
extern const char* NAME_ELEMENT_CHANNELS;
extern const char* NAME_ELEMENT_CLEAR_COLOR;
extern const char* NAME_ELEMENT_CLEAR_DEPTH;
extern const char* NAME_ELEMENT_CLEAR_STENCIL;
extern const char* NAME_ELEMENT_CLIP_PLANE;
extern const char* NAME_ELEMENT_CLIP_PLANE_ENABLE;
extern const char* NAME_ELEMENT_CODE;
extern const char* NAME_ELEMENT_COLLADA;
extern const char* NAME_ELEMENT_COLOR;
extern const char* NAME_ELEMENT_COLOR_CLEAR;
extern const char* NAME_ELEMENT_COLOR_CLEAR____FX_CLEARCOLOR_COMMON;
extern const char* NAME_ELEMENT_COLOR_CLEAR____FX_COLOR_COMMON;
extern const char* NAME_ELEMENT_COLOR_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_COLOR_MASK;
extern const char* NAME_ELEMENT_COLOR_MATERIAL;
extern const char* NAME_ELEMENT_COLOR_MATERIAL_ENABLE;
extern const char* NAME_ELEMENT_COLOR_MATERIAL__FACE;
extern const char* NAME_ELEMENT_COLOR_MATERIAL__MODE;
extern const char* NAME_ELEMENT_COLOR_TARGET;
extern const char* NAME_ELEMENT_COLOR_TARGET____FX_COLORTARGET_COMMON;
extern const char* NAME_ELEMENT_COLOR_TARGET____GLES_RENDERTARGET_COMMON;
extern const char* NAME_ELEMENT_COLOR____TARGETABLEFLOAT3;
extern const char* NAME_ELEMENT_COMMENTS;
extern const char* NAME_ELEMENT_COMMON_COLOR_OR_TEXTURE_TYPE____COLOR;
extern const char* NAME_ELEMENT_COMMON_COLOR_OR_TEXTURE_TYPE____PARAM;
extern const char* NAME_ELEMENT_COMMON_FLOAT_OR_PARAM_TYPE____FLOAT;
extern const char* NAME_ELEMENT_COMMON_FLOAT_OR_PARAM_TYPE____PARAM;
extern const char* NAME_ELEMENT_COMPILER_OPTIONS;
extern const char* NAME_ELEMENT_COMPILER_TARGET;
extern const char* NAME_ELEMENT_CONNECT_PARAM;
extern const char* NAME_ELEMENT_CONSTANT;
extern const char* NAME_ELEMENT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_CONSTANT____GLES_TEXTURE_CONSTANT_TYPE;
extern const char* NAME_ELEMENT_CONTRIBUTOR;
extern const char* NAME_ELEMENT_CONTROLLER;
extern const char* NAME_ELEMENT_CONTROL_VERTICES;
extern const char* NAME_ELEMENT_CONVEX_MESH;
extern const char* NAME_ELEMENT_COPYRIGHT;
extern const char* NAME_ELEMENT_CREATED;
extern const char* NAME_ELEMENT_CULL_FACE;
extern const char* NAME_ELEMENT_CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_CYLINDER;
extern const char* NAME_ELEMENT_DAMPING;
extern const char* NAME_ELEMENT_DATA;
extern const char* NAME_ELEMENT_DENSITY;
extern const char* NAME_ELEMENT_DEPTH_BOUNDS;
extern const char* NAME_ELEMENT_DEPTH_BOUNDS_ENABLE;
extern const char* NAME_ELEMENT_DEPTH_CLAMP_ENABLE;
extern const char* NAME_ELEMENT_DEPTH_CLEAR;
extern const char* NAME_ELEMENT_DEPTH_CLEAR____FLOAT;
extern const char* NAME_ELEMENT_DEPTH_CLEAR____FX_CLEARDEPTH_COMMON;
extern const char* NAME_ELEMENT_DEPTH_FUNC;
extern const char* NAME_ELEMENT_DEPTH_MASK;
extern const char* NAME_ELEMENT_DEPTH_RANGE;
extern const char* NAME_ELEMENT_DEPTH_TARGET;
extern const char* NAME_ELEMENT_DEPTH_TARGET____FX_DEPTHTARGET_COMMON;
extern const char* NAME_ELEMENT_DEPTH_TARGET____GLES_RENDERTARGET_COMMON;
extern const char* NAME_ELEMENT_DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_DEST;
extern const char* NAME_ELEMENT_DEST_ALPHA;
extern const char* NAME_ELEMENT_DEST_RGB;
extern const char* NAME_ELEMENT_DIFFUSE;
extern const char* NAME_ELEMENT_DIRECTIONAL;
extern const char* NAME_ELEMENT_DITHER_ENABLE;
extern const char* NAME_ELEMENT_DRAW;
extern const char* NAME_ELEMENT_DYNAMIC;
extern const char* NAME_ELEMENT_DYNAMIC_FRICTION;
extern const char* NAME_ELEMENT_EFFECT;
extern const char* NAME_ELEMENT_ELLIPSOID;
extern const char* NAME_ELEMENT_EMISSION;
extern const char* NAME_ELEMENT_ENABLED;
extern const char* NAME_ELEMENT_ENUM;
extern const char* NAME_ELEMENT_ENUM____GLES_ENUMERATION;
extern const char* NAME_ELEMENT_ENUM____GL_ENUMERATION;
extern const char* NAME_ELEMENT_ENUM____STRING;
extern const char* NAME_ELEMENT_EQUATION;
extern const char* NAME_ELEMENT_EVALUATE_SCENE;
extern const char* NAME_ELEMENT_EXTRA;
extern const char* NAME_ELEMENT_FACE;
extern const char* NAME_ELEMENT_FAIL;
extern const char* NAME_ELEMENT_FALLOFF_ANGLE;
extern const char* NAME_ELEMENT_FALLOFF_EXPONENT;
extern const char* NAME_ELEMENT_FIXED;
extern const char* NAME_ELEMENT_FIXED1;
extern const char* NAME_ELEMENT_FIXED1X1;
extern const char* NAME_ELEMENT_FIXED1X2;
extern const char* NAME_ELEMENT_FIXED1X3;
extern const char* NAME_ELEMENT_FIXED1X4;
extern const char* NAME_ELEMENT_FIXED2;
extern const char* NAME_ELEMENT_FIXED2X1;
extern const char* NAME_ELEMENT_FIXED2X2;
extern const char* NAME_ELEMENT_FIXED2X3;
extern const char* NAME_ELEMENT_FIXED2X4;
extern const char* NAME_ELEMENT_FIXED3;
extern const char* NAME_ELEMENT_FIXED3X1;
extern const char* NAME_ELEMENT_FIXED3X2;
extern const char* NAME_ELEMENT_FIXED3X3;
extern const char* NAME_ELEMENT_FIXED3X4;
extern const char* NAME_ELEMENT_FIXED4;
extern const char* NAME_ELEMENT_FIXED4X1;
extern const char* NAME_ELEMENT_FIXED4X2;
extern const char* NAME_ELEMENT_FIXED4X3;
extern const char* NAME_ELEMENT_FIXED4X4;
extern const char* NAME_ELEMENT_FLOAT;
extern const char* NAME_ELEMENT_FLOAT1;
extern const char* NAME_ELEMENT_FLOAT1X1;
extern const char* NAME_ELEMENT_FLOAT1X1____CG_FLOAT1X1;
extern const char* NAME_ELEMENT_FLOAT1X1____FLOAT;
extern const char* NAME_ELEMENT_FLOAT1X2;
extern const char* NAME_ELEMENT_FLOAT1X2____CG_FLOAT1X2;
extern const char* NAME_ELEMENT_FLOAT1X2____FLOAT2;
extern const char* NAME_ELEMENT_FLOAT1X3;
extern const char* NAME_ELEMENT_FLOAT1X3____CG_FLOAT1X3;
extern const char* NAME_ELEMENT_FLOAT1X3____FLOAT3;
extern const char* NAME_ELEMENT_FLOAT1X4;
extern const char* NAME_ELEMENT_FLOAT1X4____CG_FLOAT1X4;
extern const char* NAME_ELEMENT_FLOAT1X4____FLOAT4;
extern const char* NAME_ELEMENT_FLOAT2;
extern const char* NAME_ELEMENT_FLOAT2X1;
extern const char* NAME_ELEMENT_FLOAT2X1____CG_FLOAT2X1;
extern const char* NAME_ELEMENT_FLOAT2X1____FLOAT2;
extern const char* NAME_ELEMENT_FLOAT2X2;
extern const char* NAME_ELEMENT_FLOAT2X2____CG_FLOAT2X2;
extern const char* NAME_ELEMENT_FLOAT2X2____FLOAT2X2;
extern const char* NAME_ELEMENT_FLOAT2X2____GLSL_FLOAT2X2;
extern const char* NAME_ELEMENT_FLOAT2X3;
extern const char* NAME_ELEMENT_FLOAT2X3____CG_FLOAT2X3;
extern const char* NAME_ELEMENT_FLOAT2X3____FLOAT2X3;
extern const char* NAME_ELEMENT_FLOAT2X4;
extern const char* NAME_ELEMENT_FLOAT2X4____CG_FLOAT2X4;
extern const char* NAME_ELEMENT_FLOAT2X4____FLOAT2X4;
extern const char* NAME_ELEMENT_FLOAT2____CG_FLOAT2;
extern const char* NAME_ELEMENT_FLOAT2____FLOAT2;
extern const char* NAME_ELEMENT_FLOAT2____GLSL_FLOAT2;
extern const char* NAME_ELEMENT_FLOAT3;
extern const char* NAME_ELEMENT_FLOAT3X1;
extern const char* NAME_ELEMENT_FLOAT3X1____CG_FLOAT3X1;
extern const char* NAME_ELEMENT_FLOAT3X1____FLOAT3;
extern const char* NAME_ELEMENT_FLOAT3X2;
extern const char* NAME_ELEMENT_FLOAT3X2____CG_FLOAT3X2;
extern const char* NAME_ELEMENT_FLOAT3X2____FLOAT3X2;
extern const char* NAME_ELEMENT_FLOAT3X3;
extern const char* NAME_ELEMENT_FLOAT3X3____CG_FLOAT3X3;
extern const char* NAME_ELEMENT_FLOAT3X3____FLOAT3X3;
extern const char* NAME_ELEMENT_FLOAT3X3____GLSL_FLOAT3X3;
extern const char* NAME_ELEMENT_FLOAT3X4;
extern const char* NAME_ELEMENT_FLOAT3X4____CG_FLOAT3X4;
extern const char* NAME_ELEMENT_FLOAT3X4____FLOAT3X4;
extern const char* NAME_ELEMENT_FLOAT3____CG_FLOAT3;
extern const char* NAME_ELEMENT_FLOAT3____FLOAT3;
extern const char* NAME_ELEMENT_FLOAT3____GLSL_FLOAT3;
extern const char* NAME_ELEMENT_FLOAT4;
extern const char* NAME_ELEMENT_FLOAT4X1;
extern const char* NAME_ELEMENT_FLOAT4X1____CG_FLOAT4X1;
extern const char* NAME_ELEMENT_FLOAT4X1____FLOAT4;
extern const char* NAME_ELEMENT_FLOAT4X2;
extern const char* NAME_ELEMENT_FLOAT4X2____CG_FLOAT4X2;
extern const char* NAME_ELEMENT_FLOAT4X2____FLOAT4X2;
extern const char* NAME_ELEMENT_FLOAT4X3;
extern const char* NAME_ELEMENT_FLOAT4X3____CG_FLOAT4X3;
extern const char* NAME_ELEMENT_FLOAT4X3____FLOAT4X3;
extern const char* NAME_ELEMENT_FLOAT4X4;
extern const char* NAME_ELEMENT_FLOAT4X4____CG_FLOAT4X4;
extern const char* NAME_ELEMENT_FLOAT4X4____FLOAT4X4;
extern const char* NAME_ELEMENT_FLOAT4X4____GLSL_FLOAT4X4;
extern const char* NAME_ELEMENT_FLOAT4____CG_FLOAT4;
extern const char* NAME_ELEMENT_FLOAT4____FLOAT4;
extern const char* NAME_ELEMENT_FLOAT4____GLSL_FLOAT4;
extern const char* NAME_ELEMENT_FLOAT_ARRAY;
extern const char* NAME_ELEMENT_FLOAT____CG_FLOAT;
extern const char* NAME_ELEMENT_FLOAT____FLOAT;
extern const char* NAME_ELEMENT_FLOAT____GLSL_FLOAT;
extern const char* NAME_ELEMENT_FOG_COLOR;
extern const char* NAME_ELEMENT_FOG_COORD_SRC;
extern const char* NAME_ELEMENT_FOG_DENSITY;
extern const char* NAME_ELEMENT_FOG_ENABLE;
extern const char* NAME_ELEMENT_FOG_END;
extern const char* NAME_ELEMENT_FOG_MODE;
extern const char* NAME_ELEMENT_FOG_START;
extern const char* NAME_ELEMENT_FORCE_FIELD;
extern const char* NAME_ELEMENT_FORMAT;
extern const char* NAME_ELEMENT_FORMAT_HINT;
extern const char* NAME_ELEMENT_FRONT;
extern const char* NAME_ELEMENT_FRONT_FACE;
extern const char* NAME_ELEMENT_FUNC;
extern const char* NAME_ELEMENT_FX_PROFILE_ABSTRACT;
extern const char* NAME_ELEMENT_FX_SURFACE_INIT_CUBE_COMMON____ALL;
extern const char* NAME_ELEMENT_FX_SURFACE_INIT_CUBE_COMMON____FACE;
extern const char* NAME_ELEMENT_FX_SURFACE_INIT_CUBE_COMMON____PRIMARY;
extern const char* NAME_ELEMENT_FX_SURFACE_INIT_PLANAR_COMMON____ALL;
extern const char* NAME_ELEMENT_FX_SURFACE_INIT_VOLUME_COMMON____ALL;
extern const char* NAME_ELEMENT_FX_SURFACE_INIT_VOLUME_COMMON____PRIMARY;
extern const char* NAME_ELEMENT_GENERATOR;
extern const char* NAME_ELEMENT_GEOMETRY;
extern const char* NAME_ELEMENT_GLSL_SURFACE_TYPE____GENERATOR;
extern const char* NAME_ELEMENT_GL_HOOK_ABSTRACT;
extern const char* NAME_ELEMENT_GRAVITY;
extern const char* NAME_ELEMENT_H;
extern const char* NAME_ELEMENT_HALF;
extern const char* NAME_ELEMENT_HALF1;
extern const char* NAME_ELEMENT_HALF1X1;
extern const char* NAME_ELEMENT_HALF1X2;
extern const char* NAME_ELEMENT_HALF1X3;
extern const char* NAME_ELEMENT_HALF1X4;
extern const char* NAME_ELEMENT_HALF2;
extern const char* NAME_ELEMENT_HALF2X1;
extern const char* NAME_ELEMENT_HALF2X2;
extern const char* NAME_ELEMENT_HALF2X3;
extern const char* NAME_ELEMENT_HALF2X4;
extern const char* NAME_ELEMENT_HALF3;
extern const char* NAME_ELEMENT_HALF3X1;
extern const char* NAME_ELEMENT_HALF3X2;
extern const char* NAME_ELEMENT_HALF3X3;
extern const char* NAME_ELEMENT_HALF3X4;
extern const char* NAME_ELEMENT_HALF4;
extern const char* NAME_ELEMENT_HALF4X1;
extern const char* NAME_ELEMENT_HALF4X2;
extern const char* NAME_ELEMENT_HALF4X3;
extern const char* NAME_ELEMENT_HALF4X4;
extern const char* NAME_ELEMENT_HALF_EXTENTS;
extern const char* NAME_ELEMENT_HEIGHT;
extern const char* NAME_ELEMENT_HOLLOW;
extern const char* NAME_ELEMENT_IDREF_ARRAY;
extern const char* NAME_ELEMENT_IMAGE;
extern const char* NAME_ELEMENT_IMAGER;
extern const char* NAME_ELEMENT_INCLUDE;
extern const char* NAME_ELEMENT_INDEX_OF_REFRACTION;
extern const char* NAME_ELEMENT_INERTIA;
extern const char* NAME_ELEMENT_INIT_AS_NULL;
extern const char* NAME_ELEMENT_INIT_AS_TARGET;
extern const char* NAME_ELEMENT_INIT_CUBE;
extern const char* NAME_ELEMENT_INIT_FROM;
extern const char* NAME_ELEMENT_INIT_FROM____ANYURI;
extern const char* NAME_ELEMENT_INIT_FROM____FX_SURFACE_INIT_FROM_COMMON;
extern const char* NAME_ELEMENT_INIT_PLANAR;
extern const char* NAME_ELEMENT_INIT_VOLUME;
extern const char* NAME_ELEMENT_INPUT;
extern const char* NAME_ELEMENT_INPUT____INPUTLOCAL;
extern const char* NAME_ELEMENT_INPUT____INPUTLOCALOFFSET;
extern const char* NAME_ELEMENT_INSTANCE_ANIMATION;
extern const char* NAME_ELEMENT_INSTANCE_CAMERA;
extern const char* NAME_ELEMENT_INSTANCE_CONTROLLER;
extern const char* NAME_ELEMENT_INSTANCE_EFFECT;
extern const char* NAME_ELEMENT_INSTANCE_EFFECT__SETPARAM;
extern const char* NAME_ELEMENT_INSTANCE_FORCE_FIELD;
extern const char* NAME_ELEMENT_INSTANCE_GEOMETRY;
extern const char* NAME_ELEMENT_INSTANCE_LIGHT;
extern const char* NAME_ELEMENT_INSTANCE_MATERIAL;
extern const char* NAME_ELEMENT_INSTANCE_MATERIAL__BIND;
extern const char* NAME_ELEMENT_INSTANCE_NODE;
extern const char* NAME_ELEMENT_INSTANCE_PHYSICS_MATERIAL;
extern const char* NAME_ELEMENT_INSTANCE_PHYSICS_MODEL;
extern const char* NAME_ELEMENT_INSTANCE_PHYSICS_SCENE;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW;
extern const char* NAME_ELEMENT_INSTANCE_RIGID_CONSTRAINT;
extern const char* NAME_ELEMENT_INSTANCE_VISUAL_SCENE;
extern const char* NAME_ELEMENT_INT;
extern const char* NAME_ELEMENT_INT1;
extern const char* NAME_ELEMENT_INT1X1;
extern const char* NAME_ELEMENT_INT1X2;
extern const char* NAME_ELEMENT_INT1X3;
extern const char* NAME_ELEMENT_INT1X4;
extern const char* NAME_ELEMENT_INT2;
extern const char* NAME_ELEMENT_INT2X1;
extern const char* NAME_ELEMENT_INT2X2;
extern const char* NAME_ELEMENT_INT2X3;
extern const char* NAME_ELEMENT_INT2X4;
extern const char* NAME_ELEMENT_INT2____CG_INT2;
extern const char* NAME_ELEMENT_INT2____GLSL_INT2;
extern const char* NAME_ELEMENT_INT2____INT2;
extern const char* NAME_ELEMENT_INT3;
extern const char* NAME_ELEMENT_INT3X1;
extern const char* NAME_ELEMENT_INT3X2;
extern const char* NAME_ELEMENT_INT3X3;
extern const char* NAME_ELEMENT_INT3X4;
extern const char* NAME_ELEMENT_INT3____CG_INT3;
extern const char* NAME_ELEMENT_INT3____GLSL_INT3;
extern const char* NAME_ELEMENT_INT3____INT3;
extern const char* NAME_ELEMENT_INT4;
extern const char* NAME_ELEMENT_INT4X1;
extern const char* NAME_ELEMENT_INT4X2;
extern const char* NAME_ELEMENT_INT4X3;
extern const char* NAME_ELEMENT_INT4X4;
extern const char* NAME_ELEMENT_INT4____CG_INT4;
extern const char* NAME_ELEMENT_INT4____GLSL_INT4;
extern const char* NAME_ELEMENT_INT4____INT4;
extern const char* NAME_ELEMENT_INTERPENETRATE;
extern const char* NAME_ELEMENT_INT_ARRAY;
extern const char* NAME_ELEMENT_INT____CG_INT;
extern const char* NAME_ELEMENT_INT____GLSL_INT;
extern const char* NAME_ELEMENT_INT____INT;
extern const char* NAME_ELEMENT_JOINTS;
extern const char* NAME_ELEMENT_KEYWORDS;
extern const char* NAME_ELEMENT_LAMBERT;
extern const char* NAME_ELEMENT_LAYER;
extern const char* NAME_ELEMENT_LIBRARY_ANIMATIONS;
extern const char* NAME_ELEMENT_LIBRARY_ANIMATION_CLIPS;
extern const char* NAME_ELEMENT_LIBRARY_CAMERAS;
extern const char* NAME_ELEMENT_LIBRARY_CONTROLLERS;
extern const char* NAME_ELEMENT_LIBRARY_EFFECTS;
extern const char* NAME_ELEMENT_LIBRARY_FORCE_FIELDS;
extern const char* NAME_ELEMENT_LIBRARY_GEOMETRIES;
extern const char* NAME_ELEMENT_LIBRARY_IMAGES;
extern const char* NAME_ELEMENT_LIBRARY_LIGHTS;
extern const char* NAME_ELEMENT_LIBRARY_MATERIALS;
extern const char* NAME_ELEMENT_LIBRARY_NODES;
extern const char* NAME_ELEMENT_LIBRARY_PHYSICS_MATERIALS;
extern const char* NAME_ELEMENT_LIBRARY_PHYSICS_MODELS;
extern const char* NAME_ELEMENT_LIBRARY_PHYSICS_SCENES;
extern const char* NAME_ELEMENT_LIBRARY_VISUAL_SCENES;
extern const char* NAME_ELEMENT_LIGHT;
extern const char* NAME_ELEMENT_LIGHTING_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_AMBIENT;
extern const char* NAME_ELEMENT_LIGHT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_LIGHT_DIFFUSE;
extern const char* NAME_ELEMENT_LIGHT_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_LINEAR_ATTENUATION;
extern const char* NAME_ELEMENT_LIGHT_LINEAR_ATTENUTATION;
extern const char* NAME_ELEMENT_LIGHT_MODEL_AMBIENT;
extern const char* NAME_ELEMENT_LIGHT_MODEL_COLOR_CONTROL;
extern const char* NAME_ELEMENT_LIGHT_MODEL_LOCAL_VIEWER_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_MODEL_TWO_SIDE_ENABLE;
extern const char* NAME_ELEMENT_LIGHT_POSITION;
extern const char* NAME_ELEMENT_LIGHT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_LIGHT_SPECULAR;
extern const char* NAME_ELEMENT_LIGHT_SPOT_CUTOFF;
extern const char* NAME_ELEMENT_LIGHT_SPOT_DIRECTION;
extern const char* NAME_ELEMENT_LIGHT_SPOT_EXPONENT;
extern const char* NAME_ELEMENT_LIGHT__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_LIGHT__TECHNIQUE_COMMON__AMBIENT;
extern const char* NAME_ELEMENT_LIMITS;
extern const char* NAME_ELEMENT_LIMITS__LINEAR;
extern const char* NAME_ELEMENT_LINEAR;
extern const char* NAME_ELEMENT_LINEAR_ATTENUATION;
extern const char* NAME_ELEMENT_LINES;
extern const char* NAME_ELEMENT_LINESTRIPS;
extern const char* NAME_ELEMENT_LINE_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_LINE_STIPPLE;
extern const char* NAME_ELEMENT_LINE_STIPPLE_ENABLE;
extern const char* NAME_ELEMENT_LINE_WIDTH;
extern const char* NAME_ELEMENT_LOGIC_OP;
extern const char* NAME_ELEMENT_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_LOOKAT;
extern const char* NAME_ELEMENT_MAGFILTER;
extern const char* NAME_ELEMENT_MASK;
extern const char* NAME_ELEMENT_MASS;
extern const char* NAME_ELEMENT_MASS_FRAME;
extern const char* NAME_ELEMENT_MATERIAL;
extern const char* NAME_ELEMENT_MATERIAL_AMBIENT;
extern const char* NAME_ELEMENT_MATERIAL_DIFFUSE;
extern const char* NAME_ELEMENT_MATERIAL_EMISSION;
extern const char* NAME_ELEMENT_MATERIAL_SHININESS;
extern const char* NAME_ELEMENT_MATERIAL_SPECULAR;
extern const char* NAME_ELEMENT_MATRIX;
extern const char* NAME_ELEMENT_MAX;
extern const char* NAME_ELEMENT_MESH;
extern const char* NAME_ELEMENT_MIN;
extern const char* NAME_ELEMENT_MINFILTER;
extern const char* NAME_ELEMENT_MIPFILTER;
extern const char* NAME_ELEMENT_MIPMAP_BIAS;
extern const char* NAME_ELEMENT_MIPMAP_GENERATE;
extern const char* NAME_ELEMENT_MIPMAP_MAXLEVEL;
extern const char* NAME_ELEMENT_MIP_LEVELS;
extern const char* NAME_ELEMENT_MODE;
extern const char* NAME_ELEMENT_MODEL_VIEW_MATRIX;
extern const char* NAME_ELEMENT_MODIFIED;
extern const char* NAME_ELEMENT_MODIFIER;
extern const char* NAME_ELEMENT_MORPH;
extern const char* NAME_ELEMENT_MULTISAMPLE_ENABLE;
extern const char* NAME_ELEMENT_NAME;
extern const char* NAME_ELEMENT_NAME_ARRAY;
extern const char* NAME_ELEMENT_NEWPARAM;
extern const char* NAME_ELEMENT_NEWPARAM____CG_NEWPARAM;
extern const char* NAME_ELEMENT_NEWPARAM____COMMON_NEWPARAM_TYPE;
extern const char* NAME_ELEMENT_NEWPARAM____FX_NEWPARAM_COMMON;
extern const char* NAME_ELEMENT_NEWPARAM____GLES_NEWPARAM;
extern const char* NAME_ELEMENT_NEWPARAM____GLSL_NEWPARAM;
extern const char* NAME_ELEMENT_NODE;
extern const char* NAME_ELEMENT_NORMALIZE_ENABLE;
extern const char* NAME_ELEMENT_OPTICS;
extern const char* NAME_ELEMENT_OPTICS__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_OPTION;
extern const char* NAME_ELEMENT_ORDER;
extern const char* NAME_ELEMENT_ORTHOGRAPHIC;
extern const char* NAME_ELEMENT_P;
extern const char* NAME_ELEMENT_PARAM;
extern const char* NAME_ELEMENT_PARAM____NCNAME;
extern const char* NAME_ELEMENT_PASS;
extern const char* NAME_ELEMENT_PERSPECTIVE;
extern const char* NAME_ELEMENT_PH;
extern const char* NAME_ELEMENT_PHONG;
extern const char* NAME_ELEMENT_PHYSICS_MATERIAL;
extern const char* NAME_ELEMENT_PHYSICS_MATERIAL__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_PHYSICS_MODEL;
extern const char* NAME_ELEMENT_PHYSICS_SCENE;
extern const char* NAME_ELEMENT_PHYSICS_SCENE__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_PLANE;
extern const char* NAME_ELEMENT_POINT;
extern const char* NAME_ELEMENT_POINT_DISTANCE_ATTENUATION;
extern const char* NAME_ELEMENT_POINT_FADE_THRESHOLD_SIZE;
extern const char* NAME_ELEMENT_POINT_SIZE;
extern const char* NAME_ELEMENT_POINT_SIZE_MAX;
extern const char* NAME_ELEMENT_POINT_SIZE_MIN;
extern const char* NAME_ELEMENT_POINT_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_POLYGONS;
extern const char* NAME_ELEMENT_POLYGON_MODE;
extern const char* NAME_ELEMENT_POLYGON_MODE__FACE;
extern const char* NAME_ELEMENT_POLYGON_MODE__MODE;
extern const char* NAME_ELEMENT_POLYGON_OFFSET;
extern const char* NAME_ELEMENT_POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_OFFSET_LINE_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_OFFSET_POINT_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_POLYGON_STIPPLE_ENABLE;
extern const char* NAME_ELEMENT_POLYLIST;
extern const char* NAME_ELEMENT_PRECISION;
extern const char* NAME_ELEMENT_PRIMARY;
extern const char* NAME_ELEMENT_PROFILE_CG;
extern const char* NAME_ELEMENT_PROFILE_CG__NEWPARAM__SURFACE__GENERATOR__NAME;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__BIND;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__BIND__PARAM;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__COMPILER_TARGET;
extern const char* NAME_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__NAME;
extern const char* NAME_ELEMENT_PROFILE_COMMON;
extern const char* NAME_ELEMENT_PROFILE_COMMON__TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_COMMON__TECHNIQUE__CONSTANT;
extern const char* NAME_ELEMENT_PROFILE_GLES;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_FUNC__VALUE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_FUNC__DEST;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_FUNC__SRC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLEAR_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLEAR_DEPTH;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLEAR_STENCIL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLIP_PLANE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLIP_PLANE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__COLOR_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__COLOR_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__COLOR_MATERIAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CULL_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_RANGE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DITHER_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_DENSITY;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_END;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_MODE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_START;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FRONT_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHTING_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_MODEL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_MODEL_TWO_SIDE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_POSITION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPOT_CUTOFF;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPOT_DIRECTION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPOT_EXPONENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LINE_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LINE_WIDTH;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LOGIC_OP;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_EMISSION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_SHININESS;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MODEL_VIEW_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MULTISAMPLE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__NORMALIZE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_DISTANCE_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_FADE_THRESHOLD_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SIZE_MAX;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SIZE_MIN;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POLYGON_OFFSET;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__PROJECTION_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__RESCALE_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_ONE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SCISSOR;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SHADE_MODEL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC__REF;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__TEXTURE_PIPELINE;
extern const char* NAME_ELEMENT_PROFILE_GLES__TECHNIQUE__SETPARAM;
extern const char* NAME_ELEMENT_PROFILE_GLSL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__NEWPARAM__SURFACE__GENERATOR__NAME;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_FUNC__VALUE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_FUNC__DEST;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_FUNC__SRC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLEAR_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLEAR_DEPTH;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLEAR_STENCIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLIP_PLANE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLIP_PLANE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__COLOR_LOGIC_OP_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__COLOR_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__COLOR_MATERIAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CULL_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CULL_FACE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_RANGE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DITHER_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_COLOR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_DENSITY;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_END;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_MODE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_START;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FRONT_FACE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHTING_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_CONSTANT_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_MODEL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_MODEL_TWO_SIDE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_POSITION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPOT_CUTOFF;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPOT_DIRECTION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPOT_EXPONENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LINE_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LINE_WIDTH;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LOGIC_OP;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_AMBIENT;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_DIFFUSE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_EMISSION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_SHININESS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_SPECULAR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MODEL_VIEW_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MULTISAMPLE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__NORMALIZE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_DISTANCE_ATTENUATION;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_FADE_THRESHOLD_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SIZE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SIZE_MAX;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SIZE_MIN;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SMOOTH_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POLYGON_OFFSET;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POLYGON_OFFSET_FILL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__PROJECTION_MATRIX;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__RESCALE_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_ONE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SCISSOR;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__BIND;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__BIND__PARAM;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__COMPILER_TARGET;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__NAME;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADE_MODEL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC__FUNC;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC__MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC__REF;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_MASK;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP__FAIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP__ZFAIL;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP__ZPASS;
extern const char* NAME_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_PROJECTION_MATRIX;
extern const char* NAME_ELEMENT_QUADRATIC_ATTENUATION;
extern const char* NAME_ELEMENT_RADIUS;
extern const char* NAME_ELEMENT_RADIUS1;
extern const char* NAME_ELEMENT_RADIUS2;
extern const char* NAME_ELEMENT_RADIUS____FLOAT;
extern const char* NAME_ELEMENT_RADIUS____FLOAT2;
extern const char* NAME_ELEMENT_RANGE;
extern const char* NAME_ELEMENT_REF;
extern const char* NAME_ELEMENT_REFLECTIVE;
extern const char* NAME_ELEMENT_REFLECTIVITY;
extern const char* NAME_ELEMENT_REF_ATTACHMENT;
extern const char* NAME_ELEMENT_RENDER;
extern const char* NAME_ELEMENT_RESCALE_NORMAL_ENABLE;
extern const char* NAME_ELEMENT_RESTITUTION;
extern const char* NAME_ELEMENT_REVISION;
extern const char* NAME_ELEMENT_RGB;
extern const char* NAME_ELEMENT_RIGID_BODY;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE;
extern const char* NAME_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW;
extern const char* NAME_ELEMENT_RIGID_CONSTRAINT;
extern const char* NAME_ELEMENT_RIGID_CONSTRAINT__TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_ROTATE;
extern const char* NAME_ELEMENT_SAMPLER;
extern const char* NAME_ELEMENT_SAMPLER1D;
extern const char* NAME_ELEMENT_SAMPLER1D____CG_SAMPLER1D;
extern const char* NAME_ELEMENT_SAMPLER1D____FX_SAMPLER1D_COMMON;
extern const char* NAME_ELEMENT_SAMPLER1D____GL_SAMPLER1D;
extern const char* NAME_ELEMENT_SAMPLER2D;
extern const char* NAME_ELEMENT_SAMPLER2D____CG_SAMPLER2D;
extern const char* NAME_ELEMENT_SAMPLER2D____FX_SAMPLER2D_COMMON;
extern const char* NAME_ELEMENT_SAMPLER2D____GL_SAMPLER2D;
extern const char* NAME_ELEMENT_SAMPLER3D;
extern const char* NAME_ELEMENT_SAMPLER3D____CG_SAMPLER3D;
extern const char* NAME_ELEMENT_SAMPLER3D____FX_SAMPLER3D_COMMON;
extern const char* NAME_ELEMENT_SAMPLER3D____GL_SAMPLER3D;
extern const char* NAME_ELEMENT_SAMPLERCUBE;
extern const char* NAME_ELEMENT_SAMPLERCUBE____CG_SAMPLERCUBE;
extern const char* NAME_ELEMENT_SAMPLERCUBE____FX_SAMPLERCUBE_COMMON;
extern const char* NAME_ELEMENT_SAMPLERCUBE____GL_SAMPLERCUBE;
extern const char* NAME_ELEMENT_SAMPLERDEPTH;
extern const char* NAME_ELEMENT_SAMPLERDEPTH____CG_SAMPLERDEPTH;
extern const char* NAME_ELEMENT_SAMPLERDEPTH____FX_SAMPLERDEPTH_COMMON;
extern const char* NAME_ELEMENT_SAMPLERDEPTH____GL_SAMPLERDEPTH;
extern const char* NAME_ELEMENT_SAMPLERRECT;
extern const char* NAME_ELEMENT_SAMPLERRECT____CG_SAMPLERRECT;
extern const char* NAME_ELEMENT_SAMPLERRECT____FX_SAMPLERRECT_COMMON;
extern const char* NAME_ELEMENT_SAMPLERRECT____GL_SAMPLERRECT;
extern const char* NAME_ELEMENT_SAMPLER_STATE;
extern const char* NAME_ELEMENT_SAMPLER_STATE____GLES_SAMPLER_STATE;
extern const char* NAME_ELEMENT_SAMPLER_STATE____NCNAME;
extern const char* NAME_ELEMENT_SAMPLE_ALPHA_TO_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_SAMPLE_ALPHA_TO_ONE_ENABLE;
extern const char* NAME_ELEMENT_SAMPLE_COVERAGE_ENABLE;
extern const char* NAME_ELEMENT_SCALE;
extern const char* NAME_ELEMENT_SCENE;
extern const char* NAME_ELEMENT_SCISSOR;
extern const char* NAME_ELEMENT_SCISSOR_TEST_ENABLE;
extern const char* NAME_ELEMENT_SEMANTIC;
extern const char* NAME_ELEMENT_SETPARAM;
extern const char* NAME_ELEMENT_SETPARAM____CG_SETPARAM;
extern const char* NAME_ELEMENT_SETPARAM____CG_SETPARAM_SIMPLE;
extern const char* NAME_ELEMENT_SETPARAM____GLSL_SETPARAM;
extern const char* NAME_ELEMENT_SETPARAM____GLSL_SETPARAM_SIMPLE;
extern const char* NAME_ELEMENT_SHADER;
extern const char* NAME_ELEMENT_SHADE_MODEL;
extern const char* NAME_ELEMENT_SHAPE;
extern const char* NAME_ELEMENT_SHININESS;
extern const char* NAME_ELEMENT_SIZE;
extern const char* NAME_ELEMENT_SIZE____FLOAT3;
extern const char* NAME_ELEMENT_SIZE____INT3;
extern const char* NAME_ELEMENT_SKELETON;
extern const char* NAME_ELEMENT_SKEW;
extern const char* NAME_ELEMENT_SKIN;
extern const char* NAME_ELEMENT_SOURCE;
extern const char* NAME_ELEMENT_SOURCE_DATA;
extern const char* NAME_ELEMENT_SOURCE____NCNAME;
extern const char* NAME_ELEMENT_SPECULAR;
extern const char* NAME_ELEMENT_SPHERE;
extern const char* NAME_ELEMENT_SPLINE;
extern const char* NAME_ELEMENT_SPOT;
extern const char* NAME_ELEMENT_SPRING;
extern const char* NAME_ELEMENT_SPRING__LINEAR;
extern const char* NAME_ELEMENT_SRC;
extern const char* NAME_ELEMENT_SRC_ALPHA;
extern const char* NAME_ELEMENT_SRC_RGB;
extern const char* NAME_ELEMENT_STATIC_FRICTION;
extern const char* NAME_ELEMENT_STENCIL_CLEAR;
extern const char* NAME_ELEMENT_STENCIL_CLEAR____BYTE;
extern const char* NAME_ELEMENT_STENCIL_CLEAR____FX_CLEARSTENCIL_COMMON;
extern const char* NAME_ELEMENT_STENCIL_FUNC;
extern const char* NAME_ELEMENT_STENCIL_FUNC_SEPARATE;
extern const char* NAME_ELEMENT_STENCIL_FUNC_SEPARATE__MASK;
extern const char* NAME_ELEMENT_STENCIL_FUNC_SEPARATE__REF;
extern const char* NAME_ELEMENT_STENCIL_MASK;
extern const char* NAME_ELEMENT_STENCIL_MASK_SEPARATE;
extern const char* NAME_ELEMENT_STENCIL_MASK_SEPARATE__FACE;
extern const char* NAME_ELEMENT_STENCIL_MASK_SEPARATE__MASK;
extern const char* NAME_ELEMENT_STENCIL_OP;
extern const char* NAME_ELEMENT_STENCIL_OP_SEPARATE;
extern const char* NAME_ELEMENT_STENCIL_OP_SEPARATE__FACE;
extern const char* NAME_ELEMENT_STENCIL_OP_SEPARATE__FAIL;
extern const char* NAME_ELEMENT_STENCIL_OP_SEPARATE__ZFAIL;
extern const char* NAME_ELEMENT_STENCIL_OP_SEPARATE__ZPASS;
extern const char* NAME_ELEMENT_STENCIL_TARGET;
extern const char* NAME_ELEMENT_STENCIL_TARGET____FX_STENCILTARGET_COMMON;
extern const char* NAME_ELEMENT_STENCIL_TARGET____GLES_RENDERTARGET_COMMON;
extern const char* NAME_ELEMENT_STENCIL_TEST_ENABLE;
extern const char* NAME_ELEMENT_STIFFNESS;
extern const char* NAME_ELEMENT_STRING;
extern const char* NAME_ELEMENT_SUBJECT;
extern const char* NAME_ELEMENT_SURFACE;
extern const char* NAME_ELEMENT_SURFACE____CG_SURFACE_TYPE;
extern const char* NAME_ELEMENT_SURFACE____FX_SURFACE_COMMON;
extern const char* NAME_ELEMENT_SURFACE____GLSL_SURFACE_TYPE;
extern const char* NAME_ELEMENT_SURFACE____NCNAME;
extern const char* NAME_ELEMENT_SWING_CONE_AND_TWIST;
extern const char* NAME_ELEMENT_TAPERED_CAPSULE;
extern const char* NAME_ELEMENT_TAPERED_CYLINDER;
extern const char* NAME_ELEMENT_TARGETS;
extern const char* NAME_ELEMENT_TARGET_VALUE;
extern const char* NAME_ELEMENT_TECHNIQUE;
extern const char* NAME_ELEMENT_TECHNIQUE_COMMON;
extern const char* NAME_ELEMENT_TECHNIQUE_HINT;
extern const char* NAME_ELEMENT_TEXCOMBINER;
extern const char* NAME_ELEMENT_TEXCOORD;
extern const char* NAME_ELEMENT_TEXENV;
extern const char* NAME_ELEMENT_TEXTURE;
extern const char* NAME_ELEMENT_TEXTURE1D;
extern const char* NAME_ELEMENT_TEXTURE1D_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE2D;
extern const char* NAME_ELEMENT_TEXTURE2D_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE3D;
extern const char* NAME_ELEMENT_TEXTURE3D_ENABLE;
extern const char* NAME_ELEMENT_TEXTURECUBE;
extern const char* NAME_ELEMENT_TEXTURECUBE_ENABLE;
extern const char* NAME_ELEMENT_TEXTUREDEPTH;
extern const char* NAME_ELEMENT_TEXTUREDEPTH_ENABLE;
extern const char* NAME_ELEMENT_TEXTURERECT;
extern const char* NAME_ELEMENT_TEXTURERECT_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE_ENV_COLOR;
extern const char* NAME_ELEMENT_TEXTURE_ENV_MODE;
extern const char* NAME_ELEMENT_TEXTURE_PIPELINE;
extern const char* NAME_ELEMENT_TEXTURE_PIPELINE_ENABLE;
extern const char* NAME_ELEMENT_TEXTURE_PIPELINE____GLES_TEXTURE_PIPELINE;
extern const char* NAME_ELEMENT_TEXTURE_UNIT;
extern const char* NAME_ELEMENT_TIME_STEP;
extern const char* NAME_ELEMENT_TITLE;
extern const char* NAME_ELEMENT_TRANSLATE;
extern const char* NAME_ELEMENT_TRANSPARENCY;
extern const char* NAME_ELEMENT_TRANSPARENT;
extern const char* NAME_ELEMENT_TRIANGLES;
extern const char* NAME_ELEMENT_TRIFANS;
extern const char* NAME_ELEMENT_TRISTRIPS;
extern const char* NAME_ELEMENT_UNIT;
extern const char* NAME_ELEMENT_UP_AXIS;
extern const char* NAME_ELEMENT_USERTYPE;
extern const char* NAME_ELEMENT_V;
extern const char* NAME_ELEMENT_VALUE;
extern const char* NAME_ELEMENT_VALUE____GLES_TEXTURE_PIPELINE;
extern const char* NAME_ELEMENT_VALUE____GL_SAMPLER1D;
extern const char* NAME_ELEMENT_VALUE____GL_SAMPLER2D;
extern const char* NAME_ELEMENT_VALUE____GL_SAMPLER3D;
extern const char* NAME_ELEMENT_VALUE____GL_SAMPLERCUBE;
extern const char* NAME_ELEMENT_VALUE____GL_SAMPLERDEPTH;
extern const char* NAME_ELEMENT_VALUE____GL_SAMPLERRECT;
extern const char* NAME_ELEMENT_VCOUNT;
extern const char* NAME_ELEMENT_VELOCITY;
extern const char* NAME_ELEMENT_VERTEX_WEIGHTS;
extern const char* NAME_ELEMENT_VERTICES;
extern const char* NAME_ELEMENT_VIEWPORT_RATIO;
extern const char* NAME_ELEMENT_VISUAL_SCENE;
extern const char* NAME_ELEMENT_WRAP_P;
extern const char* NAME_ELEMENT_WRAP_S;
extern const char* NAME_ELEMENT_WRAP_S____FX_SAMPLER_WRAP_COMMON;
extern const char* NAME_ELEMENT_WRAP_S____GLES_SAMPLER_WRAP;
extern const char* NAME_ELEMENT_WRAP_T;
extern const char* NAME_ELEMENT_WRAP_T____FX_SAMPLER_WRAP_COMMON;
extern const char* NAME_ELEMENT_WRAP_T____GLES_SAMPLER_WRAP;
extern const char* NAME_ELEMENT_XFOV;
extern const char* NAME_ELEMENT_XMAG;
extern const char* NAME_ELEMENT_YFOV;
extern const char* NAME_ELEMENT_YMAG;
extern const char* NAME_ELEMENT_ZFAIL;
extern const char* NAME_ELEMENT_ZFAR;
extern const char* NAME_ELEMENT_ZNEAR;
extern const char* NAME_ELEMENT_ZPASS;


const StringHash HASH_ATTRIBUTE_base = 67145573;
const StringHash HASH_ATTRIBUTE_body = 431545;
const StringHash HASH_ATTRIBUTE_camera_node = 136173157;
const StringHash HASH_ATTRIBUTE_closed = 111372724;
const StringHash HASH_ATTRIBUTE_constraint = 180279812;
const StringHash HASH_ATTRIBUTE_convex_hull_of = 167766694;
const StringHash HASH_ATTRIBUTE_count = 6974548;
const StringHash HASH_ATTRIBUTE_depth = 6997928;
const StringHash HASH_ATTRIBUTE_digits = 112189619;
const StringHash HASH_ATTRIBUTE_end = 27716;
const StringHash HASH_ATTRIBUTE_face = 444309;
const StringHash HASH_ATTRIBUTE_format = 114725764;
const StringHash HASH_ATTRIBUTE_height = 116129268;
const StringHash HASH_ATTRIBUTE_id = 1780;
const StringHash HASH_ATTRIBUTE_index = 7359176;
const StringHash HASH_ATTRIBUTE_input_semantic = 256703331;
const StringHash HASH_ATTRIBUTE_input_set = 130685332;
const StringHash HASH_ATTRIBUTE_layer = 7507906;
const StringHash HASH_ATTRIBUTE_length = 120344232;
const StringHash HASH_ATTRIBUTE_magnitude = 240175317;
const StringHash HASH_ATTRIBUTE_material = 145524812;
const StringHash HASH_ATTRIBUTE_maxInclusive = 251940997;
const StringHash HASH_ATTRIBUTE_meter = 7588546;
const StringHash HASH_ATTRIBUTE_method = 121417556;
const StringHash HASH_ATTRIBUTE_minInclusive = 259829893;
const StringHash HASH_ATTRIBUTE_mip = 29696;
const StringHash HASH_ATTRIBUTE_name = 477237;
const StringHash HASH_ATTRIBUTE_offset = 123525572;
const StringHash HASH_ATTRIBUTE_opaque = 124160181;
const StringHash HASH_ATTRIBUTE_operand = 107776052;
const StringHash HASH_ATTRIBUTE_operator = 113806850;
const StringHash HASH_ATTRIBUTE_param = 7768189;
const StringHash HASH_ATTRIBUTE_parent = 124292180;
const StringHash HASH_ATTRIBUTE_platform = 42652157;
const StringHash HASH_ATTRIBUTE_profile = 127258709;
const StringHash HASH_ATTRIBUTE_program = 127264781;
const StringHash HASH_ATTRIBUTE_ref = 30902;
const StringHash HASH_ATTRIBUTE_rigid_body = 262281833;
const StringHash HASH_ATTRIBUTE_scale = 7968805;
const StringHash HASH_ATTRIBUTE_semantic = 205020515;
const StringHash HASH_ATTRIBUTE_set = 31172;
const StringHash HASH_ATTRIBUTE_sid = 31220;
const StringHash HASH_ATTRIBUTE_slice = 8007573;
const StringHash HASH_ATTRIBUTE_source = 128370837;
const StringHash HASH_ATTRIBUTE_stage = 8038357;
const StringHash HASH_ATTRIBUTE_start = 8038548;
const StringHash HASH_ATTRIBUTE_stride = 128683941;
const StringHash HASH_ATTRIBUTE_symbol = 128989532;
const StringHash HASH_ATTRIBUTE_target = 128486852;
const StringHash HASH_ATTRIBUTE_texcoord = 216686884;
const StringHash HASH_ATTRIBUTE_texture = 181386485;
const StringHash HASH_ATTRIBUTE_type = 508005;
const StringHash HASH_ATTRIBUTE_unit = 509188;
const StringHash HASH_ATTRIBUTE_url = 31884;
const StringHash HASH_ATTRIBUTE_value = 8160181;
const StringHash HASH_ATTRIBUTE_version = 214540334;
const StringHash HASH_ATTRIBUTE_width = 8256424;
const StringHash HASH_ATTRIBUTE_xmlns = 8340307;
const StringHash HASH_ELEMENT_ACCESSOR = 161263634;
const StringHash HASH_ELEMENT_ALL = 26668;
const StringHash HASH_ELEMENT_ALPHA = 6829793;
const StringHash HASH_ELEMENT_ALPHA_FUNC = 242440483;
const StringHash HASH_ELEMENT_ALPHA_TEST_ENABLE = 46564773;
const StringHash HASH_ELEMENT_ALPHA____GLES_TEXCOMBINER_COMMANDALPHA_TYPE = 179777157;
const StringHash HASH_ELEMENT_AMBIENT = 137952308;
const StringHash HASH_ELEMENT_AMBIENT____COMMON_COLOR_OR_TEXTURE_TYPE = 14131013;
const StringHash HASH_ELEMENT_ANGULAR = 139379426;
const StringHash HASH_ELEMENT_ANGULAR_VELOCITY = 135963289;
const StringHash HASH_ELEMENT_ANIMATION = 3721230;
const StringHash HASH_ELEMENT_ANIMATION_CLIP = 21376896;
const StringHash HASH_ELEMENT_ANIMATION__SOURCE__TECHNIQUE_COMMON = 154067054;
const StringHash HASH_ELEMENT_ANNOTATE = 89566757;
const StringHash HASH_ELEMENT_ARGUMENT = 149700308;
const StringHash HASH_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENTALPHA_TYPE = 171283253;
const StringHash HASH_ELEMENT_ARGUMENT____GLES_TEXCOMBINER_ARGUMENTRGB_TYPE = 189989461;
const StringHash HASH_ELEMENT_ARRAY = 6854793;
const StringHash HASH_ELEMENT_ARRAY____CG_NEWARRAY_TYPE = 30817685;
const StringHash HASH_ELEMENT_ARRAY____CG_SETARRAY_TYPE = 215039413;
const StringHash HASH_ELEMENT_ARRAY____GLSL_NEWARRAY_TYPE = 22516773;
const StringHash HASH_ELEMENT_ARRAY____GLSL_SETARRAY_TYPE = 206607461;
const StringHash HASH_ELEMENT_ASPECT_RATIO = 14868815;
const StringHash HASH_ELEMENT_ASSET = 6859204;
const StringHash HASH_ELEMENT_ATTACHMENT = 127513076;
const StringHash HASH_ELEMENT_AUTHOR = 109883234;
const StringHash HASH_ELEMENT_AUTHORING_TOOL = 142204124;
const StringHash HASH_ELEMENT_AUTO_NORMAL_ENABLE = 107084965;
const StringHash HASH_ELEMENT_BACK = 427931;
const StringHash HASH_ELEMENT_BIND = 430148;
const StringHash HASH_ELEMENT_BIND_MATERIAL = 27797804;
const StringHash HASH_ELEMENT_BIND_MATERIAL__TECHNIQUE_COMMON = 90983294;
const StringHash HASH_ELEMENT_BIND_SHAPE_MATRIX = 80689944;
const StringHash HASH_ELEMENT_BIND_VERTEX_INPUT = 217827748;
const StringHash HASH_ELEMENT_BLEND_COLOR = 171020066;
const StringHash HASH_ELEMENT_BLEND_ENABLE = 53919109;
const StringHash HASH_ELEMENT_BLEND_EQUATION = 175901966;
const StringHash HASH_ELEMENT_BLEND_EQUATION_SEPARATE = 213461493;
const StringHash HASH_ELEMENT_BLEND_EQUATION_SEPARATE__ALPHA = 13019489;
const StringHash HASH_ELEMENT_BLEND_FUNC = 77810307;
const StringHash HASH_ELEMENT_BLEND_FUNC_SEPARATE = 146822149;
const StringHash HASH_ELEMENT_BLINN = 6893646;
const StringHash HASH_ELEMENT_BOOL = 431708;
const StringHash HASH_ELEMENT_BOOL1 = 6907377;
const StringHash HASH_ELEMENT_BOOL1X1 = 157677777;
const StringHash HASH_ELEMENT_BOOL1X2 = 157677778;
const StringHash HASH_ELEMENT_BOOL1X3 = 157677779;
const StringHash HASH_ELEMENT_BOOL1X4 = 157677780;
const StringHash HASH_ELEMENT_BOOL2 = 6907378;
const StringHash HASH_ELEMENT_BOOL2X1 = 157678033;
const StringHash HASH_ELEMENT_BOOL2X2 = 157678034;
const StringHash HASH_ELEMENT_BOOL2X3 = 157678035;
const StringHash HASH_ELEMENT_BOOL2X4 = 157678036;
const StringHash HASH_ELEMENT_BOOL2____BOOL2 = 50914834;
const StringHash HASH_ELEMENT_BOOL2____CG_BOOL2 = 90666386;
const StringHash HASH_ELEMENT_BOOL2____GLSL_BOOL2 = 6913586;
const StringHash HASH_ELEMENT_BOOL3 = 6907379;
const StringHash HASH_ELEMENT_BOOL3X1 = 157678289;
const StringHash HASH_ELEMENT_BOOL3X2 = 157678290;
const StringHash HASH_ELEMENT_BOOL3X3 = 157678291;
const StringHash HASH_ELEMENT_BOOL3X4 = 157678292;
const StringHash HASH_ELEMENT_BOOL3____BOOL3 = 50910739;
const StringHash HASH_ELEMENT_BOOL3____CG_BOOL3 = 107443603;
const StringHash HASH_ELEMENT_BOOL3____GLSL_BOOL3 = 6913843;
const StringHash HASH_ELEMENT_BOOL4 = 6907380;
const StringHash HASH_ELEMENT_BOOL4X1 = 157678545;
const StringHash HASH_ELEMENT_BOOL4X2 = 157678546;
const StringHash HASH_ELEMENT_BOOL4X3 = 157678547;
const StringHash HASH_ELEMENT_BOOL4X4 = 157678548;
const StringHash HASH_ELEMENT_BOOL4____BOOL4 = 50923028;
const StringHash HASH_ELEMENT_BOOL4____CG_BOOL4 = 124220820;
const StringHash HASH_ELEMENT_BOOL4____GLSL_BOOL4 = 6913076;
const StringHash HASH_ELEMENT_BOOL_ARRAY = 39718633;
const StringHash HASH_ELEMENT_BOOL____BOOL = 65271820;
const StringHash HASH_ELEMENT_BOOL____CG_BOOL = 267439212;
const StringHash HASH_ELEMENT_BOOL____GLSL_BOOL = 222022220;
const StringHash HASH_ELEMENT_BORDER_COLOR = 47245730;
const StringHash HASH_ELEMENT_BOX = 26984;
const StringHash HASH_ELEMENT_CAMERA = 110640257;
const StringHash HASH_ELEMENT_CAPSULE = 159886405;
const StringHash HASH_ELEMENT_CG_SURFACE_TYPE____GENERATOR = 37457026;
const StringHash HASH_ELEMENT_CHANNEL = 166221020;
const StringHash HASH_ELEMENT_CHANNELS = 243617443;
const StringHash HASH_ELEMENT_CLEAR_COLOR = 137661154;
const StringHash HASH_ELEMENT_CLEAR_DEPTH = 137423912;
const StringHash HASH_ELEMENT_CLEAR_STENCIL = 8503804;
const StringHash HASH_ELEMENT_CLIP_PLANE = 107054405;
const StringHash HASH_ELEMENT_CLIP_PLANE_ENABLE = 62664181;
const StringHash HASH_ELEMENT_CODE = 435621;
const StringHash HASH_ELEMENT_COLLADA = 138479041;
const StringHash HASH_ELEMENT_COLOR = 6972258;
const StringHash HASH_ELEMENT_COLOR_CLEAR = 137644258;
const StringHash HASH_ELEMENT_COLOR_CLEAR____FX_CLEARCOLOR_COMMON = 64562366;
const StringHash HASH_ELEMENT_COLOR_CLEAR____FX_COLOR_COMMON = 135547134;
const StringHash HASH_ELEMENT_COLOR_LOGIC_OP_ENABLE = 188045397;
const StringHash HASH_ELEMENT_COLOR_MASK = 109354667;
const StringHash HASH_ELEMENT_COLOR_MATERIAL = 244976620;
const StringHash HASH_ELEMENT_COLOR_MATERIAL_ENABLE = 203733285;
const StringHash HASH_ELEMENT_COLOR_MATERIAL__FACE = 15715221;
const StringHash HASH_ELEMENT_COLOR_MATERIAL__MODE = 15747493;
const StringHash HASH_ELEMENT_COLOR_TARGET = 965444;
const StringHash HASH_ELEMENT_COLOR_TARGET____FX_COLORTARGET_COMMON = 260850126;
const StringHash HASH_ELEMENT_COLOR_TARGET____GLES_RENDERTARGET_COMMON = 155379502;
const StringHash HASH_ELEMENT_COLOR____TARGETABLEFLOAT3 = 189850083;
const StringHash HASH_ELEMENT_COMMENTS = 105104147;
const StringHash HASH_ELEMENT_COMMON_COLOR_OR_TEXTURE_TYPE____COLOR = 143009378;
const StringHash HASH_ELEMENT_COMMON_COLOR_OR_TEXTURE_TYPE____PARAM = 146460541;
const StringHash HASH_ELEMENT_COMMON_FLOAT_OR_PARAM_TYPE____FLOAT = 148509924;
const StringHash HASH_ELEMENT_COMMON_FLOAT_OR_PARAM_TYPE____PARAM = 149137693;
const StringHash HASH_ELEMENT_COMPILER_OPTIONS = 25077875;
const StringHash HASH_ELEMENT_COMPILER_TARGET = 47762404;
const StringHash HASH_ELEMENT_CONNECT_PARAM = 67355581;
const StringHash HASH_ELEMENT_CONSTANT = 106603252;
const StringHash HASH_ELEMENT_CONSTANT_ATTENUATION = 96122782;
const StringHash HASH_ELEMENT_CONSTANT____GLES_TEXTURE_CONSTANT_TYPE = 9903477;
const StringHash HASH_ELEMENT_CONTRIBUTOR = 143896786;
const StringHash HASH_ELEMENT_CONTROLLER = 194286738;
const StringHash HASH_ELEMENT_CONTROL_VERTICES = 118372691;
const StringHash HASH_ELEMENT_CONVEX_MESH = 214980952;
const StringHash HASH_ELEMENT_COPYRIGHT = 134780820;
const StringHash HASH_ELEMENT_CREATED = 176917204;
const StringHash HASH_ELEMENT_CULL_FACE = 52800853;
const StringHash HASH_ELEMENT_CULL_FACE_ENABLE = 134131333;
const StringHash HASH_ELEMENT_CYLINDER = 3165298;
const StringHash HASH_ELEMENT_DAMPING = 176451623;
const StringHash HASH_ELEMENT_DATA = 436385;
const StringHash HASH_ELEMENT_DENSITY = 180723929;
const StringHash HASH_ELEMENT_DEPTH_BOUNDS = 54189651;
const StringHash HASH_ELEMENT_DEPTH_BOUNDS_ENABLE = 103390949;
const StringHash HASH_ELEMENT_DEPTH_CLAMP_ENABLE = 118122469;
const StringHash HASH_ELEMENT_DEPTH_CLEAR = 238349346;
const StringHash HASH_ELEMENT_DEPTH_CLEAR____FLOAT = 195009940;
const StringHash HASH_ELEMENT_DEPTH_CLEAR____FX_CLEARDEPTH_COMMON = 99740910;
const StringHash HASH_ELEMENT_DEPTH_FUNC = 182679603;
const StringHash HASH_ELEMENT_DEPTH_MASK = 182752491;
const StringHash HASH_ELEMENT_DEPTH_RANGE = 235062133;
const StringHash HASH_ELEMENT_DEPTH_TARGET = 325412;
const StringHash HASH_ELEMENT_DEPTH_TARGET____FX_DEPTHTARGET_COMMON = 264162222;
const StringHash HASH_ELEMENT_DEPTH_TARGET____GLES_RENDERTARGET_COMMON = 81980142;
const StringHash HASH_ELEMENT_DEPTH_TEST_ENABLE = 197424741;
const StringHash HASH_ELEMENT_DEST = 437412;
const StringHash HASH_ELEMENT_DEST_ALPHA = 173185601;
const StringHash HASH_ELEMENT_DEST_RGB = 212495986;
const StringHash HASH_ELEMENT_DIFFUSE = 184343797;
const StringHash HASH_ELEMENT_DIRECTIONAL = 180710604;
const StringHash HASH_ELEMENT_DITHER_ENABLE = 248419589;
const StringHash HASH_ELEMENT_DRAW = 440455;
const StringHash HASH_ELEMENT_DYNAMIC = 201622419;
const StringHash HASH_ELEMENT_DYNAMIC_FRICTION = 12843982;
const StringHash HASH_ELEMENT_EFFECT = 113036196;
const StringHash HASH_ELEMENT_ELLIPSOID = 50858436;
const StringHash HASH_ELEMENT_EMISSION = 67803806;
const StringHash HASH_ELEMENT_ENABLED = 206017236;
const StringHash HASH_ELEMENT_ENUM = 443837;
const StringHash HASH_ELEMENT_ENUM____GLES_ENUMERATION = 4511230;
const StringHash HASH_ELEMENT_ENUM____GL_ENUMERATION = 260138062;
const StringHash HASH_ELEMENT_ENUM____STRING = 57204183;
const StringHash HASH_ELEMENT_EQUATION = 146320030;
const StringHash HASH_ELEMENT_EVALUATE_SCENE = 132785701;
const StringHash HASH_ELEMENT_EXTRA = 7142273;
const StringHash HASH_ELEMENT_FACE = HASH_ATTRIBUTE_face;
const StringHash HASH_ELEMENT_FAIL = 444412;
const StringHash HASH_ELEMENT_FALLOFF_ANGLE = 148208005;
const StringHash HASH_ELEMENT_FALLOFF_EXPONENT = 206620580;
const StringHash HASH_ELEMENT_FIXED = 7147188;
const StringHash HASH_ELEMENT_FIXED1 = 114355057;
const StringHash HASH_ELEMENT_FIXED1X1 = 15433313;
const StringHash HASH_ELEMENT_FIXED1X2 = 15433314;
const StringHash HASH_ELEMENT_FIXED1X3 = 15433315;
const StringHash HASH_ELEMENT_FIXED1X4 = 15433316;
const StringHash HASH_ELEMENT_FIXED2 = 114355058;
const StringHash HASH_ELEMENT_FIXED2X1 = 15433569;
const StringHash HASH_ELEMENT_FIXED2X2 = 15433570;
const StringHash HASH_ELEMENT_FIXED2X3 = 15433571;
const StringHash HASH_ELEMENT_FIXED2X4 = 15433572;
const StringHash HASH_ELEMENT_FIXED3 = 114355059;
const StringHash HASH_ELEMENT_FIXED3X1 = 15432801;
const StringHash HASH_ELEMENT_FIXED3X2 = 15432802;
const StringHash HASH_ELEMENT_FIXED3X3 = 15432803;
const StringHash HASH_ELEMENT_FIXED3X4 = 15432804;
const StringHash HASH_ELEMENT_FIXED4 = 114355060;
const StringHash HASH_ELEMENT_FIXED4X1 = 15433057;
const StringHash HASH_ELEMENT_FIXED4X2 = 15433058;
const StringHash HASH_ELEMENT_FIXED4X3 = 15433059;
const StringHash HASH_ELEMENT_FIXED4X4 = 15433060;
const StringHash HASH_ELEMENT_FLOAT = 7157124;
const StringHash HASH_ELEMENT_FLOAT1 = 114514033;
const StringHash HASH_ELEMENT_FLOAT1X1 = 56131169;
const StringHash HASH_ELEMENT_FLOAT1X1____CG_FLOAT1X1 = 132632465;
const StringHash HASH_ELEMENT_FLOAT1X1____FLOAT = 79386452;
const StringHash HASH_ELEMENT_FLOAT1X2 = 56131170;
const StringHash HASH_ELEMENT_FLOAT1X2____CG_FLOAT1X2 = 132636562;
const StringHash HASH_ELEMENT_FLOAT1X2____FLOAT2 = 196638002;
const StringHash HASH_ELEMENT_FLOAT1X3 = 56131171;
const StringHash HASH_ELEMENT_FLOAT1X3____CG_FLOAT1X3 = 132607891;
const StringHash HASH_ELEMENT_FLOAT1X3____FLOAT3 = 197096755;
const StringHash HASH_ELEMENT_FLOAT1X4 = 56131172;
const StringHash HASH_ELEMENT_FLOAT1X4____CG_FLOAT1X4 = 132644756;
const StringHash HASH_ELEMENT_FLOAT1X4____FLOAT4 = 196769076;
const StringHash HASH_ELEMENT_FLOAT2 = 114514034;
const StringHash HASH_ELEMENT_FLOAT2X1 = 56131425;
const StringHash HASH_ELEMENT_FLOAT2X1____CG_FLOAT2X1 = 133680785;
const StringHash HASH_ELEMENT_FLOAT2X1____FLOAT2 = 179664178;
const StringHash HASH_ELEMENT_FLOAT2X2 = 56131426;
const StringHash HASH_ELEMENT_FLOAT2X2____CG_FLOAT2X2 = 133684882;
const StringHash HASH_ELEMENT_FLOAT2X2____FLOAT2X2 = 141898498;
const StringHash HASH_ELEMENT_FLOAT2X2____GLSL_FLOAT2X2 = 255109314;
const StringHash HASH_ELEMENT_FLOAT2X3 = 56131427;
const StringHash HASH_ELEMENT_FLOAT2X3____CG_FLOAT2X3 = 133656211;
const StringHash HASH_ELEMENT_FLOAT2X3____FLOAT2X3 = 259339011;
const StringHash HASH_ELEMENT_FLOAT2X4 = 56131428;
const StringHash HASH_ELEMENT_FLOAT2X4____CG_FLOAT2X4 = 133693076;
const StringHash HASH_ELEMENT_FLOAT2X4____FLOAT2X4 = 175452932;
const StringHash HASH_ELEMENT_FLOAT2____CG_FLOAT2 = 257001090;
const StringHash HASH_ELEMENT_FLOAT2____FLOAT2 = 230813778;
const StringHash HASH_ELEMENT_FLOAT2____GLSL_FLOAT2 = 120212690;
const StringHash HASH_ELEMENT_FLOAT3 = 114514035;
const StringHash HASH_ELEMENT_FLOAT3X1 = 56130657;
const StringHash HASH_ELEMENT_FLOAT3X1____CG_FLOAT3X1 = 130534801;
const StringHash HASH_ELEMENT_FLOAT3X1____FLOAT3 = 162886963;
const StringHash HASH_ELEMENT_FLOAT3X2 = 56130658;
const StringHash HASH_ELEMENT_FLOAT3X2____CG_FLOAT3X2 = 130538898;
const StringHash HASH_ELEMENT_FLOAT3X2____FLOAT3X2 = HASH_ELEMENT_FLOAT2X2____FLOAT2X2;
const StringHash HASH_ELEMENT_FLOAT3X3 = 56130659;
const StringHash HASH_ELEMENT_FLOAT3X3____CG_FLOAT3X3 = 130510227;
const StringHash HASH_ELEMENT_FLOAT3X3____FLOAT3X3 = HASH_ELEMENT_FLOAT2X3____FLOAT2X3;
const StringHash HASH_ELEMENT_FLOAT3X3____GLSL_FLOAT3X3 = 262450131;
const StringHash HASH_ELEMENT_FLOAT3X4 = 56130660;
const StringHash HASH_ELEMENT_FLOAT3X4____CG_FLOAT3X4 = 130547092;
const StringHash HASH_ELEMENT_FLOAT3X4____FLOAT3X4 = HASH_ELEMENT_FLOAT2X4____FLOAT2X4;
const StringHash HASH_ELEMENT_FLOAT3____CG_FLOAT3 = 257001011;
const StringHash HASH_ELEMENT_FLOAT3____FLOAT3 = 231665747;
const StringHash HASH_ELEMENT_FLOAT3____GLSL_FLOAT3 = 120233171;
const StringHash HASH_ELEMENT_FLOAT4 = 114514036;
const StringHash HASH_ELEMENT_FLOAT4X1 = 56130913;
const StringHash HASH_ELEMENT_FLOAT4X1____CG_FLOAT4X1 = 131583121;
const StringHash HASH_ELEMENT_FLOAT4X1____FLOAT4 = 146109748;
const StringHash HASH_ELEMENT_FLOAT4X2 = 56130914;
const StringHash HASH_ELEMENT_FLOAT4X2____CG_FLOAT4X2 = 131587218;
const StringHash HASH_ELEMENT_FLOAT4X2____FLOAT4X2 = HASH_ELEMENT_FLOAT2X2____FLOAT2X2;
const StringHash HASH_ELEMENT_FLOAT4X3 = 56130915;
const StringHash HASH_ELEMENT_FLOAT4X3____CG_FLOAT4X3 = 131558547;
const StringHash HASH_ELEMENT_FLOAT4X3____FLOAT4X3 = HASH_ELEMENT_FLOAT2X3____FLOAT2X3;
const StringHash HASH_ELEMENT_FLOAT4X4 = 56130916;
const StringHash HASH_ELEMENT_FLOAT4X4____CG_FLOAT4X4 = 131595412;
const StringHash HASH_ELEMENT_FLOAT4X4____FLOAT4X4 = HASH_ELEMENT_FLOAT2X4____FLOAT2X4;
const StringHash HASH_ELEMENT_FLOAT4X4____GLSL_FLOAT4X4 = 265595620;
const StringHash HASH_ELEMENT_FLOAT4____CG_FLOAT4 = 257001892;
const StringHash HASH_ELEMENT_FLOAT4____FLOAT4 = 223604820;
const StringHash HASH_ELEMENT_FLOAT4____GLSL_FLOAT4 = 119893204;
const StringHash HASH_ELEMENT_FLOAT_ARRAY = 171289865;
const StringHash HASH_ELEMENT_FLOAT____CG_FLOAT = 124237204;
const StringHash HASH_ELEMENT_FLOAT____FLOAT = 134583844;
const StringHash HASH_ELEMENT_FLOAT____GLSL_FLOAT = 215833652;
const StringHash HASH_ELEMENT_FOG_COLOR = 224022578;
const StringHash HASH_ELEMENT_FOG_COORD_SRC = 198521027;
const StringHash HASH_ELEMENT_FOG_DENSITY = 183033321;
const StringHash HASH_ELEMENT_FOG_ENABLE = 96644853;
const StringHash HASH_ELEMENT_FOG_END = 224222244;
const StringHash HASH_ELEMENT_FOG_MODE = 97928053;
const StringHash HASH_ELEMENT_FOG_START = 225101252;
const StringHash HASH_ELEMENT_FORCE_FIELD = 187798708;
const StringHash HASH_ELEMENT_FORMAT = HASH_ATTRIBUTE_format;
const StringHash HASH_ELEMENT_FORMAT_HINT = 151553892;
const StringHash HASH_ELEMENT_FRONT = 7181908;
const StringHash HASH_ELEMENT_FRONT_FACE = 94576373;
const StringHash HASH_ELEMENT_FUNC = 449603;
const StringHash HASH_ELEMENT_FX_PROFILE_ABSTRACT = 143616996;
const StringHash HASH_ELEMENT_FX_SURFACE_INIT_CUBE_COMMON____ALL = 189838860;
const StringHash HASH_ELEMENT_FX_SURFACE_INIT_CUBE_COMMON____FACE = 84665637;
const StringHash HASH_ELEMENT_FX_SURFACE_INIT_CUBE_COMMON____PRIMARY = 94726169;
const StringHash HASH_ELEMENT_FX_SURFACE_INIT_PLANAR_COMMON____ALL = 266015324;
const StringHash HASH_ELEMENT_FX_SURFACE_INIT_VOLUME_COMMON____ALL = 124181564;
const StringHash HASH_ELEMENT_FX_SURFACE_INIT_VOLUME_COMMON____PRIMARY = 194600569;
const StringHash HASH_ELEMENT_GENERATOR = 80275618;
const StringHash HASH_ELEMENT_GEOMETRY = 207867209;
const StringHash HASH_ELEMENT_GLSL_SURFACE_TYPE____GENERATOR = 14595458;
const StringHash HASH_ELEMENT_GL_HOOK_ABSTRACT = 114258052;
const StringHash HASH_ELEMENT_GRAVITY = 243847385;
const StringHash HASH_ELEMENT_H = 104;
const StringHash HASH_ELEMENT_HALF = 452646;
const StringHash HASH_ELEMENT_HALF1 = 7242385;
const StringHash HASH_ELEMENT_HALF1X1 = 243439825;
const StringHash HASH_ELEMENT_HALF1X2 = 243439826;
const StringHash HASH_ELEMENT_HALF1X3 = 243439827;
const StringHash HASH_ELEMENT_HALF1X4 = 243439828;
const StringHash HASH_ELEMENT_HALF2 = 7242386;
const StringHash HASH_ELEMENT_HALF2X1 = 243440081;
const StringHash HASH_ELEMENT_HALF2X2 = 243440082;
const StringHash HASH_ELEMENT_HALF2X3 = 243440083;
const StringHash HASH_ELEMENT_HALF2X4 = 243440084;
const StringHash HASH_ELEMENT_HALF3 = 7242387;
const StringHash HASH_ELEMENT_HALF3X1 = 243440337;
const StringHash HASH_ELEMENT_HALF3X2 = 243440338;
const StringHash HASH_ELEMENT_HALF3X3 = 243440339;
const StringHash HASH_ELEMENT_HALF3X4 = 243440340;
const StringHash HASH_ELEMENT_HALF4 = 7242388;
const StringHash HASH_ELEMENT_HALF4X1 = 243440593;
const StringHash HASH_ELEMENT_HALF4X2 = 243440594;
const StringHash HASH_ELEMENT_HALF4X3 = 243440595;
const StringHash HASH_ELEMENT_HALF4X4 = 243440596;
const StringHash HASH_ELEMENT_HALF_EXTENTS = 168995299;
const StringHash HASH_ELEMENT_HEIGHT = HASH_ATTRIBUTE_height;
const StringHash HASH_ELEMENT_HOLLOW = 116798311;
const StringHash HASH_ELEMENT_IDREF_ARRAY = 202706457;
const StringHash HASH_ELEMENT_IMAGE = 7354325;
const StringHash HASH_ELEMENT_IMAGER = 117669314;
const StringHash HASH_ELEMENT_INCLUDE = 4864981;
const StringHash HASH_ELEMENT_INDEX_OF_REFRACTION = 242674622;
const StringHash HASH_ELEMENT_INERTIA = 5020289;
const StringHash HASH_ELEMENT_INIT_AS_NULL = 261182076;
const StringHash HASH_ELEMENT_INIT_AS_TARGET = 61002324;
const StringHash HASH_ELEMENT_INIT_CUBE = 10869717;
const StringHash HASH_ELEMENT_INIT_FROM = 10856717;
const StringHash HASH_ELEMENT_INIT_FROM____ANYURI = 209066137;
const StringHash HASH_ELEMENT_INIT_FROM____FX_SURFACE_INIT_FROM_COMMON = 1012638;
const StringHash HASH_ELEMENT_INIT_PLANAR = 102904866;
const StringHash HASH_ELEMENT_INIT_VOLUME = 113667221;
const StringHash HASH_ELEMENT_INPUT = 7362500;
const StringHash HASH_ELEMENT_INPUT____INPUTLOCAL = 162993740;
const StringHash HASH_ELEMENT_INPUT____INPUTLOCALOFFSET = 183226260;
const StringHash HASH_ELEMENT_INSTANCE_ANIMATION = 228086430;
const StringHash HASH_ELEMENT_INSTANCE_CAMERA = 255854209;
const StringHash HASH_ELEMENT_INSTANCE_CONTROLLER = 26967202;
const StringHash HASH_ELEMENT_INSTANCE_EFFECT = 253030820;
const StringHash HASH_ELEMENT_INSTANCE_EFFECT__SETPARAM = 219277853;
const StringHash HASH_ELEMENT_INSTANCE_FORCE_FIELD = 195760404;
const StringHash HASH_ELEMENT_INSTANCE_GEOMETRY = 75089129;
const StringHash HASH_ELEMENT_INSTANCE_LIGHT = 17333844;
const StringHash HASH_ELEMENT_INSTANCE_MATERIAL = 9871340;
const StringHash HASH_ELEMENT_INSTANCE_MATERIAL__BIND = 48435636;
const StringHash HASH_ELEMENT_INSTANCE_NODE = 168885653;
const StringHash HASH_ELEMENT_INSTANCE_PHYSICS_MATERIAL = 190501244;
const StringHash HASH_ELEMENT_INSTANCE_PHYSICS_MODEL = 98657756;
const StringHash HASH_ELEMENT_INSTANCE_PHYSICS_SCENE = 99263781;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY = 95007321;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON = 107721262;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC = 15147027;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME = 258084517;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE = 64146181;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW = 121584071;
const StringHash HASH_ELEMENT_INSTANCE_RIGID_CONSTRAINT = 252820964;
const StringHash HASH_ELEMENT_INSTANCE_VISUAL_SCENE = 235998149;
const StringHash HASH_ELEMENT_INT = 28756;
const StringHash HASH_ELEMENT_INT1 = 460145;
const StringHash HASH_ELEMENT_INT1X1 = 117799089;
const StringHash HASH_ELEMENT_INT1X2 = 117799090;
const StringHash HASH_ELEMENT_INT1X3 = 117799091;
const StringHash HASH_ELEMENT_INT1X4 = 117799092;
const StringHash HASH_ELEMENT_INT2 = 460146;
const StringHash HASH_ELEMENT_INT2X1 = 117799345;
const StringHash HASH_ELEMENT_INT2X2 = 117799346;
const StringHash HASH_ELEMENT_INT2X3 = 117799347;
const StringHash HASH_ELEMENT_INT2X4 = 117799348;
const StringHash HASH_ELEMENT_INT2____CG_INT2 = 240173170;
const StringHash HASH_ELEMENT_INT2____GLSL_INT2 = 224464578;
const StringHash HASH_ELEMENT_INT2____INT2 = 72391970;
const StringHash HASH_ELEMENT_INT3 = 460147;
const StringHash HASH_ELEMENT_INT3X1 = 117799601;
const StringHash HASH_ELEMENT_INT3X2 = 117799602;
const StringHash HASH_ELEMENT_INT3X3 = 117799603;
const StringHash HASH_ELEMENT_INT3X4 = 117799604;
const StringHash HASH_ELEMENT_INT3____CG_INT3 = 241221747;
const StringHash HASH_ELEMENT_INT3____GLSL_INT3 = 224464595;
const StringHash HASH_ELEMENT_INT3____INT3 = 72391715;
const StringHash HASH_ELEMENT_INT4 = 460148;
const StringHash HASH_ELEMENT_INT4X1 = 117799857;
const StringHash HASH_ELEMENT_INT4X2 = 117799858;
const StringHash HASH_ELEMENT_INT4X3 = 117799859;
const StringHash HASH_ELEMENT_INT4X4 = 117799860;
const StringHash HASH_ELEMENT_INT4____CG_INT4 = 242270324;
const StringHash HASH_ELEMENT_INT4____GLSL_INT4 = 224464612;
const StringHash HASH_ELEMENT_INT4____INT4 = 72392484;
const StringHash HASH_ELEMENT_INTERPENETRATE = 23533925;
const StringHash HASH_ELEMENT_INT_ARRAY = 173598937;
const StringHash HASH_ELEMENT_INT____CG_INT = 96930580;
const StringHash HASH_ELEMENT_INT____GLSL_INT = 164961220;
const StringHash HASH_ELEMENT_INT____INT = 89414644;
const StringHash HASH_ELEMENT_JOINTS = 118883763;
const StringHash HASH_ELEMENT_KEYWORDS = 219049891;
const StringHash HASH_ELEMENT_LAMBERT = 42175716;
const StringHash HASH_ELEMENT_LAYER = HASH_ATTRIBUTE_layer;
const StringHash HASH_ELEMENT_LIBRARY_ANIMATIONS = 223353555;
const StringHash HASH_ELEMENT_LIBRARY_ANIMATION_CLIPS = 210579923;
const StringHash HASH_ELEMENT_LIBRARY_CAMERAS = 17507619;
const StringHash HASH_ELEMENT_LIBRARY_CONTROLLERS = 117752259;
const StringHash HASH_ELEMENT_LIBRARY_EFFECTS = 38033171;
const StringHash HASH_ELEMENT_LIBRARY_FORCE_FIELDS = 262260019;
const StringHash HASH_ELEMENT_LIBRARY_GEOMETRIES = 219269923;
const StringHash HASH_ELEMENT_LIBRARY_IMAGES = 175895315;
const StringHash HASH_ELEMENT_LIBRARY_LIGHTS = 196563299;
const StringHash HASH_ELEMENT_LIBRARY_MATERIALS = 35999283;
const StringHash HASH_ELEMENT_LIBRARY_NODES = 230609443;
const StringHash HASH_ELEMENT_LIBRARY_PHYSICS_MATERIALS = 149953987;
const StringHash HASH_ELEMENT_LIBRARY_PHYSICS_MODELS = 247630259;
const StringHash HASH_ELEMENT_LIBRARY_PHYSICS_SCENES = 236889923;
const StringHash HASH_ELEMENT_LIBRARY_VISUAL_SCENES = 1834835;
const StringHash HASH_ELEMENT_LIGHT = 7536116;
const StringHash HASH_ELEMENT_LIGHTING_ENABLE = 140439397;
const StringHash HASH_ELEMENT_LIGHT_AMBIENT = 113574516;
const StringHash HASH_ELEMENT_LIGHT_CONSTANT_ATTENUATION = 107287662;
const StringHash HASH_ELEMENT_LIGHT_DIFFUSE = 134293173;
const StringHash HASH_ELEMENT_LIGHT_ENABLE = 44804229;
const StringHash HASH_ELEMENT_LIGHT_LINEAR_ATTENUATION = 14779134;
const StringHash HASH_ELEMENT_LIGHT_LINEAR_ATTENUTATION = 154814046;
const StringHash HASH_ELEMENT_LIGHT_MODEL_AMBIENT = 111126580;
const StringHash HASH_ELEMENT_LIGHT_MODEL_COLOR_CONTROL = 90408300;
const StringHash HASH_ELEMENT_LIGHT_MODEL_LOCAL_VIEWER_ENABLE = 147233621;
const StringHash HASH_ELEMENT_LIGHT_MODEL_TWO_SIDE_ENABLE = 26336277;
const StringHash HASH_ELEMENT_LIGHT_POSITION = 157224718;
const StringHash HASH_ELEMENT_LIGHT_QUADRATIC_ATTENUATION = 111314510;
const StringHash HASH_ELEMENT_LIGHT_SPECULAR = 157623042;
const StringHash HASH_ELEMENT_LIGHT_SPOT_CUTOFF = 263462582;
const StringHash HASH_ELEMENT_LIGHT_SPOT_DIRECTION = 34428126;
const StringHash HASH_ELEMENT_LIGHT_SPOT_EXPONENT = 132068740;
const StringHash HASH_ELEMENT_LIGHT__TECHNIQUE_COMMON = 9633678;
const StringHash HASH_ELEMENT_LIGHT__TECHNIQUE_COMMON__AMBIENT = 163690020;
const StringHash HASH_ELEMENT_LIMITS = 120602803;
const StringHash HASH_ELEMENT_LIMITS__LINEAR = 108038914;
const StringHash HASH_ELEMENT_LINEAR = 120605570;
const StringHash HASH_ELEMENT_LINEAR_ATTENUATION = 80764702;
const StringHash HASH_ELEMENT_LINES = 7537859;
const StringHash HASH_ELEMENT_LINESTRIPS = 212647987;
const StringHash HASH_ELEMENT_LINE_SMOOTH_ENABLE = 179460837;
const StringHash HASH_ELEMENT_LINE_STIPPLE = 228605509;
const StringHash HASH_ELEMENT_LINE_STIPPLE_ENABLE = 264740261;
const StringHash HASH_ELEMENT_LINE_WIDTH = 191548392;
const StringHash HASH_ELEMENT_LOGIC_OP = 98538320;
const StringHash HASH_ELEMENT_LOGIC_OP_ENABLE = 140332597;
const StringHash HASH_ELEMENT_LOOKAT = 121004420;
const StringHash HASH_ELEMENT_MAGFILTER = 231754162;
const StringHash HASH_ELEMENT_MASK = 473243;
const StringHash HASH_ELEMENT_MASS = 473251;
const StringHash HASH_ELEMENT_MASS_FRAME = 156942485;
const StringHash HASH_ELEMENT_MATERIAL = HASH_ATTRIBUTE_material;
const StringHash HASH_ELEMENT_MATERIAL_AMBIENT = 212902564;
const StringHash HASH_ELEMENT_MATERIAL_DIFFUSE = 108299877;
const StringHash HASH_ELEMENT_MATERIAL_EMISSION = 210711006;
const StringHash HASH_ELEMENT_MATERIAL_SHININESS = 135694787;
const StringHash HASH_ELEMENT_MATERIAL_SPECULAR = 239021794;
const StringHash HASH_ELEMENT_MATRIX = 121157896;
const StringHash HASH_ELEMENT_MAX = 29576;
const StringHash HASH_ELEMENT_MESH = 474264;
const StringHash HASH_ELEMENT_MIN = 29694;
const StringHash HASH_ELEMENT_MINFILTER = 80760514;
const StringHash HASH_ELEMENT_MIPFILTER = 114314946;
const StringHash HASH_ELEMENT_MIPMAP_BIAS = 137463987;
const StringHash HASH_ELEMENT_MIPMAP_GENERATE = 225753461;
const StringHash HASH_ELEMENT_MIPMAP_MAXLEVEL = 267058828;
const StringHash HASH_ELEMENT_MIP_LEVELS = 103517267;
const StringHash HASH_ELEMENT_MODE = 476581;
const StringHash HASH_ELEMENT_MODEL_VIEW_MATRIX = 136322840;
const StringHash HASH_ELEMENT_MODIFIED = 95406324;
const StringHash HASH_ELEMENT_MODIFIER = 95406210;
const StringHash HASH_ELEMENT_MORPH = 7629160;
const StringHash HASH_ELEMENT_MULTISAMPLE_ENABLE = 202117781;
const StringHash HASH_ELEMENT_NAME = HASH_ATTRIBUTE_name;
const StringHash HASH_ELEMENT_NAME_ARRAY = 190697657;
const StringHash HASH_ELEMENT_NEWPARAM = 216436541;
const StringHash HASH_ELEMENT_NEWPARAM____CG_NEWPARAM = 21743373;
const StringHash HASH_ELEMENT_NEWPARAM____COMMON_NEWPARAM_TYPE = 159994405;
const StringHash HASH_ELEMENT_NEWPARAM____FX_NEWPARAM_COMMON = 25121566;
const StringHash HASH_ELEMENT_NEWPARAM____GLES_NEWPARAM = 103891357;
const StringHash HASH_ELEMENT_NEWPARAM____GLSL_NEWPARAM = 103133597;
const StringHash HASH_ELEMENT_NODE = 480677;
const StringHash HASH_ELEMENT_NORMALIZE_ENABLE = 104996709;
const StringHash HASH_ELEMENT_OPTICS = 124235683;
const StringHash HASH_ELEMENT_OPTICS__TECHNIQUE_COMMON = 170249678;
const StringHash HASH_ELEMENT_OPTION = 124235870;
const StringHash HASH_ELEMENT_ORDER = 7768770;
const StringHash HASH_ELEMENT_ORTHOGRAPHIC = 165790115;
const StringHash HASH_ELEMENT_P = 112;
const StringHash HASH_ELEMENT_PARAM = HASH_ATTRIBUTE_param;
const StringHash HASH_ELEMENT_PARAM____NCNAME = 212141541;
const StringHash HASH_ELEMENT_PASS = 485539;
const StringHash HASH_ELEMENT_PERSPECTIVE = 114063717;
const StringHash HASH_ELEMENT_PH = 1896;
const StringHash HASH_ELEMENT_PHONG = 7796295;
const StringHash HASH_ELEMENT_PHYSICS_MATERIAL = 22186316;
const StringHash HASH_ELEMENT_PHYSICS_MATERIAL__TECHNIQUE_COMMON = 99789790;
const StringHash HASH_ELEMENT_PHYSICS_MODEL = 6604124;
const StringHash HASH_ELEMENT_PHYSICS_SCENE = 6882725;
const StringHash HASH_ELEMENT_PHYSICS_SCENE__TECHNIQUE_COMMON = 53159934;
const StringHash HASH_ELEMENT_PLANE = 7809093;
const StringHash HASH_ELEMENT_POINT = 7823444;
const StringHash HASH_ELEMENT_POINT_DISTANCE_ATTENUATION = 139457678;
const StringHash HASH_ELEMENT_POINT_FADE_THRESHOLD_SIZE = 101782757;
const StringHash HASH_ELEMENT_POINT_SIZE = 94500613;
const StringHash HASH_ELEMENT_POINT_SIZE_MAX = 118555256;
const StringHash HASH_ELEMENT_POINT_SIZE_MIN = 118555150;
const StringHash HASH_ELEMENT_POINT_SMOOTH_ENABLE = 182602229;
const StringHash HASH_ELEMENT_POLYGONS = 104850211;
const StringHash HASH_ELEMENT_POLYGON_MODE = 55843397;
const StringHash HASH_ELEMENT_POLYGON_MODE__FACE = 169991637;
const StringHash HASH_ELEMENT_POLYGON_MODE__MODE = 170016741;
const StringHash HASH_ELEMENT_POLYGON_OFFSET = 73611924;
const StringHash HASH_ELEMENT_POLYGON_OFFSET_FILL_ENABLE = 194749109;
const StringHash HASH_ELEMENT_POLYGON_OFFSET_LINE_ENABLE = 201697125;
const StringHash HASH_ELEMENT_POLYGON_OFFSET_POINT_ENABLE = 98702757;
const StringHash HASH_ELEMENT_POLYGON_SMOOTH_ENABLE = 242755781;
const StringHash HASH_ELEMENT_POLYGON_STIPPLE_ENABLE = 56638309;
const StringHash HASH_ELEMENT_POLYLIST = 104871892;
const StringHash HASH_ELEMENT_PRECISION = 195095006;
const StringHash HASH_ELEMENT_PRIMARY = 126892265;
const StringHash HASH_ELEMENT_PROFILE_CG = 218491431;
const StringHash HASH_ELEMENT_PROFILE_CG__NEWPARAM__SURFACE__GENERATOR__NAME = 159447349;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE = 198327461;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS = 44323843;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER = 46821682;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__BIND = 111148916;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__BIND__PARAM = 244936829;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__COMPILER_TARGET = 28270324;
const StringHash HASH_ELEMENT_PROFILE_CG__TECHNIQUE__PASS__SHADER__NAME = 111163141;
const StringHash HASH_ELEMENT_PROFILE_COMMON = 171910622;
const StringHash HASH_ELEMENT_PROFILE_COMMON__TECHNIQUE = 123572309;
const StringHash HASH_ELEMENT_PROFILE_COMMON__TECHNIQUE__CONSTANT = 100962372;
const StringHash HASH_ELEMENT_PROFILE_GLES = 99286435;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE = 52481493;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS = 175547763;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_FUNC = 221658707;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_FUNC__FUNC = 109307411;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_FUNC__VALUE = 135243477;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__ALPHA_TEST_ENABLE = 116446325;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_ENABLE = 110342341;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_FUNC = 132574611;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_FUNC__DEST = 11747588;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__BLEND_FUNC__SRC = 168494163;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLEAR_COLOR = 74334162;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLEAR_DEPTH = 74365720;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLEAR_STENCIL = 249101740;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLIP_PLANE = 119959605;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CLIP_PLANE_ENABLE = 132349125;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__COLOR_LOGIC_OP_ENABLE = 237586949;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__COLOR_MASK = 88570363;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__COLOR_MATERIAL_ENABLE = 154729141;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CULL_FACE = 103937893;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__CULL_FACE_ENABLE = 112922613;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_FUNC = 161928003;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_MASK = 161952155;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_RANGE = 173601861;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DEPTH_TEST_ENABLE = 268313397;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__DITHER_ENABLE = 83468661;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_COLOR = 208565858;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_DENSITY = 244493529;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_ENABLE = 117901797;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_END = 118255732;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_MODE = 13076181;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FOG_START = 207527828;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__FRONT_FACE = 115784101;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHTING_ENABLE = 260748629;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_AMBIENT = 9582196;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_CONSTANT_ATTENUATION = 253121470;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_DIFFUSE = 39195269;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_ENABLE = 21943749;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_MODEL_AMBIENT = 214461476;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_MODEL_TWO_SIDE_ENABLE = 6145669;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_POSITION = 206327470;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_QUADRATIC_ATTENUATION = 60211262;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPECULAR = 202793122;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPOT_CUTOFF = 193890726;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPOT_DIRECTION = 92018558;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LIGHT_SPOT_EXPONENT = 231350708;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LINE_SMOOTH_ENABLE = 193669669;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LINE_WIDTH = 170487960;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__LOGIC_OP = 11326192;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_AMBIENT = 166922196;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_DIFFUSE = 87499029;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_EMISSION = 11880510;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_SHININESS = 95681155;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MATERIAL_SPECULAR = 168131026;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MODEL_VIEW_MATRIX = 69093992;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__MULTISAMPLE_ENABLE = 137314773;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__NORMALIZE_ENABLE = 101481877;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_DISTANCE_ATTENUATION = 249345518;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_FADE_THRESHOLD_SIZE = 209316021;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SIZE = 115757461;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SIZE_MAX = 27117016;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SIZE_MIN = 27116974;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POINT_SMOOTH_ENABLE = 17399269;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POLYGON_OFFSET = 84967220;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__POLYGON_OFFSET_FILL_ENABLE = 151578389;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__PROJECTION_MATRIX = 195010584;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__RESCALE_NORMAL_ENABLE = 243886325;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 143218709;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_ONE_ENABLE = 99783653;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SAMPLE_COVERAGE_ENABLE = 254242853;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SCISSOR = 64462434;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SCISSOR_TEST_ENABLE = 1650213;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__SHADE_MODEL = 258075180;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC = 2756003;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC__FUNC = 159373283;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC__MASK = 159397179;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_FUNC__REF = 177719398;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_MASK = 2879355;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP = 132131760;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP__FAIL = 62254156;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP__ZFAIL = 192052428;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_OP__ZPASS = 192140179;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__STENCIL_TEST_ENABLE = 120835461;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__PASS__TEXTURE_PIPELINE = 149197237;
const StringHash HASH_ELEMENT_PROFILE_GLES__TECHNIQUE__SETPARAM = 23920173;
const StringHash HASH_ELEMENT_PROFILE_GLSL = 99286140;
const StringHash HASH_ELEMENT_PROFILE_GLSL__NEWPARAM__SURFACE__GENERATOR__NAME = 15584837;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE = 30461381;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS = 212247907;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_FUNC = 195444291;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_FUNC__FUNC = 83092995;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_FUNC__VALUE = 252684277;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__ALPHA_TEST_ENABLE = 267440917;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_ENABLE = 110336597;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_FUNC = 24571299;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_FUNC__DEST = 172179716;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__BLEND_FUNC__SRC = 178521171;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLEAR_COLOR = 158220466;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLEAR_DEPTH = 158251640;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLEAR_STENCIL = 249008300;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLIP_PLANE = 26636325;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CLIP_PLANE_ENABLE = 216234981;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__COLOR_LOGIC_OP_ENABLE = 226904581;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__COLOR_MASK = 60258763;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__COLOR_MATERIAL_ENABLE = 173406901;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CULL_FACE = 89847653;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__CULL_FACE_ENABLE = 2822181;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_FUNC = 255251283;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_MASK = 255275403;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_RANGE = 56161573;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DEPTH_TEST_ENABLE = 117318741;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__DITHER_ENABLE = 83414133;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_COLOR = 185038434;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_DENSITY = 93498297;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_ENABLE = 5704085;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_END = 118162804;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_MODE = 11654869;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FOG_START = 188063636;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__FRONT_FACE = 7780821;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHTING_ENABLE = 237090133;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_AMBIENT = 9657716;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_CONSTANT_ATTENUATION = 251708350;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_DIFFUSE = 39287685;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_ENABLE = 21939029;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_MODEL_AMBIENT = 214555940;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_MODEL_TWO_SIDE_ENABLE = 67815045;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_POSITION = 205184686;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_QUADRATIC_ATTENUATION = 16760894;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPECULAR = 201380002;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPOT_CUTOFF = 42895494;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPOT_DIRECTION = 91129726;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LIGHT_SPOT_EXPONENT = 231279284;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LINE_SMOOTH_ENABLE = 193667957;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LINE_WIDTH = 10055832;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__LOGIC_OP = 12223216;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_AMBIENT = 256051140;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_DIFFUSE = 59187461;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_EMISSION = 95766382;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_SHININESS = 95677491;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MATERIAL_SPECULAR = 17135794;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MODEL_VIEW_MATRIX = 186534216;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__MULTISAMPLE_ENABLE = 137325381;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__NORMALIZE_ENABLE = 54295621;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_DISTANCE_ATTENUATION = 250217966;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_FADE_THRESHOLD_SIZE = 209367477;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SIZE = 7754149;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SIZE_MAX = 27997656;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SIZE_MIN = 27997614;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POINT_SMOOTH_ENABLE = 15996645;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POLYGON_OFFSET = 84340532;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__POLYGON_OFFSET_FILL_ENABLE = 150886165;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__PROJECTION_MATRIX = 44014456;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__RESCALE_NORMAL_ENABLE = 153512181;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 143294229;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SAMPLE_ALPHA_TO_ONE_ENABLE = 99353573;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SAMPLE_COVERAGE_ENABLE = 230125525;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SCISSOR = 64419682;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SCISSOR_TEST_ENABLE = 1591077;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER = 3874610;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__BIND = 143561076;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__BIND__PARAM = 227516253;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__COMPILER_TARGET = 20989972;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADER__NAME = 143612165;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__SHADE_MODEL = 107080524;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC = 2767667;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC__FUNC = 159375731;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC__MASK = 159384491;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_FUNC__REF = 60279558;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_MASK = 2873835;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP = 24128384;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP__FAIL = 88468604;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP__ZFAIL = 74612652;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_OP__ZPASS = 74698995;
const StringHash HASH_ELEMENT_PROFILE_GLSL__TECHNIQUE__PASS__STENCIL_TEST_ENABLE = 120628869;
const StringHash HASH_ELEMENT_PROJECTION_MATRIX = 266718536;
const StringHash HASH_ELEMENT_QUADRATIC_ATTENUATION = 89712814;
const StringHash HASH_ELEMENT_RADIUS = 126333123;
const StringHash HASH_ELEMENT_RADIUS1 = 142281745;
const StringHash HASH_ELEMENT_RADIUS2 = 142281746;
const StringHash HASH_ELEMENT_RADIUS____FLOAT = 5388500;
const StringHash HASH_ELEMENT_RADIUS____FLOAT2 = 86216050;
const StringHash HASH_ELEMENT_RANGE = 7898325;
const StringHash HASH_ELEMENT_REF = HASH_ATTRIBUTE_ref;
const StringHash HASH_ELEMENT_REFLECTIVE = 45955861;
const StringHash HASH_ELEMENT_REFLECTIVITY = 221979145;
const StringHash HASH_ELEMENT_REF_ATTACHMENT = 207403124;
const StringHash HASH_ELEMENT_RENDER = 126634690;
const StringHash HASH_ELEMENT_RESCALE_NORMAL_ENABLE = 190127845;
const StringHash HASH_ELEMENT_RESTITUTION = 12871406;
const StringHash HASH_ELEMENT_REVISION = 214997470;
const StringHash HASH_ELEMENT_RGB = 22194;
const StringHash HASH_ELEMENT_RIGID_BODY = HASH_ATTRIBUTE_rigid_body;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON = 140646974;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__DYNAMIC = 174112899;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__MASS_FRAME = 90547925;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE = 55069861;
const StringHash HASH_ELEMENT_RIGID_BODY__TECHNIQUE_COMMON__SHAPE__HOLLOW = 230332791;
const StringHash HASH_ELEMENT_RIGID_CONSTRAINT = 85611988;
const StringHash HASH_ELEMENT_RIGID_CONSTRAINT__TECHNIQUE_COMMON = 28989358;
const StringHash HASH_ELEMENT_ROTATE = 127314085;
const StringHash HASH_ELEMENT_SAMPLER = 159675058;
const StringHash HASH_ELEMENT_SAMPLER1D = 74628308;
const StringHash HASH_ELEMENT_SAMPLER1D____CG_SAMPLER1D = 221373652;
const StringHash HASH_ELEMENT_SAMPLER1D____FX_SAMPLER1D_COMMON = 51154702;
const StringHash HASH_ELEMENT_SAMPLER1D____GL_SAMPLER1D = 223536340;
const StringHash HASH_ELEMENT_SAMPLER2D = 74628324;
const StringHash HASH_ELEMENT_SAMPLER2D____CG_SAMPLER2D = 220325092;
const StringHash HASH_ELEMENT_SAMPLER2D____FX_SAMPLER2D_COMMON = 67931662;
const StringHash HASH_ELEMENT_SAMPLER2D____GL_SAMPLER2D = 222487780;
const StringHash HASH_ELEMENT_SAMPLER3D = 74628340;
const StringHash HASH_ELEMENT_SAMPLER3D____CG_SAMPLER3D = 219276532;
const StringHash HASH_ELEMENT_SAMPLER3D____FX_SAMPLER3D_COMMON = 17599758;
const StringHash HASH_ELEMENT_SAMPLER3D____GL_SAMPLER3D = 221439220;
const StringHash HASH_ELEMENT_SAMPLERCUBE = 46075157;
const StringHash HASH_ELEMENT_SAMPLERCUBE____CG_SAMPLERCUBE = 94614933;
const StringHash HASH_ELEMENT_SAMPLERCUBE____FX_SAMPLERCUBE_COMMON = 264208206;
const StringHash HASH_ELEMENT_SAMPLERCUBE____GL_SAMPLERCUBE = 245609893;
const StringHash HASH_ELEMENT_SAMPLERDEPTH = 200336040;
const StringHash HASH_ELEMENT_SAMPLERDEPTH____CG_SAMPLERDEPTH = 111514472;
const StringHash HASH_ELEMENT_SAMPLERDEPTH____FX_SAMPLERDEPTH_COMMON = 266163102;
const StringHash HASH_ELEMENT_SAMPLERDEPTH____GL_SAMPLERDEPTH = 111516376;
const StringHash HASH_ELEMENT_SAMPLERRECT = 46067188;
const StringHash HASH_ELEMENT_SAMPLERRECT____CG_SAMPLERRECT = 10859924;
const StringHash HASH_ELEMENT_SAMPLERRECT____FX_SAMPLERRECT_COMMON = 266305518;
const StringHash HASH_ELEMENT_SAMPLERRECT____GL_SAMPLERRECT = 262518244;
const StringHash HASH_ELEMENT_SAMPLER_STATE = 267311637;
const StringHash HASH_ELEMENT_SAMPLER_STATE____GLES_SAMPLER_STATE = 47877797;
const StringHash HASH_ELEMENT_SAMPLER_STATE____NCNAME = 171113877;
const StringHash HASH_ELEMENT_SAMPLE_ALPHA_TO_COVERAGE_ENABLE = 47727685;
const StringHash HASH_ELEMENT_SAMPLE_ALPHA_TO_ONE_ENABLE = 251778565;
const StringHash HASH_ELEMENT_SAMPLE_COVERAGE_ENABLE = 241354933;
const StringHash HASH_ELEMENT_SCALE = HASH_ATTRIBUTE_scale;
const StringHash HASH_ELEMENT_SCENE = 7969861;
const StringHash HASH_ELEMENT_SCISSOR = 161524242;
const StringHash HASH_ELEMENT_SCISSOR_TEST_ENABLE = 177084853;
const StringHash HASH_ELEMENT_SEMANTIC = HASH_ATTRIBUTE_semantic;
const StringHash HASH_ELEMENT_SETPARAM = 213290989;
const StringHash HASH_ELEMENT_SETPARAM____CG_SETPARAM = 49241821;
const StringHash HASH_ELEMENT_SETPARAM____CG_SETPARAM_SIMPLE = 141273397;
const StringHash HASH_ELEMENT_SETPARAM____GLSL_SETPARAM = 42774717;
const StringHash HASH_ELEMENT_SETPARAM____GLSL_SETPARAM_SIMPLE = 240470837;
const StringHash HASH_ELEMENT_SHADER = 127826626;
const StringHash HASH_ELEMENT_SHADE_MODEL = 188464412;
const StringHash HASH_ELEMENT_SHAPE = 7989349;
const StringHash HASH_ELEMENT_SHININESS = 5256531;
const StringHash HASH_ELEMENT_SIZE = 499973;
const StringHash HASH_ELEMENT_SIZE____FLOAT3 = 81398915;
const StringHash HASH_ELEMENT_SIZE____INT3 = 83168803;
const StringHash HASH_ELEMENT_SKELETON = 29544190;
const StringHash HASH_ELEMENT_SKEW = 500167;
const StringHash HASH_ELEMENT_SKIN = 500222;
const StringHash HASH_ELEMENT_SOURCE = HASH_ATTRIBUTE_source;
const StringHash HASH_ELEMENT_SOURCE_DATA = 166708257;
const StringHash HASH_ELEMENT_SOURCE____NCNAME = 213562789;
const StringHash HASH_ELEMENT_SPECULAR = 112903458;
const StringHash HASH_ELEMENT_SPHERE = 128380037;
const StringHash HASH_ELEMENT_SPLINE = 128397381;
const StringHash HASH_ELEMENT_SPOT = 501604;
const StringHash HASH_ELEMENT_SPRING = 128421959;
const StringHash HASH_ELEMENT_SPRING__LINEAR = 62916466;
const StringHash HASH_ELEMENT_SRC = 31363;
const StringHash HASH_ELEMENT_SRC_ALPHA = 156781665;
const StringHash HASH_ELEMENT_SRC_RGB = 176777378;
const StringHash HASH_ELEMENT_STATIC_FRICTION = 4890926;
const StringHash HASH_ELEMENT_STENCIL_CLEAR = 217473554;
const StringHash HASH_ELEMENT_STENCIL_CLEAR____BYTE = 53976165;
const StringHash HASH_ELEMENT_STENCIL_CLEAR____FX_CLEARSTENCIL_COMMON = 71235438;
const StringHash HASH_ELEMENT_STENCIL_FUNC = 164569299;
const StringHash HASH_ELEMENT_STENCIL_FUNC_SEPARATE = 241541829;
const StringHash HASH_ELEMENT_STENCIL_FUNC_SEPARATE__MASK = 87136859;
const StringHash HASH_ELEMENT_STENCIL_FUNC_SEPARATE__REF = 206779030;
const StringHash HASH_ELEMENT_STENCIL_MASK = 164690955;
const StringHash HASH_ELEMENT_STENCIL_MASK_SEPARATE = 61416245;
const StringHash HASH_ELEMENT_STENCIL_MASK_SEPARATE__FACE = 184345765;
const StringHash HASH_ELEMENT_STENCIL_MASK_SEPARATE__MASK = 184359851;
const StringHash HASH_ELEMENT_STENCIL_OP = 77189280;
const StringHash HASH_ELEMENT_STENCIL_OP_SEPARATE = 23242661;
const StringHash HASH_ELEMENT_STENCIL_OP_SEPARATE__FACE = 204955701;
const StringHash HASH_ELEMENT_STENCIL_OP_SEPARATE__FAIL = 204955740;
const StringHash HASH_ELEMENT_STENCIL_OP_SEPARATE__ZFAIL = 56278332;
const StringHash HASH_ELEMENT_STENCIL_OP_SEPARATE__ZPASS = 56300131;
const StringHash HASH_ELEMENT_STENCIL_TARGET = 208409604;
const StringHash HASH_ELEMENT_STENCIL_TARGET____FX_STENCILTARGET_COMMON = 62244350;
const StringHash HASH_ELEMENT_STENCIL_TARGET____GLES_RENDERTARGET_COMMON = 110019150;
const StringHash HASH_ELEMENT_STENCIL_TEST_ENABLE = 159579189;
const StringHash HASH_ELEMENT_STIFFNESS = 265106947;
const StringHash HASH_ELEMENT_STRING = 128684103;
const StringHash HASH_ELEMENT_SUBJECT = 179899348;
const StringHash HASH_ELEMENT_SURFACE = 180930533;
const StringHash HASH_ELEMENT_SURFACE____CG_SURFACE_TYPE = 115551221;
const StringHash HASH_ELEMENT_SURFACE____FX_SURFACE_COMMON = 4957886;
const StringHash HASH_ELEMENT_SURFACE____GLSL_SURFACE_TYPE = 104738245;
const StringHash HASH_ELEMENT_SURFACE____NCNAME = 96983973;
const StringHash HASH_ELEMENT_SWING_CONE_AND_TWIST = 103048868;
const StringHash HASH_ELEMENT_TAPERED_CAPSULE = 256864501;
const StringHash HASH_ELEMENT_TAPERED_CYLINDER = 77858562;
const StringHash HASH_ELEMENT_TARGETS = 176741571;
const StringHash HASH_ELEMENT_TARGET_VALUE = 264572533;
const StringHash HASH_ELEMENT_TECHNIQUE = 167080453;
const StringHash HASH_ELEMENT_TECHNIQUE_COMMON = 181609502;
const StringHash HASH_ELEMENT_TECHNIQUE_HINT = 4897140;
const StringHash HASH_ELEMENT_TEXCOMBINER = 105007714;
const StringHash HASH_ELEMENT_TEXCOORD = HASH_ATTRIBUTE_texcoord;
const StringHash HASH_ELEMENT_TEXENV = 128773206;
const StringHash HASH_ELEMENT_TEXTURE = HASH_ATTRIBUTE_texture;
const StringHash HASH_ELEMENT_TEXTURE1D = 264041108;
const StringHash HASH_ELEMENT_TEXTURE1D_ENABLE = 244011605;
const StringHash HASH_ELEMENT_TEXTURE2D = 264041124;
const StringHash HASH_ELEMENT_TEXTURE2D_ENABLE = 244011861;
const StringHash HASH_ELEMENT_TEXTURE3D = 264041140;
const StringHash HASH_ELEMENT_TEXTURE3D_ENABLE = 244012117;
const StringHash HASH_ELEMENT_TEXTURECUBE = 217269973;
const StringHash HASH_ELEMENT_TEXTURECUBE_ENABLE = 177215061;
const StringHash HASH_ELEMENT_TEXTUREDEPTH = 255090248;
const StringHash HASH_ELEMENT_TEXTUREDEPTH_ENABLE = 117546261;
const StringHash HASH_ELEMENT_TEXTURERECT = 217097780;
const StringHash HASH_ELEMENT_TEXTURERECT_ENABLE = 181279813;
const StringHash HASH_ELEMENT_TEXTURE_ENV_COLOR = 145348738;
const StringHash HASH_ELEMENT_TEXTURE_ENV_MODE = 244006133;
const StringHash HASH_ELEMENT_TEXTURE_PIPELINE = 195376869;
const StringHash HASH_ELEMENT_TEXTURE_PIPELINE_ENABLE = 267851525;
const StringHash HASH_ELEMENT_TEXTURE_PIPELINE____GLES_TEXTURE_PIPELINE = 136209125;
const StringHash HASH_ELEMENT_TEXTURE_UNIT = 253386948;
const StringHash HASH_ELEMENT_TIME_STEP = 62312896;
const StringHash HASH_ELEMENT_TITLE = 8063781;
const StringHash HASH_ELEMENT_TRANSLATE = 140137253;
const StringHash HASH_ELEMENT_TRANSPARENCY = 19939593;
const StringHash HASH_ELEMENT_TRANSPARENT = 169018372;
const StringHash HASH_ELEMENT_TRIANGLES = 260356419;
const StringHash HASH_ELEMENT_TRIFANS = 193972259;
const StringHash HASH_ELEMENT_TRISTRIPS = 11275235;
const StringHash HASH_ELEMENT_UNIT = HASH_ATTRIBUTE_unit;
const StringHash HASH_ELEMENT_UP_AXIS = 207982451;
const StringHash HASH_ELEMENT_USERTYPE = 164218789;
const StringHash HASH_ELEMENT_V = 118;
const StringHash HASH_ELEMENT_VALUE = HASH_ATTRIBUTE_value;
const StringHash HASH_ELEMENT_VALUE____GLES_TEXTURE_PIPELINE = 59331413;
const StringHash HASH_ELEMENT_VALUE____GL_SAMPLER1D = 36300260;
const StringHash HASH_ELEMENT_VALUE____GL_SAMPLER2D = 36300244;
const StringHash HASH_ELEMENT_VALUE____GL_SAMPLER3D = 36300228;
const StringHash HASH_ELEMENT_VALUE____GL_SAMPLERCUBE = 166083397;
const StringHash HASH_ELEMENT_VALUE____GL_SAMPLERDEPTH = 241403672;
const StringHash HASH_ELEMENT_VALUE____GL_SAMPLERRECT = 166189988;
const StringHash HASH_ELEMENT_VCOUNT = 130706516;
const StringHash HASH_ELEMENT_VELOCITY = 204842873;
const StringHash HASH_ELEMENT_VERTEX_WEIGHTS = 19207187;
const StringHash HASH_ELEMENT_VERTICES = 211484163;
const StringHash HASH_ELEMENT_VIEWPORT_RATIO = 128383919;
const StringHash HASH_ELEMENT_VISUAL_SCENE = 129577413;
const StringHash HASH_ELEMENT_WRAP_P = 132679264;
const StringHash HASH_ELEMENT_WRAP_S = 132679267;
const StringHash HASH_ELEMENT_WRAP_S____FX_SAMPLER_WRAP_COMMON = 22902798;
const StringHash HASH_ELEMENT_WRAP_S____GLES_SAMPLER_WRAP = 132876992;
const StringHash HASH_ELEMENT_WRAP_T = 132679268;
const StringHash HASH_ELEMENT_WRAP_T____FX_SAMPLER_WRAP_COMMON = 22899470;
const StringHash HASH_ELEMENT_WRAP_T____GLES_SAMPLER_WRAP = 132807360;
const StringHash HASH_ELEMENT_XFOV = 519526;
const StringHash HASH_ELEMENT_XMAG = 521079;
const StringHash HASH_ELEMENT_YFOV = 523622;
const StringHash HASH_ELEMENT_YMAG = 525175;
const StringHash HASH_ELEMENT_ZFAIL = 8439804;
const StringHash HASH_ELEMENT_ZFAR = 527490;
const StringHash HASH_ELEMENT_ZNEAR = 8473474;
const StringHash HASH_ELEMENT_ZPASS = 8480931;
const StringHash STATE_MACHINE_ROOT = 88705076;
const StringHash STATE_MACHINE_END = 123005284;


struct COLLADA__AttributeData
{
    static const COLLADA__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_BASE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__VersionType version;
    COLLADABU::URI base;
};

struct unit__AttributeData
{
    static const unit__AttributeData DEFAULT;

    float meter;
    const ParserChar* name;
};

struct library_animations__AttributeData
{
    static const library_animations__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct animation__AttributeData
{
    static const animation__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct source__AttributeData
{
    static const source__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct IDREF_array__AttributeData
{
    static const IDREF_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct Name_array__AttributeData
{
    static const Name_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct bool_array__AttributeData
{
    static const bool_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
};

struct float_array__AttributeData
{
    static const float_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
    sint16 digits;
    sint16 magnitude;
};

struct int_array__AttributeData
{
    static const int_array__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    uint64 count;
    sint64 minInclusive;
    sint64 maxInclusive;
};

struct accessor__AttributeData
{
    static const accessor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_SOURCE_PRESENT = 0x2;

    uint32 present_attributes;

    uint64 count;
    uint64 offset;
    COLLADABU::URI source;
    uint64 stride;
};

struct param__AttributeData
{
    static const param__AttributeData DEFAULT;

    const ParserChar* name;
    const ParserChar* sid;
    const ParserChar* semantic;
    const ParserChar* type;
};

struct technique__AttributeData
{
    static const technique__AttributeData DEFAULT;

    const ParserChar* profile;
};

struct sampler__AttributeData
{
    static const sampler__AttributeData DEFAULT;

    const ParserChar* id;
};

struct input____InputLocal__AttributeData
{
    static const input____InputLocal__AttributeData DEFAULT;

    const ParserChar* semantic;
    const ParserChar* source;
};

struct channel__AttributeData
{
    static const channel__AttributeData DEFAULT;

    const ParserChar* source;
    const ParserChar* target;
};

struct extra__AttributeData
{
    static const extra__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* type;
};

struct library_animation_clips__AttributeData
{
    static const library_animation_clips__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct animation_clip__AttributeData
{
    static const animation_clip__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_END_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    float start;
    float end;
};

struct instance_animation__AttributeData
{
    static const instance_animation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct library_cameras__AttributeData
{
    static const library_cameras__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct camera__AttributeData
{
    static const camera__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct xmag__AttributeData
{
    static const xmag__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct ymag__AttributeData
{
    static const ymag__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct aspect_ratio__AttributeData
{
    static const aspect_ratio__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct znear__AttributeData
{
    static const znear__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct zfar__AttributeData
{
    static const zfar__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct xfov__AttributeData
{
    static const xfov__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct yfov__AttributeData
{
    static const yfov__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_controllers__AttributeData
{
    static const library_controllers__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct controller__AttributeData
{
    static const controller__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct skin__AttributeData
{
    static const skin__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SOURCE_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI source;
};

struct vertex_weights__AttributeData
{
    static const vertex_weights__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 count;
};

struct input____InputLocalOffset__AttributeData
{
    static const input____InputLocalOffset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_OFFSET_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_SET_PRESENT = 0x2;

    uint32 present_attributes;

    uint64 offset;
    const ParserChar* semantic;
    const ParserChar* source;
    uint64 set;
};

struct morph__AttributeData
{
    static const morph__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SOURCE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__MorphMethodType method;
    COLLADABU::URI source;
};

struct library_geometries__AttributeData
{
    static const library_geometries__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct geometry__AttributeData
{
    static const geometry__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct convex_mesh__AttributeData
{
    static const convex_mesh__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_CONVEX_HULL_OF_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI convex_hull_of;
};

struct vertices__AttributeData
{
    static const vertices__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct lines__AttributeData
{
    static const lines__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct linestrips__AttributeData
{
    static const linestrips__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct polygons__AttributeData
{
    static const polygons__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct polylist__AttributeData
{
    static const polylist__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct triangles__AttributeData
{
    static const triangles__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct trifans__AttributeData
{
    static const trifans__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct tristrips__AttributeData
{
    static const tristrips__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_COUNT_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* name;
    uint64 count;
    const ParserChar* material;
};

struct spline__AttributeData
{
    static const spline__AttributeData DEFAULT;

    bool closed;
};

struct library_effects__AttributeData
{
    static const library_effects__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct effect__AttributeData
{
    static const effect__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct annotate__AttributeData
{
    static const annotate__AttributeData DEFAULT;

    const ParserChar* name;
};

struct image__AttributeData
{
    static const image__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_HEIGHT_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_WIDTH_PRESENT = 0x2;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* format;
    uint64 height;
    uint64 width;
    uint64 depth;
};

struct newparam____fx_newparam_common__AttributeData
{
    static const newparam____fx_newparam_common__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct surface____fx_surface_common__AttributeData
{
    static const surface____fx_surface_common__AttributeData DEFAULT;

    ENUM__fx_surface_type_enum type;
};

struct init_as_null__AttributeData
{
    static const init_as_null__AttributeData DEFAULT;


    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct init_as_target__AttributeData
{
    static const init_as_target__AttributeData DEFAULT;


    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct fx_surface_init_cube_common____all__AttributeData
{
    static const fx_surface_init_cube_common____all__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct fx_surface_init_cube_common____primary__AttributeData
{
    static const fx_surface_init_cube_common____primary__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct fx_surface_init_cube_common____face__AttributeData
{
    static const fx_surface_init_cube_common____face__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct fx_surface_init_volume_common____all__AttributeData
{
    static const fx_surface_init_volume_common____all__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct fx_surface_init_volume_common____primary__AttributeData
{
    static const fx_surface_init_volume_common____primary__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct fx_surface_init_planar_common____all__AttributeData
{
    static const fx_surface_init_planar_common____all__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct init_from____fx_surface_init_from_common__AttributeData
{
    static const init_from____fx_surface_init_from_common__AttributeData DEFAULT;

    uint32 mip;
    uint32 slice;
    ENUM__fx_surface_face_enum face;
};

struct fx_profile_abstract__AttributeData
{
    static const fx_profile_abstract__AttributeData DEFAULT;


    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct library_force_fields__AttributeData
{
    static const library_force_fields__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct force_field__AttributeData
{
    static const force_field__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct library_images__AttributeData
{
    static const library_images__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct library_lights__AttributeData
{
    static const library_lights__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct light__AttributeData
{
    static const light__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct color____TargetableFloat3__AttributeData
{
    static const color____TargetableFloat3__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct constant_attenuation__AttributeData
{
    static const constant_attenuation__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct linear_attenuation__AttributeData
{
    static const linear_attenuation__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct quadratic_attenuation__AttributeData
{
    static const quadratic_attenuation__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct falloff_angle__AttributeData
{
    static const falloff_angle__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct falloff_exponent__AttributeData
{
    static const falloff_exponent__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_materials__AttributeData
{
    static const library_materials__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct material__AttributeData
{
    static const material__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct instance_effect__AttributeData
{
    static const instance_effect__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct technique_hint__AttributeData
{
    static const technique_hint__AttributeData DEFAULT;

    const ParserChar* platform;
    const ParserChar* profile;
    const ParserChar* ref;
};

struct instance_effect__setparam__AttributeData
{
    static const instance_effect__setparam__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct library_nodes__AttributeData
{
    static const library_nodes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct node__AttributeData
{
    static const node__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LAYER_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* id;
    const ParserChar* name;
    const ParserChar* sid;
    ENUM__NodeType type;
    GeneratedSaxParser::XSList<ParserString> layer;
};

struct lookat__AttributeData
{
    static const lookat__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct matrix__AttributeData
{
    static const matrix__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct rotate__AttributeData
{
    static const rotate__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct scale__AttributeData
{
    static const scale__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct skew__AttributeData
{
    static const skew__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct translate__AttributeData
{
    static const translate__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_camera__AttributeData
{
    static const instance_camera__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_controller__AttributeData
{
    static const instance_controller__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_material__AttributeData
{
    static const instance_material__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_TARGET_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* symbol;
    COLLADABU::URI target;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_material__bind__AttributeData
{
    static const instance_material__bind__AttributeData DEFAULT;

    const ParserChar* semantic;
    const ParserChar* target;
};

struct bind_vertex_input__AttributeData
{
    static const bind_vertex_input__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INPUT_SET_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* semantic;
    const ParserChar* input_semantic;
    uint64 input_set;
};

struct instance_geometry__AttributeData
{
    static const instance_geometry__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_light__AttributeData
{
    static const instance_light__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_node__AttributeData
{
    static const instance_node__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct library_physics_materials__AttributeData
{
    static const library_physics_materials__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct physics_material__AttributeData
{
    static const physics_material__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct dynamic_friction__AttributeData
{
    static const dynamic_friction__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct restitution__AttributeData
{
    static const restitution__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct static_friction__AttributeData
{
    static const static_friction__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_physics_models__AttributeData
{
    static const library_physics_models__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct physics_model__AttributeData
{
    static const physics_model__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct rigid_body__AttributeData
{
    static const rigid_body__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct rigid_body__technique_common__dynamic__AttributeData
{
    static const rigid_body__technique_common__dynamic__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct mass__AttributeData
{
    static const mass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct inertia__AttributeData
{
    static const inertia__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_physics_material__AttributeData
{
    static const instance_physics_material__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct rigid_body__technique_common__shape__hollow__AttributeData
{
    static const rigid_body__technique_common__shape__hollow__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct density__AttributeData
{
    static const density__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct rigid_constraint__AttributeData
{
    static const rigid_constraint__AttributeData DEFAULT;

    const ParserChar* sid;
    const ParserChar* name;
};

struct ref_attachment__AttributeData
{
    static const ref_attachment__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_RIGID_BODY_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI rigid_body;
};

struct attachment__AttributeData
{
    static const attachment__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_RIGID_BODY_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI rigid_body;
};

struct enabled__AttributeData
{
    static const enabled__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct interpenetrate__AttributeData
{
    static const interpenetrate__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct min__AttributeData
{
    static const min__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct max__AttributeData
{
    static const max__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct stiffness__AttributeData
{
    static const stiffness__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct damping__AttributeData
{
    static const damping__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct target_value__AttributeData
{
    static const target_value__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_physics_model__AttributeData
{
    static const instance_physics_model__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_PARENT_PRESENT = 0x2;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
    COLLADABU::URI parent;
};

struct instance_force_field__AttributeData
{
    static const instance_force_field__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_rigid_body__AttributeData
{
    static const instance_rigid_body__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_TARGET_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* body;
    const ParserChar* sid;
    const ParserChar* name;
    COLLADABU::URI target;
};

struct instance_rigid_body__technique_common__dynamic__AttributeData
{
    static const instance_rigid_body__technique_common__dynamic__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_rigid_body__technique_common__shape__hollow__AttributeData
{
    static const instance_rigid_body__technique_common__shape__hollow__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct instance_rigid_constraint__AttributeData
{
    static const instance_rigid_constraint__AttributeData DEFAULT;

    const ParserChar* constraint;
    const ParserChar* sid;
    const ParserChar* name;
};

struct library_physics_scenes__AttributeData
{
    static const library_physics_scenes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct physics_scene__AttributeData
{
    static const physics_scene__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct gravity__AttributeData
{
    static const gravity__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct time_step__AttributeData
{
    static const time_step__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct library_visual_scenes__AttributeData
{
    static const library_visual_scenes__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct visual_scene__AttributeData
{
    static const visual_scene__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* name;
};

struct evaluate_scene__AttributeData
{
    static const evaluate_scene__AttributeData DEFAULT;

    const ParserChar* name;
};

struct render__AttributeData
{
    static const render__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_CAMERA_NODE_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI camera_node;
};

struct instance_physics_scene__AttributeData
{
    static const instance_physics_scene__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct instance_visual_scene__AttributeData
{
    static const instance_visual_scene__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    COLLADABU::URI url;
    const ParserChar* sid;
    const ParserChar* name;
};

struct profile_GLES__AttributeData
{
    static const profile_GLES__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* platform;
};

struct newparam____gles_newparam__AttributeData
{
    static const newparam____gles_newparam__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct texture_pipeline____gles_texture_pipeline__AttributeData
{
    static const texture_pipeline____gles_texture_pipeline__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct constant____gles_texture_constant_type__AttributeData
{
    static const constant____gles_texture_constant_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct RGB__AttributeData
{
    static const RGB__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SCALE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__gles_texcombiner_operatorRGB_enums _operator;
    float scale;
};

struct argument____gles_texcombiner_argumentRGB_type__AttributeData
{
    static const argument____gles_texcombiner_argumentRGB_type__AttributeData DEFAULT;

    ENUM__gles_texcombiner_source_enums source;
    ENUM__gles_texcombiner_operandRGB_enums operand;
    const ParserChar* unit;
};

struct alpha____gles_texcombiner_commandAlpha_type__AttributeData
{
    static const alpha____gles_texcombiner_commandAlpha_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_SCALE_PRESENT = 0x1;

    uint32 present_attributes;

    ENUM__gles_texcombiner_operatorAlpha_enums _operator;
    float scale;
};

struct argument____gles_texcombiner_argumentAlpha_type__AttributeData
{
    static const argument____gles_texcombiner_argumentAlpha_type__AttributeData DEFAULT;

    ENUM__gles_texcombiner_source_enums source;
    ENUM__gles_texcombiner_operandAlpha_enums operand;
    const ParserChar* unit;
};

struct texenv__AttributeData
{
    static const texenv__AttributeData DEFAULT;

    ENUM__gles_texenv_mode_enums _operator;
    const ParserChar* unit;
};

struct sampler_state____gles_sampler_state__AttributeData
{
    static const sampler_state____gles_sampler_state__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct texture_unit__AttributeData
{
    static const texture_unit__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct texcoord__AttributeData
{
    static const texcoord__AttributeData DEFAULT;

    const ParserChar* semantic;
};

struct profile_GLES__technique__AttributeData
{
    static const profile_GLES__technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct profile_GLES__technique__setparam__AttributeData
{
    static const profile_GLES__technique__setparam__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_GLES__technique__pass__AttributeData
{
    static const profile_GLES__technique__pass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_GLES__technique__pass__alpha_func__func__AttributeData
{
    static const profile_GLES__technique__pass__alpha_func__func__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__alpha_func__value__AttributeData
{
    static const profile_GLES__technique__pass__alpha_func__value__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__blend_func__src__AttributeData
{
    static const profile_GLES__technique__pass__blend_func__src__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__blend_func__dest__AttributeData
{
    static const profile_GLES__technique__pass__blend_func__dest__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__clear_color__AttributeData
{
    static const profile_GLES__technique__pass__clear_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__clear_stencil__AttributeData
{
    static const profile_GLES__technique__pass__clear_stencil__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__clear_depth__AttributeData
{
    static const profile_GLES__technique__pass__clear_depth__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__clip_plane__AttributeData
{
    static const profile_GLES__technique__pass__clip_plane__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__color_mask__AttributeData
{
    static const profile_GLES__technique__pass__color_mask__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__cull_face__AttributeData
{
    static const profile_GLES__technique__pass__cull_face__AttributeData DEFAULT;

    ENUM__gl_face_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__depth_func__AttributeData
{
    static const profile_GLES__technique__pass__depth_func__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__depth_mask__AttributeData
{
    static const profile_GLES__technique__pass__depth_mask__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__depth_range__AttributeData
{
    static const profile_GLES__technique__pass__depth_range__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__fog_color__AttributeData
{
    static const profile_GLES__technique__pass__fog_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__fog_density__AttributeData
{
    static const profile_GLES__technique__pass__fog_density__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__fog_mode__AttributeData
{
    static const profile_GLES__technique__pass__fog_mode__AttributeData DEFAULT;

    ENUM__gl_fog_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__fog_start__AttributeData
{
    static const profile_GLES__technique__pass__fog_start__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__fog_end__AttributeData
{
    static const profile_GLES__technique__pass__fog_end__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__front_face__AttributeData
{
    static const profile_GLES__technique__pass__front_face__AttributeData DEFAULT;

    ENUM__gl_front_face_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__texture_pipeline__AttributeData
{
    static const profile_GLES__technique__pass__texture_pipeline__AttributeData DEFAULT;

    const ParserChar* param;
};

typedef texture_pipeline____gles_texture_pipeline__AttributeData value____gles_texture_pipeline__AttributeData;

struct profile_GLES__technique__pass__logic_op__AttributeData
{
    static const profile_GLES__technique__pass__logic_op__AttributeData DEFAULT;

    ENUM__gl_logic_op_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__light_ambient__AttributeData
{
    static const profile_GLES__technique__pass__light_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_diffuse__AttributeData
{
    static const profile_GLES__technique__pass__light_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_specular__AttributeData
{
    static const profile_GLES__technique__pass__light_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_position__AttributeData
{
    static const profile_GLES__technique__pass__light_position__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_constant_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__light_constant_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct light_linear_attenutation__AttributeData
{
    static const light_linear_attenutation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_quadratic_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__light_quadratic_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_spot_cutoff__AttributeData
{
    static const profile_GLES__technique__pass__light_spot_cutoff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_spot_direction__AttributeData
{
    static const profile_GLES__technique__pass__light_spot_direction__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_spot_exponent__AttributeData
{
    static const profile_GLES__technique__pass__light_spot_exponent__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__light_model_ambient__AttributeData
{
    static const profile_GLES__technique__pass__light_model_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__line_width__AttributeData
{
    static const profile_GLES__technique__pass__line_width__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__material_ambient__AttributeData
{
    static const profile_GLES__technique__pass__material_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__material_diffuse__AttributeData
{
    static const profile_GLES__technique__pass__material_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__material_emission__AttributeData
{
    static const profile_GLES__technique__pass__material_emission__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__material_shininess__AttributeData
{
    static const profile_GLES__technique__pass__material_shininess__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__material_specular__AttributeData
{
    static const profile_GLES__technique__pass__material_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__model_view_matrix__AttributeData
{
    static const profile_GLES__technique__pass__model_view_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__point_distance_attenuation__AttributeData
{
    static const profile_GLES__technique__pass__point_distance_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__point_fade_threshold_size__AttributeData
{
    static const profile_GLES__technique__pass__point_fade_threshold_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__point_size__AttributeData
{
    static const profile_GLES__technique__pass__point_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__point_size_min__AttributeData
{
    static const profile_GLES__technique__pass__point_size_min__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__point_size_max__AttributeData
{
    static const profile_GLES__technique__pass__point_size_max__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__polygon_offset__AttributeData
{
    static const profile_GLES__technique__pass__polygon_offset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__projection_matrix__AttributeData
{
    static const profile_GLES__technique__pass__projection_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__scissor__AttributeData
{
    static const profile_GLES__technique__pass__scissor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__shade_model__AttributeData
{
    static const profile_GLES__technique__pass__shade_model__AttributeData DEFAULT;

    ENUM__gl_shade_model_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_func__func__AttributeData
{
    static const profile_GLES__technique__pass__stencil_func__func__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_func__ref__AttributeData
{
    static const profile_GLES__technique__pass__stencil_func__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_func__mask__AttributeData
{
    static const profile_GLES__technique__pass__stencil_func__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_mask__AttributeData
{
    static const profile_GLES__technique__pass__stencil_mask__AttributeData DEFAULT;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_op__fail__AttributeData
{
    static const profile_GLES__technique__pass__stencil_op__fail__AttributeData DEFAULT;

    ENUM__gles_stencil_op_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_op__zfail__AttributeData
{
    static const profile_GLES__technique__pass__stencil_op__zfail__AttributeData DEFAULT;

    ENUM__gles_stencil_op_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_op__zpass__AttributeData
{
    static const profile_GLES__technique__pass__stencil_op__zpass__AttributeData DEFAULT;

    ENUM__gles_stencil_op_type value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__alpha_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__alpha_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__blend_enable__AttributeData
{
    static const profile_GLES__technique__pass__blend_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__clip_plane_enable__AttributeData
{
    static const profile_GLES__technique__pass__clip_plane_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__color_logic_op_enable__AttributeData
{
    static const profile_GLES__technique__pass__color_logic_op_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__color_material_enable__AttributeData
{
    static const profile_GLES__technique__pass__color_material_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__cull_face_enable__AttributeData
{
    static const profile_GLES__technique__pass__cull_face_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__depth_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__depth_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__dither_enable__AttributeData
{
    static const profile_GLES__technique__pass__dither_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__fog_enable__AttributeData
{
    static const profile_GLES__technique__pass__fog_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct texture_pipeline_enable__AttributeData
{
    static const texture_pipeline_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__light_enable__AttributeData
{
    static const profile_GLES__technique__pass__light_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLES__technique__pass__lighting_enable__AttributeData
{
    static const profile_GLES__technique__pass__lighting_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__light_model_two_side_enable__AttributeData
{
    static const profile_GLES__technique__pass__light_model_two_side_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__line_smooth_enable__AttributeData
{
    static const profile_GLES__technique__pass__line_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__multisample_enable__AttributeData
{
    static const profile_GLES__technique__pass__multisample_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__normalize_enable__AttributeData
{
    static const profile_GLES__technique__pass__normalize_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__point_smooth_enable__AttributeData
{
    static const profile_GLES__technique__pass__point_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__polygon_offset_fill_enable__AttributeData
{
    static const profile_GLES__technique__pass__polygon_offset_fill_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__rescale_normal_enable__AttributeData
{
    static const profile_GLES__technique__pass__rescale_normal_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__sample_alpha_to_coverage_enable__AttributeData
{
    static const profile_GLES__technique__pass__sample_alpha_to_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__sample_alpha_to_one_enable__AttributeData
{
    static const profile_GLES__technique__pass__sample_alpha_to_one_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__sample_coverage_enable__AttributeData
{
    static const profile_GLES__technique__pass__sample_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__scissor_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__scissor_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLES__technique__pass__stencil_test_enable__AttributeData
{
    static const profile_GLES__technique__pass__stencil_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__AttributeData
{
    static const profile_GLSL__AttributeData DEFAULT;

    const ParserChar* id;
};

struct code__AttributeData
{
    static const code__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct include__AttributeData
{
    static const include__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_URL_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* sid;
    COLLADABU::URI url;
};

struct newparam____glsl_newparam__AttributeData
{
    static const newparam____glsl_newparam__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct surface____glsl_surface_type__AttributeData
{
    static const surface____glsl_surface_type__AttributeData DEFAULT;

    ENUM__fx_surface_type_enum type;
};

struct profile_GLSL__newparam__surface__generator__name__AttributeData
{
    static const profile_GLSL__newparam__surface__generator__name__AttributeData DEFAULT;

    const ParserChar* source;
};

struct setparam____glsl_setparam_simple__AttributeData
{
    static const setparam____glsl_setparam_simple__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct array____glsl_newarray_type__AttributeData
{
    static const array____glsl_newarray_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct profile_GLSL__technique__AttributeData
{
    static const profile_GLSL__technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct setparam____glsl_setparam__AttributeData
{
    static const setparam____glsl_setparam__AttributeData DEFAULT;

    const ParserChar* ref;
    const ParserChar* program;
};

struct array____glsl_setarray_type__AttributeData
{
    static const array____glsl_setarray_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct profile_GLSL__technique__pass__AttributeData
{
    static const profile_GLSL__technique__pass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct color_target____fx_colortarget_common__AttributeData
{
    static const color_target____fx_colortarget_common__AttributeData DEFAULT;

    uint64 index;
    ENUM__fx_surface_face_enum face;
    uint64 mip;
    uint64 slice;
};

struct depth_target____fx_depthtarget_common__AttributeData
{
    static const depth_target____fx_depthtarget_common__AttributeData DEFAULT;

    uint64 index;
    ENUM__fx_surface_face_enum face;
    uint64 mip;
    uint64 slice;
};

struct stencil_target____fx_stenciltarget_common__AttributeData
{
    static const stencil_target____fx_stenciltarget_common__AttributeData DEFAULT;

    uint64 index;
    ENUM__fx_surface_face_enum face;
    uint64 mip;
    uint64 slice;
};

struct color_clear____fx_clearcolor_common__AttributeData
{
    static const color_clear____fx_clearcolor_common__AttributeData DEFAULT;

    uint64 index;
};

struct depth_clear____fx_cleardepth_common__AttributeData
{
    static const depth_clear____fx_cleardepth_common__AttributeData DEFAULT;

    uint64 index;
};

struct stencil_clear____fx_clearstencil_common__AttributeData
{
    static const stencil_clear____fx_clearstencil_common__AttributeData DEFAULT;

    uint64 index;
};

struct profile_GLSL__technique__pass__alpha_func__func__AttributeData
{
    static const profile_GLSL__technique__pass__alpha_func__func__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__alpha_func__value__AttributeData
{
    static const profile_GLSL__technique__pass__alpha_func__value__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__blend_func__src__AttributeData
{
    static const profile_GLSL__technique__pass__blend_func__src__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__blend_func__dest__AttributeData
{
    static const profile_GLSL__technique__pass__blend_func__dest__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct src_rgb__AttributeData
{
    static const src_rgb__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct dest_rgb__AttributeData
{
    static const dest_rgb__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct src_alpha__AttributeData
{
    static const src_alpha__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct dest_alpha__AttributeData
{
    static const dest_alpha__AttributeData DEFAULT;

    ENUM__gl_blend_type value;
    const ParserChar* param;
};

struct blend_equation__AttributeData
{
    static const blend_equation__AttributeData DEFAULT;

    ENUM__gl_blend_equation_type value;
    const ParserChar* param;
};

struct rgb__AttributeData
{
    static const rgb__AttributeData DEFAULT;

    ENUM__gl_blend_equation_type value;
    const ParserChar* param;
};

struct blend_equation_separate__alpha__AttributeData
{
    static const blend_equation_separate__alpha__AttributeData DEFAULT;

    ENUM__gl_blend_equation_type value;
    const ParserChar* param;
};

struct color_material__face__AttributeData
{
    static const color_material__face__AttributeData DEFAULT;

    ENUM__gl_face_type value;
    const ParserChar* param;
};

struct color_material__mode__AttributeData
{
    static const color_material__mode__AttributeData DEFAULT;

    ENUM__gl_material_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__cull_face__AttributeData
{
    static const profile_GLSL__technique__pass__cull_face__AttributeData DEFAULT;

    ENUM__gl_face_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__depth_func__AttributeData
{
    static const profile_GLSL__technique__pass__depth_func__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__fog_mode__AttributeData
{
    static const profile_GLSL__technique__pass__fog_mode__AttributeData DEFAULT;

    ENUM__gl_fog_type value;
    const ParserChar* param;
};

struct fog_coord_src__AttributeData
{
    static const fog_coord_src__AttributeData DEFAULT;

    ENUM__gl_fog_coord_src_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__front_face__AttributeData
{
    static const profile_GLSL__technique__pass__front_face__AttributeData DEFAULT;

    ENUM__gl_front_face_type value;
    const ParserChar* param;
};

struct light_model_color_control__AttributeData
{
    static const light_model_color_control__AttributeData DEFAULT;

    ENUM__gl_light_model_color_control_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__logic_op__AttributeData
{
    static const profile_GLSL__technique__pass__logic_op__AttributeData DEFAULT;

    ENUM__gl_logic_op_type value;
    const ParserChar* param;
};

struct polygon_mode__face__AttributeData
{
    static const polygon_mode__face__AttributeData DEFAULT;

    ENUM__gl_face_type value;
    const ParserChar* param;
};

struct polygon_mode__mode__AttributeData
{
    static const polygon_mode__mode__AttributeData DEFAULT;

    ENUM__gl_polygon_mode_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__shade_model__AttributeData
{
    static const profile_GLSL__technique__pass__shade_model__AttributeData DEFAULT;

    ENUM__gl_shade_model_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_func__func__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_func__func__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_func__ref__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_func__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_func__mask__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_func__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_op__fail__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_op__fail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_op__zfail__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_op__zfail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_type value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_op__zpass__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_op__zpass__AttributeData DEFAULT;

    ENUM__gl_stencil_op_type value;
    const ParserChar* param;
};

struct front__AttributeData
{
    static const front__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct back__AttributeData
{
    static const back__AttributeData DEFAULT;

    ENUM__gl_func_type value;
    const ParserChar* param;
};

struct stencil_func_separate__ref__AttributeData
{
    static const stencil_func_separate__ref__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct stencil_func_separate__mask__AttributeData
{
    static const stencil_func_separate__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct stencil_op_separate__face__AttributeData
{
    static const stencil_op_separate__face__AttributeData DEFAULT;

    ENUM__gl_face_type value;
    const ParserChar* param;
};

struct stencil_op_separate__fail__AttributeData
{
    static const stencil_op_separate__fail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_type value;
    const ParserChar* param;
};

struct stencil_op_separate__zfail__AttributeData
{
    static const stencil_op_separate__zfail__AttributeData DEFAULT;

    ENUM__gl_stencil_op_type value;
    const ParserChar* param;
};

struct stencil_op_separate__zpass__AttributeData
{
    static const stencil_op_separate__zpass__AttributeData DEFAULT;

    ENUM__gl_stencil_op_type value;
    const ParserChar* param;
};

struct stencil_mask_separate__face__AttributeData
{
    static const stencil_mask_separate__face__AttributeData DEFAULT;

    ENUM__gl_face_type value;
    const ParserChar* param;
};

struct stencil_mask_separate__mask__AttributeData
{
    static const stencil_mask_separate__mask__AttributeData DEFAULT;

    uint8 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__light_enable__AttributeData
{
    static const profile_GLSL__technique__pass__light_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_ambient__AttributeData
{
    static const profile_GLSL__technique__pass__light_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_diffuse__AttributeData
{
    static const profile_GLSL__technique__pass__light_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_specular__AttributeData
{
    static const profile_GLSL__technique__pass__light_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_position__AttributeData
{
    static const profile_GLSL__technique__pass__light_position__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_constant_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__light_constant_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct light_linear_attenuation__AttributeData
{
    static const light_linear_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_quadratic_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__light_quadratic_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_spot_cutoff__AttributeData
{
    static const profile_GLSL__technique__pass__light_spot_cutoff__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_spot_direction__AttributeData
{
    static const profile_GLSL__technique__pass__light_spot_direction__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__light_spot_exponent__AttributeData
{
    static const profile_GLSL__technique__pass__light_spot_exponent__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    float value;
    const ParserChar* param;
    uint64 index;
};

struct texture1D__AttributeData
{
    static const texture1D__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct texture2D__AttributeData
{
    static const texture2D__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct texture3D__AttributeData
{
    static const texture3D__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct textureCUBE__AttributeData
{
    static const textureCUBE__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct textureRECT__AttributeData
{
    static const textureRECT__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct textureDEPTH__AttributeData
{
    static const textureDEPTH__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 index;
};

struct texture1D_enable__AttributeData
{
    static const texture1D_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct texture2D_enable__AttributeData
{
    static const texture2D_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct texture3D_enable__AttributeData
{
    static const texture3D_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct textureCUBE_enable__AttributeData
{
    static const textureCUBE_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct textureRECT_enable__AttributeData
{
    static const textureRECT_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct textureDEPTH_enable__AttributeData
{
    static const textureDEPTH_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct texture_env_color__AttributeData
{
    static const texture_env_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct texture_env_mode__AttributeData
{
    static const texture_env_mode__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    const ParserChar* value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__clip_plane__AttributeData
{
    static const profile_GLSL__technique__pass__clip_plane__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;
    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x2;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
    uint64 index;
};

struct profile_GLSL__technique__pass__clip_plane_enable__AttributeData
{
    static const profile_GLSL__technique__pass__clip_plane_enable__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_INDEX_PRESENT = 0x1;

    uint32 present_attributes;

    bool value;
    const ParserChar* param;
    uint64 index;
};

struct blend_color__AttributeData
{
    static const blend_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__clear_color__AttributeData
{
    static const profile_GLSL__technique__pass__clear_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__clear_stencil__AttributeData
{
    static const profile_GLSL__technique__pass__clear_stencil__AttributeData DEFAULT;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__clear_depth__AttributeData
{
    static const profile_GLSL__technique__pass__clear_depth__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__color_mask__AttributeData
{
    static const profile_GLSL__technique__pass__color_mask__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<bool> value;
    const ParserChar* param;
};

struct depth_bounds__AttributeData
{
    static const depth_bounds__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__depth_mask__AttributeData
{
    static const profile_GLSL__technique__pass__depth_mask__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__depth_range__AttributeData
{
    static const profile_GLSL__technique__pass__depth_range__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__fog_density__AttributeData
{
    static const profile_GLSL__technique__pass__fog_density__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__fog_start__AttributeData
{
    static const profile_GLSL__technique__pass__fog_start__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__fog_end__AttributeData
{
    static const profile_GLSL__technique__pass__fog_end__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__fog_color__AttributeData
{
    static const profile_GLSL__technique__pass__fog_color__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__light_model_ambient__AttributeData
{
    static const profile_GLSL__technique__pass__light_model_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__lighting_enable__AttributeData
{
    static const profile_GLSL__technique__pass__lighting_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct line_stipple__AttributeData
{
    static const line_stipple__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__line_width__AttributeData
{
    static const profile_GLSL__technique__pass__line_width__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__material_ambient__AttributeData
{
    static const profile_GLSL__technique__pass__material_ambient__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__material_diffuse__AttributeData
{
    static const profile_GLSL__technique__pass__material_diffuse__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__material_emission__AttributeData
{
    static const profile_GLSL__technique__pass__material_emission__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__material_shininess__AttributeData
{
    static const profile_GLSL__technique__pass__material_shininess__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__material_specular__AttributeData
{
    static const profile_GLSL__technique__pass__material_specular__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__model_view_matrix__AttributeData
{
    static const profile_GLSL__technique__pass__model_view_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__point_distance_attenuation__AttributeData
{
    static const profile_GLSL__technique__pass__point_distance_attenuation__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__point_fade_threshold_size__AttributeData
{
    static const profile_GLSL__technique__pass__point_fade_threshold_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__point_size__AttributeData
{
    static const profile_GLSL__technique__pass__point_size__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__point_size_min__AttributeData
{
    static const profile_GLSL__technique__pass__point_size_min__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__point_size_max__AttributeData
{
    static const profile_GLSL__technique__pass__point_size_max__AttributeData DEFAULT;

    float value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__polygon_offset__AttributeData
{
    static const profile_GLSL__technique__pass__polygon_offset__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__projection_matrix__AttributeData
{
    static const profile_GLSL__technique__pass__projection_matrix__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<float> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__scissor__AttributeData
{
    static const profile_GLSL__technique__pass__scissor__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_VALUE_PRESENT = 0x1;

    uint32 present_attributes;

    GeneratedSaxParser::XSList<sint64> value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_mask__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_mask__AttributeData DEFAULT;

    sint64 value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__alpha_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__alpha_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct auto_normal_enable__AttributeData
{
    static const auto_normal_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__blend_enable__AttributeData
{
    static const profile_GLSL__technique__pass__blend_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__color_logic_op_enable__AttributeData
{
    static const profile_GLSL__technique__pass__color_logic_op_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__color_material_enable__AttributeData
{
    static const profile_GLSL__technique__pass__color_material_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__cull_face_enable__AttributeData
{
    static const profile_GLSL__technique__pass__cull_face_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct depth_bounds_enable__AttributeData
{
    static const depth_bounds_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct depth_clamp_enable__AttributeData
{
    static const depth_clamp_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__depth_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__depth_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__dither_enable__AttributeData
{
    static const profile_GLSL__technique__pass__dither_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__fog_enable__AttributeData
{
    static const profile_GLSL__technique__pass__fog_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct light_model_local_viewer_enable__AttributeData
{
    static const light_model_local_viewer_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__light_model_two_side_enable__AttributeData
{
    static const profile_GLSL__technique__pass__light_model_two_side_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__line_smooth_enable__AttributeData
{
    static const profile_GLSL__technique__pass__line_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct line_stipple_enable__AttributeData
{
    static const line_stipple_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct logic_op_enable__AttributeData
{
    static const logic_op_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__multisample_enable__AttributeData
{
    static const profile_GLSL__technique__pass__multisample_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__normalize_enable__AttributeData
{
    static const profile_GLSL__technique__pass__normalize_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__point_smooth_enable__AttributeData
{
    static const profile_GLSL__technique__pass__point_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__polygon_offset_fill_enable__AttributeData
{
    static const profile_GLSL__technique__pass__polygon_offset_fill_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_offset_line_enable__AttributeData
{
    static const polygon_offset_line_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_offset_point_enable__AttributeData
{
    static const polygon_offset_point_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_smooth_enable__AttributeData
{
    static const polygon_smooth_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct polygon_stipple_enable__AttributeData
{
    static const polygon_stipple_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__rescale_normal_enable__AttributeData
{
    static const profile_GLSL__technique__pass__rescale_normal_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__sample_alpha_to_coverage_enable__AttributeData
{
    static const profile_GLSL__technique__pass__sample_alpha_to_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__sample_alpha_to_one_enable__AttributeData
{
    static const profile_GLSL__technique__pass__sample_alpha_to_one_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__sample_coverage_enable__AttributeData
{
    static const profile_GLSL__technique__pass__sample_coverage_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__scissor_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__scissor_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct profile_GLSL__technique__pass__stencil_test_enable__AttributeData
{
    static const profile_GLSL__technique__pass__stencil_test_enable__AttributeData DEFAULT;

    bool value;
    const ParserChar* param;
};

struct gl_hook_abstract__AttributeData
{
    static const gl_hook_abstract__AttributeData DEFAULT;


    GeneratedSaxParser::XSList<const ParserChar*> unknownAttributes;
};

struct profile_GLSL__technique__pass__shader__AttributeData
{
    static const profile_GLSL__technique__pass__shader__AttributeData DEFAULT;

    ENUM__glsl_pipeline_stage stage;
};

struct profile_GLSL__technique__pass__shader__name__AttributeData
{
    static const profile_GLSL__technique__pass__shader__name__AttributeData DEFAULT;

    const ParserChar* source;
};

struct profile_GLSL__technique__pass__shader__bind__AttributeData
{
    static const profile_GLSL__technique__pass__shader__bind__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct profile_GLSL__technique__pass__shader__bind__param__AttributeData
{
    static const profile_GLSL__technique__pass__shader__bind__param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_CG__AttributeData
{
    static const profile_CG__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* platform;
};

struct newparam____cg_newparam__AttributeData
{
    static const newparam____cg_newparam__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct surface____cg_surface_type__AttributeData
{
    static const surface____cg_surface_type__AttributeData DEFAULT;

    ENUM__fx_surface_type_enum type;
};

struct profile_CG__newparam__surface__generator__name__AttributeData
{
    static const profile_CG__newparam__surface__generator__name__AttributeData DEFAULT;

    const ParserChar* source;
};

struct setparam____cg_setparam_simple__AttributeData
{
    static const setparam____cg_setparam_simple__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct usertype__AttributeData
{
    static const usertype__AttributeData DEFAULT;

    const ParserChar* name;
    const ParserChar* source;
};

struct array____cg_setarray_type__AttributeData
{
    static const array____cg_setarray_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct connect_param__AttributeData
{
    static const connect_param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct setparam____cg_setparam__AttributeData
{
    static const setparam____cg_setparam__AttributeData DEFAULT;

    const ParserChar* ref;
    const ParserChar* program;
};

struct array____cg_newarray_type__AttributeData
{
    static const array____cg_newarray_type__AttributeData DEFAULT;

    static const uint32 ATTRIBUTE_LENGTH_PRESENT = 0x1;

    uint32 present_attributes;

    uint64 length;
};

struct profile_CG__technique__AttributeData
{
    static const profile_CG__technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct profile_CG__technique__pass__AttributeData
{
    static const profile_CG__technique__pass__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_CG__technique__pass__shader__AttributeData
{
    static const profile_CG__technique__pass__shader__AttributeData DEFAULT;

    ENUM__cg_pipeline_stage stage;
};

struct profile_CG__technique__pass__shader__name__AttributeData
{
    static const profile_CG__technique__pass__shader__name__AttributeData DEFAULT;

    const ParserChar* source;
};

struct profile_CG__technique__pass__shader__bind__AttributeData
{
    static const profile_CG__technique__pass__shader__bind__AttributeData DEFAULT;

    const ParserChar* symbol;
};

struct profile_CG__technique__pass__shader__bind__param__AttributeData
{
    static const profile_CG__technique__pass__shader__bind__param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct profile_COMMON__AttributeData
{
    static const profile_COMMON__AttributeData DEFAULT;

    const ParserChar* id;
};

struct newparam____common_newparam_type__AttributeData
{
    static const newparam____common_newparam_type__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct profile_COMMON__technique__AttributeData
{
    static const profile_COMMON__technique__AttributeData DEFAULT;

    const ParserChar* id;
    const ParserChar* sid;
};

struct common_color_or_texture_type____color__AttributeData
{
    static const common_color_or_texture_type____color__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct common_color_or_texture_type____param__AttributeData
{
    static const common_color_or_texture_type____param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct texture__AttributeData
{
    static const texture__AttributeData DEFAULT;

    const ParserChar* texture;
    const ParserChar* texcoord;
};

struct common_float_or_param_type____float__AttributeData
{
    static const common_float_or_param_type____float__AttributeData DEFAULT;

    const ParserChar* sid;
};

struct common_float_or_param_type____param__AttributeData
{
    static const common_float_or_param_type____param__AttributeData DEFAULT;

    const ParserChar* ref;
};

struct transparent__AttributeData
{
    static const transparent__AttributeData DEFAULT;

    ENUM__fx_opaque_enum opaque;
};



} // namespace
#endif
