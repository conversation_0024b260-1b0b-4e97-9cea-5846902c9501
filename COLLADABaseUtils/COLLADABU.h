/*
    Copyright (c) 2008-2009 NetAllied Systems GmbH

	This file is part of COLLADABaseUtils.
	
    Licensed under the MIT Open Source License, 
    for details please see LICENSE file or the website
    http://www.opensource.org/licenses/mit-license.php
*/

#ifndef __COLLADABU_H__
#define __COLLADABU_H__


// contains all headers of the COLLADA base utils api
// might be used in precompiled headers
#include "COLLADABU.h"
#include "COLLADABUException.h"
#include "COLLADABUHashFunctions.h"
#include "COLLADABUhash_map.h"
#include "COLLADABUIDList.h"
#include "COLLADABUNativeString.h"
#include "COLLADABUPlatform.h"
#include "COLLADABUStringUtils.h"
#include "COLLADABUURI.h"
#include "COLLADABUUtils.h"

#include "Math/COLLADABUMathMatrix3.h"
#include "Math/COLLADABUMathMatrix4.h"
#include "Math/COLLADABUMathQuaternion.h"
#include "Math/COLLADABUMathUtils.h"
#include "Math/COLLADABUMathVector3.h"

#endif //__COLLADABU_H__
