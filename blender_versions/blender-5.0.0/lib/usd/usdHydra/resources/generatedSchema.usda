#usda 1.0
(
    "WARNING: THIS FILE IS GENERATED BY usdGenSchema.  DO NOT EDIT."
)

class "HydraGenerativeProceduralAPI" (
    doc = """
    This API extends and configures the core UsdProcGenerativeProcedural schema
    defined within usdProc for use with hydra generative procedurals as defined
    within hdGp.
    """
)
{
    token primvars:hdGp:proceduralType (
        doc = """The registered name of a HdGpGenerativeProceduralPlugin to
        be executed."""
    )
    token proceduralSystem = "hydraGenerativeProcedural" (
        doc = '''
        This value should correspond to a configured instance of
        HdGpGenerativeProceduralResolvingSceneIndex which will evaluate the
        procedural. The default value of "hydraGenerativeProcedural" matches
        the equivalent default of HdGpGenerativeProceduralResolvingSceneIndex.
        Multiple instances of the scene index can be used to determine where
        within a scene index chain a given procedural will be evaluated.
        '''
    )
}

