# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "apiSchemas": {
                        "appliesTo": "prims", 
                        "type": "tokenlistop"
                    }, 
                    "clipSets": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "stringlistop"
                    }, 
                    "clips": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "dictionary"
                    }, 
                    "fallbackPrimTypes": {
                        "appliesTo": [
                            "layers"
                        ], 
                        "type": "dictionary"
                    }
                }, 
                "Types": {
                    "UsdAPISchemaBase": {
                        "alias": {
                            "UsdSchemaBase": "APISchemaBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdSchemaBase"
                        ], 
                        "schemaIdentifier": "APISchemaBase", 
                        "schemaKind": "abstractBase"
                    }, 
                    "UsdClipsAPI": {
                        "alias": {
                            "UsdSchemaBase": "ClipsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ClipsAPI", 
                        "schemaKind": "nonAppliedAPI"
                    }, 
                    "UsdCollectionAPI": {
                        "alias": {
                            "UsdSchemaBase": "CollectionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "CollectionAPI", 
                        "schemaKind": "multipleApplyAPI"
                    }, 
                    "UsdColorSpaceAPI": {
                        "alias": {
                            "UsdSchemaBase": "ColorSpaceAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ColorSpaceAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdColorSpaceDefinitionAPI": {
                        "alias": {
                            "UsdSchemaBase": "ColorSpaceDefinitionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ColorSpaceDefinitionAPI", 
                        "schemaKind": "multipleApplyAPI"
                    }, 
                    "UsdModelAPI": {
                        "alias": {
                            "UsdSchemaBase": "ModelAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ModelAPI", 
                        "schemaKind": "nonAppliedAPI"
                    }, 
                    "UsdSchemaBase": {
                        "alias": {
                            "UsdSchemaBase": "SchemaBase"
                        }, 
                        "schemaKind": "abstractBase"
                    }, 
                    "UsdTyped": {
                        "alias": {
                            "UsdSchemaBase": "Typed"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdSchemaBase"
                        ], 
                        "schemaIdentifier": "Typed", 
                        "schemaKind": "abstractBase"
                    }, 
                    "UsdUsdFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USD File Format", 
                        "extensions": [
                            "usd"
                        ], 
                        "formatId": "usd", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "UsdUsdaFileFormat": {
                        "bases": [
                            "SdfTextFileFormat"
                        ], 
                        "displayName": "USD Text File Format", 
                        "extensions": [
                            "usda"
                        ], 
                        "formatId": "usda", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "UsdUsdcFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USD Crate File Format", 
                        "extensions": [
                            "usdc"
                        ], 
                        "formatId": "usdc", 
                        "primary": true, 
                        "target": "usd"
                    }, 
                    "UsdUsdzFileFormat": {
                        "bases": [
                            "SdfFileFormat"
                        ], 
                        "displayName": "USDZ File Format", 
                        "extensions": [
                            "usdz"
                        ], 
                        "formatId": "usdz", 
                        "primary": true, 
                        "supportsEditing": false, 
                        "supportsWriting": false, 
                        "target": "usd"
                    }, 
                    "Usd_UsdzResolver": {
                        "bases": [
                            "ArPackageResolver"
                        ], 
                        "extensions": [
                            "usdz"
                        ]
                    }
                }
            }, 
            "LibraryPath": "", 
            "Name": "usd", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
