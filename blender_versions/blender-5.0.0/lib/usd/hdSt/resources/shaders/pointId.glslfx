-- glslfx version 0.1

//
// Copyright 2018 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/pointId.glslfx

#import $TOOLS/hdx/shaders/selection.glslfx

--- --------------------------------------------------------------------------
-- glsl PointId.Vertex.None

int GetPointId()
{
    return -1;
}

float GetPointRasterSize(int pointId)
{
    return GetPointSize();
}

void ProcessPointId(int pointId)
{
    // do nothing
}

--- --------------------------------------------------------------------------
-- layout PointId.Vertex.PointParam

# Plumb the pointId, for use in the FS.
# XXX: This works only because the TES and GS stages are disabled when
# rendering as points. If they are enabled, we need to add the plumbing.
[
    ["out", "int", "vsPointId", "flat"]
]

--- --------------------------------------------------------------------------
-- glsl PointId.Vertex.PointParam

// Fwd declare accessor method defined via code gen
FORWARD_DECL(int GetBaseVertexOffset());
int GetPointId()
{
    return int(hd_VertexID) - GetBaseVertexOffset();
}

// Fwd declare selection decoder method defined in hdx/shaders/selection.glslfx
FORWARD_DECL(bool IsPointSelected(int));
float GetPointRasterSize(int pointId)
{
    return IsPointSelected(pointId)? 
                        GetPointSelectedSize() : GetPointSize();
}

void ProcessPointId(int pointId)
{
    vsPointId = pointId;
}

--- --------------------------------------------------------------------------
-- glsl PointId.Fragment.Fallback

int GetPointId()
{
    return -1;
}

--- --------------------------------------------------------------------------
-- layout PointId.Fragment.PointParam

[
    ["in", "int", "vsPointId", "flat"]
]

--- --------------------------------------------------------------------------
-- glsl PointId.Fragment.PointParam

int GetPointId()
{
    return vsPointId;
}
