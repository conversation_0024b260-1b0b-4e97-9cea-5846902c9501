-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/simpleLightingShader.glslfx

#import $TOOLS/glf/shaders/pcfShader.glslfx
#import $TOOLS/glf/shaders/simpleLighting.glslfx

-- configuration
{
    "techniques": {
        "default": {
            "fragmentShader" : {
                "source": [
                    "PCF.ShadowFilterFragmentOnly",
                    "SimpleLighting.LightIntegrator",
                    "SimpleLighting.SimpleLighting",
                    "LightingOverride.SimpleLighting"
                ]
            }
        }
    }
}

-- glsl LightingOverride.SimpleLighting

vec3 FallbackLighting(in vec3 Peye, in vec3 Neye, in vec3 color)
{
    return simpleLightingMaterial(
        vec4(color,1), 
        vec4(Peye,1), 
        Neye, 
        vec4(1)).rgb;
}
