#usda 1.0
(
    "WARNING: THIS FILE IS GENERATED BY usdGenSchema.  DO NOT EDIT."
)

class "StatementsAPI" (
    doc = """Container namespace schema for all renderman statements.

    \\note The longer term goal is for clients to go directly to primvar
    or render-attribute API's, instead of using UsdRi StatementsAPI
    for inherited attributes.  Anticpating this, StatementsAPI
    can smooth the way via a few environment variables:

    * USDRI_STATEMENTS_READ_OLD_ENCODING: Causes StatementsAPI to read
      old-style attributes instead of primvars in the \"ri:\"
      namespace.
    """
)
{
}

class "RiMaterialAPI" (
    doc = """
    \\deprecated Materials should use UsdShadeMaterial instead.
    This schema will be removed in a future release.

    This API provides outputs that connect a material prim to prman 
    shaders and RIS objects."""
)
{
    token outputs:ri:displacement (
        displayGroup = "Outputs"
    )
    token outputs:ri:surface (
        displayGroup = "Outputs"
    )
    token outputs:ri:volume (
        displayGroup = "Outputs"
    )
}

class "RiSplineAPI" (
    doc = '''
    \\deprecated This API schema will be removed in a future release.

    RiSplineAPI is a general purpose API schema used to describe
    a named spline stored as a set of attributes on a prim.
    
    It is an add-on schema that can be applied many times to a prim with
    different spline names. All the attributes authored by the schema
    are namespaced under "$NAME:spline:", with the name of the
    spline providing a namespace for the attributes.

    The spline describes a 2D piecewise cubic curve with a position and
    value for each knot. This is chosen to give straightforward artistic
    control over the shape. The supported basis types are:

    - linear (UsdRiTokens->linear)
    - bspline (UsdRiTokens->bspline)
    - Catmull-Rom (UsdRiTokens->catmullRom)
    '''
)
{
}

class "RiRenderPassAPI" (
    apiSchemas = ["CollectionAPI:matte", "CollectionAPI:cameraVisibility"]
    customData = {
        token[] apiSchemaOverridePropertyNames = ["collection:cameraVisibility:includeRoot"]
    }
    doc = """
    RiRenderPassAPI is an API schema that provides a mechanism
    to set certain Ri statements on each prim in a collection,
    for a given RenderPass prim.

    \\anchor usdRi_cameraVisibility
    The objects that are relevant to the render is specified via the 
    cameraVisibility collection (UsdCollectionAPI) and can be accessed via 
    GetCameraVisibilityCollectionAPI(). Each prim in the collection will have
    ri:visible:camera set to 1.  By default everything in the scene should be 
    visible to camera, so this collection sets includeRoot to 1.

    \\anchor usdRi_matte
    The objects that are relevant to the render is specified via the 
    matte collection (UsdCollectionAPI) and can be accessed via 
    GetMatteCollectionAPI().  Each prim in the collection will have
    ri:matte set to 1.  By default everything in the scene should render
    normally, so this collection sets includeRoot to 0.
    """
)
{
    uniform bool collection:cameraVisibility:includeRoot = 1
}

