#usda 1.0
(
    "This file describes the USD Lux light schemata for code generation."
    subLayers = [
        @usdGeom/schema.usda@
    ]
)

over "GLOBAL" (
    customData = {
        string libraryName      = "usdLux"
        string libraryPath      = "pxr/usd/usdLux"
        dictionary libraryTokens = {
            dictionary lightLink = {
                string doc = """
                This token represents the collection name to use
                with UsdCollectionAPI to represent light-linking
                of a prim with an applied UsdLuxLightAPI.
                """
            }
            dictionary shadowLink = {
                string doc = """
                This token represents the collection name to use
                with UsdCollectionAPI to represent shadow-linking
                of a prim with an applied UsdLuxLightAPI.
                """
            }
            dictionary filterLink = {
                string doc = """
                This token represents the collection name to use
                with UsdCollectionAPI to represent filter-linking
                of a UsdLuxLightFilter prim.
                """
            }
            dictionary orientToStageUpAxis = {
                string doc = """
                This token represents the suffix for a UsdGeomXformOp
                used to orient a light with the stage's up axis.
                """
            }
        }
    }
)
{
}

class "LightAPI" (
    inherits = </APISchemaBase>
    doc = """API schema that imparts the quality of being a light onto a prim. 

    A light is any prim that has this schema applied to it.  This is true 
    regardless of whether LightAPI is included as a built-in API of the prim 
    type (e.g. RectLight or DistantLight) or is applied directly to a Gprim 
    that should be treated as a light.

    <b>Linking</b>

    Lights can be linked to geometry.  Linking controls which geometry
    a light illuminates, and which geometry casts shadows from the light.

    Linking is specified as collections (UsdCollectionAPI) which can
    be accessed via GetLightLinkCollection() and GetShadowLinkCollection().
    Note that these collections have their includeRoot set to true,
    so that lights will illuminate and cast shadows from all objects
    by default.  To illuminate only a specific set of objects, there
    are two options.  One option is to modify the collection paths
    to explicitly exclude everything else, assuming it is known;
    the other option is to set includeRoot to false and explicitly
    include the desired objects.  These are complementary approaches
    that may each be preferable depending on the scenario and how
    to best express the intent of the light setup.
    """
    customData = {
        dictionary extraPlugInfo = {
            bool providesUsdShadeConnectableAPIBehavior = 1
        }
        string extraIncludes = """
#include "pxr/usd/usd/collectionAPI.h"
#include "pxr/usd/usdShade/input.h"
#include "pxr/usd/usdShade/output.h" """
    }
    prepend apiSchemas = ["CollectionAPI:lightLink", "CollectionAPI:shadowLink"]
) {
    uniform bool collection:lightLink:includeRoot = 1 (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    uniform bool collection:shadowLink:includeRoot = 1 (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    uniform token light:shaderId = "" (
        displayGroup = "Internal"
        doc = """Default ID for the light's shader. 
        This defines the shader ID for this light when a render context specific
        shader ID is not available. 

        The default shaderId for the intrinsic UsdLux lights (RectLight, 
        DistantLight, etc.) are set to default to the light's type name. For 
        each intrinsic UsdLux light, we will always register an SdrShaderNode in
        the SdrRegistry, with the identifier matching the type name and the 
        source type "USD", that corresponds to the light's inputs.
        \\see GetShaderId
        \\see GetShaderIdAttrForRenderContext
        \\see SdrRegistry::GetShaderNodeByIdentifier
        \\see SdrRegistry::GetShaderNodeByIdentifierAndType
        """
        customData = {
            token apiName = "shaderId"
        }
    )
    uniform token light:materialSyncMode = "noMaterialResponse" (
        displayGroup = "Geometry"
        displayName = "Material Sync Mode"
        doc = """
        For a LightAPI applied to geometry that has a bound Material, 
        which is entirely or partly emissive, this specifies the relationship 
        of the Material response to the lighting response.
        Valid values are:
        - materialGlowTintsLight: All primary and secondary rays see the 
          emissive/glow response as dictated by the bound Material while the 
          base color seen by light rays (which is then modulated by all of the 
          other LightAPI controls) is the multiplication of the color feeding 
          the emission/glow input of the Material (i.e. its surface or volume 
          shader) with the scalar or pattern input to *inputs:color*.
          This allows the light's color to tint the geometry's glow color while 
          preserving access to intensity and other light controls as ways to 
          further modulate the illumination.
        - independent: All primary and secondary rays see the emissive/glow 
          response as dictated by the bound Material, while the base color seen 
          by light rays is determined solely by *inputs:color*. Note that for 
          partially emissive geometry (in which some parts are reflective 
          rather than emissive), a suitable pattern must be connected to the 
          light's color input, or else the light will radiate uniformly from 
          the geometry.
        - noMaterialResponse: The geometry behaves as if there is no Material
          bound at all, i.e. there is no diffuse, specular, or transmissive 
          response. The base color of light rays is entirely controlled by the
          *inputs:color*. This is the standard mode for "canonical" lights in 
          UsdLux and indicates to renderers that a Material will either never 
          be bound or can always be ignored.
        """
        allowedTokens = ["materialGlowTintsLight", 
                         "independent", 
                         "noMaterialResponse"]
        customData = {
            token apiName = "materialSyncMode"
        }
    )
    float inputs:intensity = 1 (
        displayGroup = "Basic"
        displayName = "Intensity"
        doc = """Scales the power of the light linearly."""
        customData = {
            token apiName = "intensity"
        }
    )
    float inputs:exposure = 0 (
        displayGroup = "Basic"
        displayName = "Exposure"
        doc = """Scales the power of the light exponentially as a power
        of 2 (similar to an F-stop control over exposure).  The result
        is multiplied against the intensity."""
        customData = {
            token apiName = "exposure"
        }
    )
    float inputs:diffuse = 1.0 (
        displayGroup = "Refine"
        displayName = "Diffuse Multiplier"
        doc = """A multiplier for the effect of this light on the diffuse
        response of materials.  This is a non-physical control."""
        customData = {
            token apiName = "diffuse"
        }
    )
    float inputs:specular = 1.0 (
        displayGroup = "Refine"
        displayName = "Specular Multiplier"
        doc = """A multiplier for the effect of this light on the specular
        response of materials.  This is a non-physical control."""
        customData = {
            token apiName = "specular"
        }
    )
    bool inputs:normalize = false (
        displayGroup = "Advanced"
        displayName = "Normalize Power"
        doc = """Normalizes power by the surface area of the light.
        This makes it easier to independently adjust the power and shape
        of the light, by causing the power to not vary with the area or
        angular size of the light."""
        customData = {
            token apiName = "normalize"
        }
    )
    color3f inputs:color = (1, 1, 1) (
        displayGroup = "Basic"
        displayName = "Color"
        doc = """The color of emitted light, in energy-linear terms."""
        customData = {
            token apiName = "color"
        }
    )
    bool inputs:enableColorTemperature = false (
        displayGroup = "Basic"
        displayName = "Enable Color Temperature"
        doc = """Enables using colorTemperature."""
        customData = {
            token apiName = "enableColorTemperature"
        }
    )
    float inputs:colorTemperature = 6500 (
        displayGroup = "Basic"
        displayName = "Color Temperature"
        doc = """Color temperature, in degrees Kelvin, representing the
        white point.  The default is a common white point, D65.  Lower
        values are warmer and higher values are cooler.  The valid range
        is from 1000 to 10000. Only takes effect when
        enableColorTemperature is set to true.  When active, the
        computed result multiplies against the color attribute.
        See UsdLuxBlackbodyTemperatureAsRgb()."""
        customData = {
            token apiName = "colorTemperature"
        }
    )
    rel light:filters (
        doc = """Relationship to the light filters that apply to this light."""
        customData = {
            token apiName = "filters"
        }
    )
}

class "MeshLightAPI" (
    inherits = </APISchemaBase>
    doc = """This is the preferred API schema to apply to 
    \\ref UsdGeomMesh "Mesh" type prims when adding light behaviors to a mesh. 
    At its base, this API schema has the built-in behavior of applying LightAPI 
    to the mesh and overriding the default materialSyncMode to allow the 
    emission/glow of the bound material to affect the color of the light. 
    But, it additionally serves as a hook for plugins to attach additional 
    properties to "mesh lights" through the creation of API schemas which are 
    authored to auto-apply to MeshLightAPI.
    \\see \\ref Usd_AutoAppliedAPISchemas
    """
    prepend apiSchemas = ["LightAPI"]
) {
    uniform token light:shaderId = "MeshLight" (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    uniform token light:materialSyncMode = "materialGlowTintsLight" (
        customData = {
            bool apiSchemaOverride = true
        }
    )
}

class "VolumeLightAPI" (
    inherits = </APISchemaBase>
    doc = """This is the preferred API schema to apply to 
    \\ref UsdVolVolume "Volume" type prims when adding light behaviors to a 
    volume. At its base, this API schema has the built-in behavior of applying 
    LightAPI to the volume and overriding the default materialSyncMode to allow 
    the emission/glow of the bound material to affect the color of the light. 
    But, it additionally serves as a hook for plugins to attach additional 
    properties to "volume lights" through the creation of API schemas which are 
    authored to auto-apply to VolumeLightAPI.
    \\see \\ref Usd_AutoAppliedAPISchemas
    """
    prepend apiSchemas = ["LightAPI"]
) {
    uniform token light:shaderId = "VolumeLight" (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    uniform token light:materialSyncMode = "materialGlowTintsLight" (
        customData = {
            bool apiSchemaOverride = true
        }
    )
}

class "LightListAPI" (
    inherits = </APISchemaBase>
    doc = """API schema to support discovery and publishing of lights in a scene.

    \\section UsdLuxLightListAPI_Discovery Discovering Lights via Traversal
    
    To motivate this API, consider what is required to discover all
    lights in a scene.  We must load all payloads and traverse all prims:

    \\code
    01  // Load everything on the stage so we can find all lights,
    02  // including those inside payloads
    03  stage->Load();
    04  
    05  // Traverse all prims, checking if they have an applied UsdLuxLightAPI
    06  // (Note: ignoring instancing and a few other things for simplicity)
    07  SdfPathVector lights;
    08  for (UsdPrim prim: stage->Traverse()) {
    09      if (prim.HasAPI<UsdLuxLightAPI>()) {
    10          lights.push_back(i->GetPath());
    11      }
    12  }
    \\endcode

    This traversal -- suitably elaborated to handle certain details --
    is the first and simplest thing UsdLuxLightListAPI provides.
    UsdLuxLightListAPI::ComputeLightList() performs this traversal and returns
    all lights in the scene:

    \\code
    01  UsdLuxLightListAPI listAPI(stage->GetPseudoRoot());
    02  SdfPathVector lights = listAPI.ComputeLightList();
    \\endcode

    \\section UsdLuxLightListAPI_LightList Publishing a Cached Light List

    Consider a USD client that needs to quickly discover lights but
    wants to defer loading payloads and traversing the entire scene
    where possible, and is willing to do up-front computation and
    caching to achieve that.

    UsdLuxLightListAPI provides a way to cache the computed light list,
    by publishing the list of lights onto prims in the model
    hierarchy.  Consider a big set that contains lights:

    \\code
    01  def Xform "BigSetWithLights" (
    02      kind = "assembly"
    03      payload = @BigSetWithLights.usd@   // Heavy payload
    04  ) {
    05      // Pre-computed, cached list of lights inside payload
    06      rel lightList = [
    07          <./Lights/light_1>,
    08          <./Lights/light_2>,
    09          ...
    10      ]
    11      token lightList:cacheBehavior = "consumeAndContinue";
    12  }
    \\endcode

    The lightList relationship encodes a set of lights, and the
    lightList:cacheBehavior property provides fine-grained
    control over how to use that cache.  (See details below.)

    The cache can be created by first invoking
    ComputeLightList(ComputeModeIgnoreCache) to pre-compute the list
    and then storing the result with UsdLuxLightListAPI::StoreLightList().

    To enable efficient retrieval of the cache, it should be stored
    on a model hierarchy prim.  Furthermore, note that while you can
    use a UsdLuxLightListAPI bound to the pseudo-root prim to query the
    lights (as in the example above) because it will perform a
    traversal over descendants, you cannot store the cache back to the
    pseduo-root prim.

    To consult the cached list, we invoke
    ComputeLightList(ComputeModeConsultModelHierarchyCache):

    \\code
    01  // Find and load all lights, using lightList cache where available
    02  UsdLuxLightListAPI list(stage->GetPseudoRoot());
    03  SdfPathSet lights = list.ComputeLightList(
    04      UsdLuxLightListAPI::ComputeModeConsultModelHierarchyCache);
    05  stage.LoadAndUnload(lights, SdfPathSet());
    \\endcode

    In this mode, ComputeLightList() will traverse the model
    hierarchy, accumulating cached light lists.

    \\section UsdLuxLightListAPI_CacheBehavior Controlling Cache Behavior

    The lightList:cacheBehavior property gives additional fine-grained
    control over cache behavior:

    \\li The fallback value, "ignore", indicates that the lightList should
    be disregarded.  This provides a way to invalidate cache entries.
    Note that unless "ignore" is specified, a lightList with an empty
    list of targets is considered a cache indicating that no lights
    are present.

    \\li The value "consumeAndContinue" indicates that the cache should
    be consulted to contribute lights to the scene, and that recursion
    should continue down the model hierarchy in case additional lights
    are added as descedants. This is the default value established when
    StoreLightList() is invoked. This behavior allows the lights within
    a large model, such as the BigSetWithLights example above, to be
    published outside the payload, while also allowing referencing and
    layering to add additional lights over that set.

    \\li The value "consumeAndHalt" provides a way to terminate recursive
    traversal of the scene for light discovery. The cache will be
    consulted but no descendant prims will be examined.

    \\section UsdLuxLightListAPI_Instancing Instancing

    Where instances are present, UsdLuxLightListAPI::ComputeLightList() will
    return the instance-unique paths to any lights discovered within
    those instances.  Lights within a UsdGeomPointInstancer will
    not be returned, however, since they cannot be referred to
    solely via paths.
"""
) {
    rel lightList (
        doc = """Relationship to lights in the scene."""
    )
    token lightList:cacheBehavior (
        doc = """
        Controls how the lightList should be interpreted.
        Valid values are:
        - consumeAndHalt: The lightList should be consulted,
          and if it exists, treated as a final authoritative statement
          of any lights that exist at or below this prim, halting
          recursive discovery of lights.
        - consumeAndContinue: The lightList should be consulted,
          but recursive traversal over nameChildren should continue
          in case additional lights are added by descendants.
        - ignore: The lightList should be entirely ignored.  This
          provides a simple way to temporarily invalidate an existing
          cache.  This is the fallback behavior.
        """
        allowedTokens = ["consumeAndHalt", "consumeAndContinue", "ignore"]
    )
}

class "ListAPI" (
    inherits = </APISchemaBase>
    doc = """
    \\deprecated
    Use LightListAPI instead
"""
) {
    rel lightList (
        doc = """Relationship to lights in the scene."""
    )
    token lightList:cacheBehavior (
        doc = """
        Controls how the lightList should be interpreted.
        Valid values are:
        - consumeAndHalt: The lightList should be consulted,
          and if it exists, treated as a final authoritative statement
          of any lights that exist at or below this prim, halting
          recursive discovery of lights.
        - consumeAndContinue: The lightList should be consulted,
          but recursive traversal over nameChildren should continue
          in case additional lights are added by descendants.
        - ignore: The lightList should be entirely ignored.  This
          provides a simple way to temporarily invalidate an existing
          cache.  This is the fallback behavior.
        """
        allowedTokens = ["consumeAndHalt", "consumeAndContinue", "ignore"]
    )
}

class "ShapingAPI" (
    inherits = </APISchemaBase>
    doc = """Controls for shaping a light's emission."""
    customData = {
        string extraIncludes = """
#include "pxr/usd/usdShade/input.h"
#include "pxr/usd/usdShade/output.h" """
    }

) {
    float inputs:shaping:focus = 0 (
        displayGroup = "Shaping"
        displayName = "Emission Focus"
        doc = """A control to shape the spread of light.  Higher focus
        values pull light towards the center and narrow the spread.
        Implemented as an off-axis cosine power exponent.
        TODO: clarify semantics"""
        customData = {
            token apiName = "shaping:focus"
        }
    )
    color3f inputs:shaping:focusTint = (0, 0, 0) (
        displayGroup = "Shaping"
        displayName = "Emission Focus Tint"
        doc = """Off-axis color tint.  This tints the emission in the
        falloff region.  The default tint is black.
        TODO: clarify semantics"""
        customData = {
            token apiName = "shaping:focusTint"
        }
    )
    float inputs:shaping:cone:angle = 90 (
        displayGroup = "Shaping"
        displayName = "Cone Angle"
        doc = """Angular limit off the primary axis to restrict the
        light spread."""
        customData = {
            token apiName = "shaping:cone:angle"
        }
    )
    float inputs:shaping:cone:softness = 0 (
        displayGroup = "Shaping"
        displayName = "Cone Softness"
        doc = """Controls the cutoff softness for cone angle.
        TODO: clarify semantics"""
        customData = {
            token apiName = "shaping:cone:softness"
        }
    )
    asset inputs:shaping:ies:file (
        displayGroup = "Shaping"
        displayName = "IES Profile"
        doc = """An IES (Illumination Engineering Society) light
        profile describing the angular distribution of light."""
        customData = {
            token apiName = "shaping:ies:file"
        }
    )
    float inputs:shaping:ies:angleScale = 0 (
        displayGroup = "Shaping"
        displayName = "Profile Scale"
        doc = """Rescales the angular distribution of the IES profile.
        TODO: clarify semantics"""
        customData = {
            token apiName = "shaping:ies:angleScale"
        }
    )
    bool inputs:shaping:ies:normalize = false (
        displayGroup = "Shaping"
        displayName = "Profile Normalization"
        doc = """Normalizes the IES profile so that it affects the shaping
        of the light while preserving the overall energy output."""
        customData = {
            token apiName = "shaping:ies:normalize"
        }
    )
}

class "ShadowAPI" (
    inherits = </APISchemaBase>
    doc = """Controls to refine a light's shadow behavior.  These are
    non-physical controls that are valuable for visual lighting work."""
    customData = {
        string extraIncludes = """
#include "pxr/usd/usdShade/input.h"
#include "pxr/usd/usdShade/output.h" """
    }

) {
    bool inputs:shadow:enable = true (
        displayGroup = "Shadows"
        displayName = "Enable Shadows"
        doc = """Enables shadows to be cast by this light."""
        customData = {
            token apiName = "shadow:enable"
        }
    )
    color3f inputs:shadow:color = (0, 0, 0) (
        displayGroup = "Shadows"
        displayName = "Shadow Color"
        doc = """The color of shadows cast by the light.  This is a
        non-physical control.  The default is to cast black shadows."""
        customData = {
            token apiName = "shadow:color"
        }
    )
    float inputs:shadow:distance = -1.0 (
        displayGroup = "Shadows"
        displayName = "Shadow Max Distance"
        doc = """The maximum distance shadows are cast. The distance is
        measured as the distance between the point on the surface and the 
        occluder.
        The default value (-1) indicates no limit.
        """
        customData = {
            token apiName = "shadow:distance"
        }
    )
    float inputs:shadow:falloff = -1.0 (
        displayGroup = "Shadows"
        displayName = "Shadow Falloff"
        doc = """The size of the shadow falloff zone within the shadow max 
        distance, which can be used to hide the hard cut-off for shadows seen 
        stretching past the max distance. The falloff zone is the area that 
        fades from full shadowing at the beginning of the falloff zone to no 
        shadowing at the max distance from the occluder. The falloff zone 
        distance cannot exceed the shadow max distance. A falloff value equal 
        to or less than zero (with -1 as the default) indicates no falloff. 
        """
        customData = {
            token apiName = "shadow:falloff"
        }
    )
    float inputs:shadow:falloffGamma = 1.0 (
        displayGroup = "Shadows"
        displayName = "Shadow Falloff Gamma"
        doc = """A gamma (i.e., exponential) control over shadow strength
        with linear distance within the falloff zone. This controls the rate
        of the falloff.
        This requires the use of shadowDistance and shadowFalloff."""
        customData = {
            token apiName = "shadow:falloffGamma"
        }
    )
}

class LightFilter "LightFilter" (
    inherits = </Xformable>
    doc = """A light filter modifies the effect of a light.
    Lights refer to filters via relationships so that filters may be
    shared.

    <b>Linking</b>

    Filters can be linked to geometry.  Linking controls which geometry
    a light-filter affects, when considering the light filters attached
    to a light illuminating the geometry.

    Linking is specified as a collection (UsdCollectionAPI) which can
    be accessed via GetFilterLinkCollection().
    """
    customData = {
        dictionary extraPlugInfo = {
            bool providesUsdShadeConnectableAPIBehavior = 1
        }
        string extraIncludes = """
#include "pxr/usd/usd/collectionAPI.h"
#include "pxr/usd/usdShade/input.h"
#include "pxr/usd/usdShade/output.h" """
    }
    prepend apiSchemas = ["CollectionAPI:filterLink"]
) {
    uniform bool collection:filterLink:includeRoot = 1 (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    uniform token lightFilter:shaderId = "" (
        displayGroup = "Internal"
        doc = """Default ID for the light filter's shader. 
        This defines the shader ID for this light filter when a render context 
        specific shader ID is not available. 

        \\see GetShaderId
        \\see GetShaderIdAttrForRenderContext
        \\see SdrRegistry::GetShaderNodeByIdentifier
        \\see SdrRegistry::GetShaderNodeByIdentifierAndType
        """
        customData = {
            token apiName = "shaderId"
        }
    )

}

class "BoundableLightBase" (
    inherits = </Boundable>
    doc = """Base class for intrinsic lights that are boundable.

    The primary purpose of this class is to provide a direct API to the 
    functions provided by LightAPI for concrete derived light types.
    """
    customData = {
        string extraIncludes = """#include "pxr/usd/usdLux/lightAPI.h" """
    }
    prepend apiSchemas = ["LightAPI"]
) {
}

class "NonboundableLightBase" (
    inherits = </Xformable>
    doc = """Base class for intrinsic lights that are not boundable.

    The primary purpose of this class is to provide a direct API to the 
    functions provided by LightAPI for concrete derived light types.
    """
    customData = {
        string extraIncludes = """#include "pxr/usd/usdLux/lightAPI.h" """
    }
    prepend apiSchemas = ["LightAPI"]
) {
}

class DistantLight "DistantLight" (
    inherits = </NonboundableLightBase>
    doc = """Light emitted from a distant source along the -Z axis.
    Also known as a directional light."""
) {
    uniform token light:shaderId = "DistantLight" (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    float inputs:angle = 0.53 (
        displayGroup = "Basic"
        displayName = "Angle Extent"
        doc = """Angular size of the light in degrees.
        As an example, the Sun is approximately 0.53 degrees as seen from Earth.
        Higher values broaden the light and therefore soften shadow edges.
        """
        customData = {
            token apiName = "angle"
        }
    )
    float inputs:intensity = 50000 (
        doc = """Scales the emission of the light linearly.
        The DistantLight has a high default intensity to approximate the Sun."""
        customData = {
            token apiName = "intensity"
            bool apiSchemaOverride = true
        }
    )
}

class DiskLight "DiskLight" (
    customData = {
        dictionary extraPlugInfo = {
            bool implementsComputeExtent = 1
        }
    }
    inherits = </BoundableLightBase>
    doc = """Light emitted from one side of a circular disk.
    The disk is centered in the XY plane and emits light along the -Z axis."""
) {
    uniform token light:shaderId = "DiskLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    float inputs:radius = 0.5 (
        displayGroup = "Geometry"
        displayName = "Radius"
        doc = "Radius of the disk."
        customData = {
            token apiName = "radius"
        }
    )
}

class RectLight "RectLight" (
    customData = {
        dictionary extraPlugInfo = {
            bool implementsComputeExtent = 1
        }
    }
    inherits = </BoundableLightBase>
    doc = """Light emitted from one side of a rectangle.
    The rectangle is centered in the XY plane and emits light along the -Z axis.
    The rectangle is 1 unit in length in the X and Y axis.  In the default 
    position, a texture file's min coordinates should be at (+X, +Y) and 
    max coordinates at (-X, -Y)."""
) {
    uniform token light:shaderId = "RectLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    float inputs:width = 1 (
        displayGroup = "Geometry"
        displayName = "Width"
        doc = "Width of the rectangle, in the local X axis."
        customData = {
            token apiName = "width"
        }

    )
    float inputs:height = 1 (
        displayGroup = "Geometry"
        displayName = "Height"
        doc = "Height of the rectangle, in the local Y axis."
        customData = {
            token apiName = "height"
        }
    )
    asset inputs:texture:file (
        displayGroup = "Basic"
        displayName = "Color Map"
        doc = """A color texture to use on the rectangle."""
        customData = {
            token apiName = "textureFile"
        }
    )
}

class SphereLight "SphereLight" (
    customData = {
        dictionary extraPlugInfo = {
            bool implementsComputeExtent = 1
        }
    }
    inherits = </BoundableLightBase>
    doc = """Light emitted outward from a sphere."""
) {
    uniform token light:shaderId = "SphereLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    float inputs:radius = 0.5 (
        displayGroup = "Geometry"
        displayName = "Radius"
        doc = "Radius of the sphere."
        customData = {
            token apiName = "radius"
        }
    )
    bool treatAsPoint = false (
        displayGroup = "Advanced"
        displayName = "Treat As Point"
        doc = """A hint that this light can be treated as a 'point'
        light (effectively, a zero-radius sphere) by renderers that
        benefit from non-area lighting. Renderers that only support
        area lights can disregard this."""
    )
}

class CylinderLight "CylinderLight" (
    customData = {
        dictionary extraPlugInfo = {
            bool implementsComputeExtent = 1
        }
    }
    inherits = </BoundableLightBase>
    doc = """Light emitted outward from a cylinder.
    The cylinder is centered at the origin and has its major axis on the X axis.
    The cylinder does not emit light from the flat end-caps.
    """
) {
    uniform token light:shaderId = "CylinderLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    float inputs:length = 1 (
        displayGroup = "Geometry"
        displayName = "Length"
        doc = "Length of the cylinder, in the local X axis."
        customData = {
            token apiName = "length"
        }
    )
    float inputs:radius = 0.5 (
        displayGroup = "Geometry"
        displayName = "Radius"
        doc = "Radius of the cylinder."
        customData = {
            token apiName = "radius"
        }
    )
    bool treatAsLine = false (
        displayGroup = "Advanced"
        displayName = "Treat As Line"
        doc = """A hint that this light can be treated as a 'line'
        light (effectively, a zero-radius cylinder) by renderers that
        benefit from non-area lighting. Renderers that only support
        area lights can disregard this."""
    )
}

class GeometryLight "GeometryLight" (
    inherits = </NonboundableLightBase>
    doc = """\\deprecated
    Light emitted outward from a geometric prim (UsdGeomGprim),
    which is typically a mesh."""
) {
    rel geometry (
        doc = """Relationship to the geometry to use as the light source."""
    )
    uniform token light:shaderId = "GeometryLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
}

class DomeLight "DomeLight" (
    inherits = </NonboundableLightBase>
    doc = """Light emitted inward from a distant external environment,
    such as a sky or IBL light probe.
    
    In this version of the dome light, the dome's default orientation is such
    that its top pole is aligned with the world's +Y axis. This adheres to the
    OpenEXR specification for latlong environment maps.  From the OpenEXR
    documentation:
    
    -------------------------------------------------------------------------
    Latitude-Longitude Map:
    
    The environment is projected onto the image using polar coordinates
    (latitude and longitude).  A pixel's x coordinate corresponds to
    its longitude, and the y coordinate corresponds to its latitude.
    Pixel (dataWindow.min.x, dataWindow.min.y) has latitude +pi/2 and
    longitude +pi; pixel (dataWindow.max.x, dataWindow.max.y) has
    latitude -pi/2 and longitude -pi.
    
    In 3D space, latitudes -pi/2 and +pi/2 correspond to the negative and
    positive y direction.  Latitude 0, longitude 0 points into positive
    z direction; and latitude 0, longitude pi/2 points into positive x
    direction.
    
    The size of the data window should be 2*N by N pixels (width by height),
    where N can be any integer greater than 0.
    -------------------------------------------------------------------------
"""
) {
    uniform token light:shaderId = "DomeLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    asset inputs:texture:file (
        displayGroup = "Basic"
        displayName = "Color Map"
        doc = """A color texture to use on the dome, such as an HDR (high
        dynamic range) texture intended for IBL (image based lighting)."""
        customData = {
            token apiName = "textureFile"
        }
    )
    token inputs:texture:format = "automatic" (
        displayGroup = "Basic"
        displayName = "Color Map Format"
        allowedTokens = ["automatic", "latlong", "mirroredBall", "angular", "cubeMapVerticalCross"]
        doc = """
        Specifies the parameterization of the color map file.
        Valid values are:
        - automatic: Tries to determine the layout from the file itself.
          For example, Renderman texture files embed an explicit
          parameterization.
        - latlong: Latitude as X, longitude as Y.
        - mirroredBall: An image of the environment reflected in a
          sphere, using an implicitly orthogonal projection.
        - angular: Similar to mirroredBall but the radial dimension
          is mapped linearly to the angle, providing better sampling
          at the edges.
        - cubeMapVerticalCross: A cube map with faces laid out as a
          vertical cross.
        """
        customData = {
            token apiName = "textureFormat"
        }
    )
    rel portals (
        doc = """Optional portals to guide light sampling."""
    )
    float guideRadius = 1.0e5 (
        displayGroup = "Guides"
        displayName = "Radius"
        doc = """The radius of guide geometry to use to visualize the dome light.  The default is 1 km for scenes whose metersPerUnit is the USD default of 0.01 (i.e., 1 world unit is 1 cm)."""
    )
}

class DomeLight_1 "DomeLight_1" (
    inherits = </NonboundableLightBase>
    doc = """Light emitted inward from a distant external environment,
    such as a sky or IBL light probe.
    
    In this version of the dome light, the dome's default orientation is
    determined by its *poleAxis* property. The fallback value, "scene", means
    that the dome starts with its top pole aligned with the stage's up axis.
    
    Note that the rotation necessary to align the dome light with its *poleAxis*
    is intended to be applied by a renderer to only the dome itself, and *not*
    to inherit down to any USD namespace children of the dome light prim.
    
    If *poleAxis* is set to "Y" or "scene" and the stage's up axis is "Y", the
    dome's default orientation will adhere to the OpenEXR specification for
    latlong environment maps.  From the OpenEXR documentation:
    
    -------------------------------------------------------------------------
    Latitude-Longitude Map:
    
    The environment is projected onto the image using polar coordinates
    (latitude and longitude).  A pixel's x coordinate corresponds to
    its longitude, and the y coordinate corresponds to its latitude.
    Pixel (dataWindow.min.x, dataWindow.min.y) has latitude +pi/2 and
    longitude +pi; pixel (dataWindow.max.x, dataWindow.max.y) has
    latitude -pi/2 and longitude -pi.
    
    In 3D space, latitudes -pi/2 and +pi/2 correspond to the negative and
    positive y direction.  Latitude 0, longitude 0 points into positive
    z direction; and latitude 0, longitude pi/2 points into positive x
    direction.
    
    The size of the data window should be 2*N by N pixels (width by height),
    where N can be any integer greater than 0.
    -------------------------------------------------------------------------
    
    If *poleAxis* is set to "Z" or "scene" and the stage's up axis is "Z",
    latitudes -pi/2 and +pi/2 will instead correspond to the negative and
    positive Z direction, and latitude 0, longitude 0 will instead point into
    the negative Y direction in 3D space.
"""
) {
    uniform token light:shaderId = "DomeLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    asset inputs:texture:file (
        displayGroup = "Basic"
        displayName = "Color Map"
        doc = """A color texture to use on the dome, such as an HDR (high
        dynamic range) texture intended for IBL (image based lighting)."""
        customData = {
            token apiName = "textureFile"
        }
    )
    token inputs:texture:format = "automatic" (
        displayGroup = "Basic"
        displayName = "Color Map Format"
        allowedTokens = ["automatic", "latlong", "mirroredBall", "angular", "cubeMapVerticalCross"]
        doc = """
        Specifies the parameterization of the color map file.
        Valid values are:
        - automatic: Tries to determine the layout from the file itself.
          For example, Renderman texture files embed an explicit
          parameterization.
        - latlong: Latitude as X, longitude as Y.
        - mirroredBall: An image of the environment reflected in a
          sphere, using an implicitly orthogonal projection.
        - angular: Similar to mirroredBall but the radial dimension
          is mapped linearly to the angle, providing better sampling
          at the edges.
        - cubeMapVerticalCross: A cube map with faces laid out as a
          vertical cross.
        """
        customData = {
            token apiName = "textureFormat"
        }
    )
    rel portals (
        doc = """Optional portals to guide light sampling."""
    )
    float guideRadius = 1.0e5 (
        displayGroup = "Guides"
        displayName = "Radius"
        doc = """The radius of guide geometry to use to visualize the dome light.  The default is 1 km for scenes whose metersPerUnit is the USD default of 0.01 (i.e., 1 world unit is 1 cm)."""
    )
    uniform token poleAxis = "scene" (
        displayGroup = "Advanced"
        displayName = "Pole Axis"
        allowedTokens = ["scene", "Y", "Z"]
        doc = """
        A token which indicates the starting alignment of the dome
        light's top pole. This alignment is for the dome itself and is *not*
        inherited by the namespace children of the dome.
        Valid values are:
        - scene: The dome light's top pole is aligned with the stage's up axis.
        - Y: The dome light's top pole is aligned with the +Y axis.
        - Z: The dome light's top pole is aligned with the +Z axis.
        """
    )
}

class PortalLight "PortalLight" (
    customData = {
        dictionary extraPlugInfo = {
            bool implementsComputeExtent = 1
        }
    }
    inherits = </BoundableLightBase>
    doc = """A rectangular portal in the local XY plane that guides sampling
    of a dome light.  Transmits light in the -Z direction.
    The rectangle is 1 unit in length."""
) {
    uniform token light:shaderId = "PortalLight"  (
        customData = {
            bool apiSchemaOverride = true
        }
    )
    float inputs:width = 1 (
        displayGroup = "Geometry"
        displayName = "Width"
        doc = "Width of the portal rectangle in the local X axis."
        customData = {
            token apiName = "width"
        }

    )
    float inputs:height = 1 (
        displayGroup = "Geometry"
        displayName = "Height"
        doc = "Height of the portal rectangle in the local Y axis."
        customData = {
            token apiName = "height"
        }
    )
}

class PluginLight "PluginLight" (
    inherits = </Xformable>
    doc = """Light that provides properties that allow it to identify an 
    external SdrShadingNode definition, through UsdShadeNodeDefAPI, that can be 
    provided to render delegates without the need to provide a schema 
    definition for the light's type.

    \\see \\ref usdLux_PluginSchemas
"""
    prepend apiSchemas = ["NodeDefAPI", "LightAPI"]
    customData = {
        string extraIncludes = """
#include "pxr/usd/usdShade/nodeDefAPI.h" """
    }
) {
}

class PluginLightFilter "PluginLightFilter" (
    inherits = </LightFilter>
    doc = """Light filter that provides properties that allow it to identify an 
    external SdrShadingNode definition, through UsdShadeNodeDefAPI, that can be 
    provided to render delegates without the need to provide a schema 
    definition for the light filter's type.

    \\see \\ref usdLux_PluginSchemas
"""
    prepend apiSchemas = ["NodeDefAPI"]
    customData = {
        string extraIncludes = """
#include "pxr/usd/usdShade/nodeDefAPI.h" """
    }
) {
}
