# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdLuxBoundableLightBase": {
                        "alias": {
                            "UsdSchemaBase": "BoundableLightBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "schemaIdentifier": "BoundableLightBase", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdLuxCylinderLight": {
                        "alias": {
                            "UsdSchemaBase": "CylinderLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "CylinderLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDiskLight": {
                        "alias": {
                            "UsdSchemaBase": "DiskLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "DiskLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDistantLight": {
                        "alias": {
                            "UsdSchemaBase": "DistantLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaIdentifier": "DistantLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDomeLight": {
                        "alias": {
                            "UsdSchemaBase": "DomeLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaIdentifier": "DomeLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxDomeLight_1": {
                        "alias": {
                            "UsdSchemaBase": "DomeLight_1"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaIdentifier": "DomeLight_1", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxGeometryLight": {
                        "alias": {
                            "UsdSchemaBase": "GeometryLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxNonboundableLightBase"
                        ], 
                        "schemaIdentifier": "GeometryLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "LightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaIdentifier": "LightAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "LightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "providesUsdShadeConnectableAPIBehavior": true, 
                        "schemaIdentifier": "LightFilter", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxLightListAPI": {
                        "alias": {
                            "UsdSchemaBase": "LightListAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "LightListAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxListAPI": {
                        "alias": {
                            "UsdSchemaBase": "ListAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ListAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxMeshLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "MeshLightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "MeshLightAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxNonboundableLightBase": {
                        "alias": {
                            "UsdSchemaBase": "NonboundableLightBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaIdentifier": "NonboundableLightBase", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdLuxPluginLight": {
                        "alias": {
                            "UsdSchemaBase": "PluginLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaIdentifier": "PluginLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxPluginLightFilter": {
                        "alias": {
                            "UsdSchemaBase": "PluginLightFilter"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxLightFilter"
                        ], 
                        "schemaIdentifier": "PluginLightFilter", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxPortalLight": {
                        "alias": {
                            "UsdSchemaBase": "PortalLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "PortalLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxRectLight": {
                        "alias": {
                            "UsdSchemaBase": "RectLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "RectLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxShadowAPI": {
                        "alias": {
                            "UsdSchemaBase": "ShadowAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ShadowAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxShapingAPI": {
                        "alias": {
                            "UsdSchemaBase": "ShapingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ShapingAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLuxSphereLight": {
                        "alias": {
                            "UsdSchemaBase": "SphereLight"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdLuxBoundableLightBase"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "SphereLight", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdLuxVolumeLightAPI": {
                        "alias": {
                            "UsdSchemaBase": "VolumeLightAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "VolumeLightAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdLux_DiscoveryPlugin": {
                        "bases": [
                            "NdrDiscoveryPlugin"
                        ]
                    }, 
                    "UsdLux_LightDefParserPlugin": {
                        "bases": [
                            "NdrParserPlugin"
                        ]
                    }
                }
            }, 
            "LibraryPath": "", 
            "Name": "usdLux", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
