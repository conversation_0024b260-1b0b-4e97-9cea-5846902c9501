-- glslfx version 0.1

//
// Copyright 2016 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

---Percentage-Closer Filtering (PCF)

-- glsl PCF.ShadowFilterVertex
// ---------------------------------------------------------------------------
//  PCF compute shadow filter length in vertex shader
// ---------------------------------------------------------------------------
#define REQUIRE_SHADOW_FILTER_WIDTH

-- glsl PCF.ShadowFilterFragment
// ---------------------------------------------------------------------------
//  PCF shadow filtering in fragment shader
// ---------------------------------------------------------------------------

#if NUM_SHADOWS > 0

#define SHADOW_FILTER

#define JITTER_NUM_SAMPLES 16
#define PCF_NUM_SAMPLES JITTER_NUM_SAMPLES 

FORWARD_DECL(float shadowSample(int index, vec4 coord));
FORWARD_DECL(float shadowCompare(int index, vec4 coord));

vec2 jitter[JITTER_NUM_SAMPLES] = {
    vec2(-0.94201624, -0.39906216),
    vec2( 0.94558609, -0.76890725),
    vec2(-0.09418410, -0.92938870),
    vec2( 0.34495938,  0.29387760),
    vec2(-0.91588581,  0.45771432),
    vec2(-0.81544232, -0.87912464),
    vec2(-0.38277543,  0.27676845),
    vec2( 0.97484398,  0.75648379),
    vec2( 0.44323325, -0.97511554),
    vec2( 0.53742981, -0.47373420),
    vec2(-0.26496911, -0.41893023),
    vec2( 0.79197514,  0.19090188),
    vec2(-0.24188840,  0.99706507),
    vec2(-0.81409955,  0.91437590),
    vec2( 0.19984126,  0.78641367),
    vec2( 0.14383161, -0.14100790)
};

in vec2 FshadowFilterWidth[NUM_SHADOWS];

float
shadowFilter(int index, vec4 Pshadow, vec4 Peye)
{
    vec2 filterWidth = FshadowFilterWidth[index];

    float sum = 0.0;
    for (int i=0; i<PCF_NUM_SAMPLES; ++i) {
        // Note that the perspective division has not been applied to
        // either Pshadow or filterWidth.
        vec2 offset = jitter[i] * filterWidth;
        sum += shadowCompare(index, Pshadow + vec4(offset, 0, 0));
    } 
    return sum / float(PCF_NUM_SAMPLES);
} 

#endif // NUM_SHADOWS > 0

-- glsl PCF.ShadowFilterFragmentOnly
// ---------------------------------------------------------------------------
//  PCF shadow filtering with compute filter length on the fly.
//  no vertex shader needed.
// ---------------------------------------------------------------------------

#if NUM_SHADOWS > 0

#define SHADOW_FILTER
#define JITTER_NUM_SAMPLES 16
#define PCF_NUM_SAMPLES JITTER_NUM_SAMPLES 

FORWARD_DECL(float shadowSample(int index, vec4 coord));
FORWARD_DECL(float shadowCompare(int index, vec4 coord));

vec2 jitter[JITTER_NUM_SAMPLES] = {
    vec2(-0.94201624, -0.39906216),
    vec2( 0.94558609, -0.76890725),
    vec2(-0.09418410, -0.92938870),
    vec2( 0.34495938,  0.29387760),
    vec2(-0.91588581,  0.45771432),
    vec2(-0.81544232, -0.87912464),
    vec2(-0.38277543,  0.27676845),
    vec2( 0.97484398,  0.75648379),
    vec2( 0.44323325, -0.97511554),
    vec2( 0.53742981, -0.47373420),
    vec2(-0.26496911, -0.41893023),
    vec2( 0.79197514,  0.19090188),
    vec2(-0.24188840,  0.99706507),
    vec2(-0.81409955,  0.91437590),
    vec2( 0.19984126,  0.78641367),
    vec2( 0.14383161, -0.14100790)
};

// defined in simpleLighting.glslfx
FORWARD_DECL(vec2 computeShadowFilterWidth(int index, vec4 Peye));

float
shadowFilter(int index, vec4 Pshadow, vec4 Peye)
{
    vec2 filterWidth = computeShadowFilterWidth(index, Peye);

    float sum = 0.0;
    for (int i=0; i<PCF_NUM_SAMPLES; ++i) { 
        // Note that the perspective division has not been applied to
        // either Pshadow or filterWidth.
        vec2 offset = jitter[i] * filterWidth;
        sum += shadowCompare(index, Pshadow + vec4(offset, 0, 0));
    } 
    return sum / float(PCF_NUM_SAMPLES);
}


#endif // NUM_SHADOWS > 0
