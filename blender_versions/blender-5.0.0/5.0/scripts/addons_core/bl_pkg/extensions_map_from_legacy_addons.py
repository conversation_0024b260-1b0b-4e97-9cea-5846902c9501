# SPDX-FileCopyrightText: 2024 Blender Foundation
#
# SPDX-License-Identifier: GPL-2.0-or-later

# This is a data file that is evaluated directly (not imported).
# NOTE: this can be removed once upgrading from 4.1 is no longer relevant.
# pylint: disable-next=pointless-statement
{
    "remote_url": "https://extensions.blender.org/api/v1/extensions",
    "extensions": {
        # The first value is the extension ID, the second is it's name.
        "add_camera_rigs": ("add_camera_rigs", "Add Camera Rigs"),
        "add_curve_extra_objects": ("extra_curve_objectes", "Extra Curve Objectes"),
        "add_curve_ivygen": ("ivygen", "IvyGen"),
        "add_curve_sapling": ("sapling_tree_gen", "Sapling Tree Gen"),
        "add_mesh_BoltFactory": ("boltfactory", "BoltFactory"),
        "add_mesh_discombobulator": ("discombobulator", "Discombobulator"),
        "add_mesh_extra_objects": ("extra_mesh_objects", "Extra Mesh Objects"),
        "add_mesh_geodesic_domes": ("geodesic_domes", "Geodesic Domes"),
        "amaranth": ("amaranth", "<PERSON>anth Toolset"),
        "animation_add_corrective_shape_key": ("corrective_shape_keys", "Corrective Shape Keys"),
        "animation_animall": ("animall", "AnimAll"),
        "ant_landscape": ("antlandscape", "A.N.T.Landscape"),
        "archimesh": ("archimesh", "Archimesh"),
        "blender_id": ("blender_id_authentication", "Blender ID authentication"),
        "btrace": ("btracer", "BTracer"),
        "camera_turnaround": ("turnaround_camera", "Turnaround Camera"),
        "curve_assign_shapekey": ("assign_shape_keys", "Assign Shape Keys"),
        "curve_simplify": ("simplify_curves_plus", "Simplify Curves+"),
        "curve_tools": ("curve_tools", "Curve Tools"),
        "development_edit_operator": ("edit_operator_source", "Edit Operator Source"),
        "development_icon_get": ("icon_viewer", "Icon Viewer"),
        "development_iskeyfree": ("is_key_free", "Is key Free"),
        "greasepencil_tools": ("grease_pencil_tools", "Grease Pencil Tools"),
        "io_anim_camera": ("export_camera_animation", "Export Camera Animation"),
        "io_anim_nuke_chan": ("nuke_animation_format_chan", "Nuke Animation Format (.chan)"),
        "io_export_dxf": ("export_autocad_dxf_format_dxf", "Export Autocad DXF Format (.dxf)"),
        "io_export_paper_model": ("export_paper_model", "Export Paper Model"),
        "io_export_pc2": ("export_pointcache_formatpc2", "Export Pointcache Format(.pc2)"),
        "io_import_BrushSet": ("import_brushset", "Import BrushSet"),
        "io_import_dxf": ("import_autocad_dxf_format_dxf", "Import AutoCAD DXF Format (.dxf)"),
        "io_import_palette": ("import_palettes", "Import Palettes"),
        "io_mesh_atomic": ("atomic_blender_pdb_xyz", "Atomic Blender PDB/XYZ"),
        "io_scene_3ds": ("autodesk_3ds_format", "Autodesk 3DS format"),
        "io_shape_mdd": ("newtek_mdd_format", "NewTek MDD format"),
        "lighting_dynamic_sky": ("dynamic_sky", "Dynamic Sky"),
        "lighting_tri_lights": ("tri_lighting", "Tri-lighting"),
        "magic_uv": ("magic_uv", "Magic UV"),
        "materials_library_vx": ("material_library", "Material Library"),
        "materials_utils": ("material_utilities", "Material Utilities"),
        "measureit": ("measureit", "MeasureIt"),
        "mesh_auto_mirror": ("auto_mirror", "Auto Mirror"),
        "mesh_bsurfaces": ("bsurfaces_gpl_edition", "Bsurfaces GPL Edition"),
        "mesh_f2": ("f2", "F2"),
        "mesh_inset": ("inset_straight_skeleton", "Inset Straight Skeleton"),
        "mesh_looptools": ("looptools", "LoopTools"),
        "mesh_snap_utilities_line": ("snap_utilities_line", "Snap_Utilities_Line"),
        "mesh_tiny_cad": ("tinycad_mesh_tools", "tinyCAD Mesh tools"),
        "mesh_tissue": ("tissue", "Tissue"),
        "mesh_tools": ("edit_mesh_tools", "Edit Mesh Tools"),
        "node_arrange": ("node_arrange", "Node Arrange"),
        "node_presets": ("node_presets", "Node Presets"),
        "object_boolean_tools": ("bool_tool", "Bool Tool"),
        "object_carver": ("carver", "Carver"),
        "object_collection_manager": ("collection_manager", "Collection Manager"),
        "object_color_rules": ("object_color_rules", "Object Color Rules"),
        "object_edit_linked": ("edit_linked_library", "Edit Linked Library"),
        "object_fracture_cell": ("cell_fracture", "Cell Fracture"),
        "object_print3d_utils": ("print3d_toolbox", "3D-Print Toolbox"),
        "object_scatter": ("scatter_objects", "Scatter Objects"),
        "object_skinify": ("skinify_rig", "Skinify Rig"),
        "paint_palette": ("paint_palettes", "Paint Palettes"),
        "power_sequencer": ("power_sequencer", "Power Sequencer"),
        "precision_drawing_tools": ("precision_drawing_tools_pdt", "Precision Drawing Tools (PDT)"),
        "real_snow": ("real_snow", "Real Snow"),
        "render_copy_settings": ("copy_render_settings", "Copy Render Settings"),
        "render_freestyle_svg": ("freestyle_svg_exporter", "Freestyle SVG Exporter"),
        "render_povray": ("pov_at_ble", "POV@Ble"),
        "render_ui_animation_render": ("ui_animation_render", "UI Animation Render"),
        "space_clip_editor_refine_solution": ("refine_tracking_solution", "Refine tracking solution"),
        "space_view3d_3d_navigation": ("navigation", "3D Navigation"),
        "space_view3d_align_tools": ("align_tools", "Align Tools"),
        "space_view3d_brush_menus": ("dynamic_brush_menus", "Dynamic Brush Menus"),
        "space_view3d_copy_attributes": ("copy_attributes_menu", "Copy Attributes Menu"),
        "space_view3d_math_vis": ("math_vis_console", "Math Vis (Console)"),
        "space_view3d_modifier_tools": ("modifier_tools", "Modifier Tools"),
        "space_view3d_pie_menus": ("viewport_pie_menus", "3D Viewport Pie Menus"),
        "space_view3d_spacebar_menu": ("dynamic_context_menu", "Dynamic Context Menu"),
        "space_view3d_stored_views": ("stored_views", "Stored Views"),
        "storypencil": ("storypencil_storyboard_tools", "Storypencil - Storyboard Tools"),
        "sun_position": ("sun_position", "Sun Position"),
        "system_blend_info": ("scene_information", "Scene Information"),
        "system_demo_mode": ("demo_mode", "Demo Mode"),
        "system_property_chart": ("property_chart", "Property Chart"),
        "vdm_brush_baker": ("vdm_brush_baker", "VDM Brush Baker"),
    },
}
