OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_map_range.oso
shader node_map_range
param	string	range_type	"linear"		%read{3,22} %write{2147483647,-1}
param	float	Value	1		%read{2,65} %write{2147483647,-1}
param	float	FromMin	0		%read{0,66} %write{2147483647,-1}
param	float	FromMax	1		%read{0,66} %write{2147483647,-1}
param	float	ToMin	0		%read{68,70} %write{2147483647,-1}
param	float	ToMax	1		%read{68,68} %write{2147483647,-1}
param	float	Steps	4		%read{8,13} %write{2147483647,-1}
oparam	float	Result	0		%read{2147483647,-1} %write{70,70}
local	float	___369_t	%read{38,62} %write{37,57}
local	float	___370_Factor	%read{11,69} %write{2,67}
temp	int	$tmp1	%read{1,1} %write{0,0}
const	string	$const1	"stepped"		%read{3,3} %write{2147483647,-1}
temp	int	$tmp2	%read{4,4} %write{3,3}
temp	float	$tmp3	%read{7,7} %write{5,5}
temp	float	$tmp4	%read{7,7} %write{6,6}
const	int	$const2	0		%read{8,8} %write{2147483647,-1}
temp	int	$tmp5	%read{9,9} %write{8,8}
temp	float	$tmp6	%read{13,13} %write{12,12}
const	float	$const3	1		%read{10,56} %write{2147483647,-1}
temp	float	$tmp7	%read{11,11} %write{10,10}
temp	float	$tmp8	%read{12,12} %write{11,11}
const	float	$const4	0		%read{14,57} %write{2147483647,-1}
const	string	$const5	"smoothstep"		%read{15,15} %write{2147483647,-1}
temp	int	$tmp9	%read{16,16} %write{15,15}
temp	int	$tmp10	%read{18,18} %write{17,17}
temp	float	$tmp11	%read{20,20} %write{19,19}
const	string	$const6	"smootherstep"		%read{22,46} %write{2147483647,-1}
temp	int	$tmp12	%read{23,23} %write{22,22}
temp	int	$tmp13	%read{25,25} %write{24,24}
temp	float	$tmp14	%read{45,45} %write{44,44}
temp	float	$tmp15	%read{36,36} %write{32,33}
temp	float	$tmp16	%read{32,32} %write{27,27}
temp	float	$tmp17	%read{30,32} %write{28,28}
const	string	$const7	"safe_divide"		%read{29,49} %write{2147483647,-1}
temp	int	$tmp18	%read{31,31} %write{30,30}
const	string	$const8	"clamp"		%read{35,55} %write{2147483647,-1}
temp	float	$tmp19	%read{37,37} %write{36,36}
temp	float	$tmp20	%read{39,39} %write{38,38}
temp	float	$tmp21	%read{44,44} %write{39,39}
const	float	$const9	6		%read{40,60} %write{2147483647,-1}
temp	float	$tmp22	%read{41,41} %write{40,40}
const	float	$const10	15		%read{41,61} %write{2147483647,-1}
temp	float	$tmp23	%read{42,42} %write{41,41}
temp	float	$tmp24	%read{43,43} %write{42,42}
const	float	$const11	10		%read{43,63} %write{2147483647,-1}
temp	float	$tmp25	%read{44,44} %write{43,43}
temp	float	$tmp26	%read{56,56} %write{52,53}
temp	float	$tmp27	%read{52,52} %write{47,47}
temp	float	$tmp28	%read{50,52} %write{48,48}
temp	int	$tmp29	%read{51,51} %write{50,50}
temp	float	$tmp30	%read{57,57} %write{56,56}
temp	float	$tmp31	%read{59,59} %write{58,58}
temp	float	$tmp32	%read{64,64} %write{59,59}
temp	float	$tmp33	%read{61,61} %write{60,60}
temp	float	$tmp34	%read{62,62} %write{61,61}
temp	float	$tmp35	%read{63,63} %write{62,62}
temp	float	$tmp36	%read{64,64} %write{63,63}
temp	float	$tmp37	%read{67,67} %write{65,65}
temp	float	$tmp38	%read{67,67} %write{66,66}
temp	float	$tmp39	%read{69,69} %write{68,68}
temp	float	$tmp40	%read{70,70} %write{69,69}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:27
#   if (FromMax != FromMin) {
	neq		$tmp1 FromMax FromMin 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl"} %line{27} %argrw{"wrr"}
	if		$tmp1 71 71 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:28
#     float Factor = Value;
	assign		___370_Factor Value 	%line{28} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:29
#     if (range_type == "stepped") {
	eq		$tmp2 range_type $const1 	%line{29} %argrw{"wrr"}
	if		$tmp2 15 68 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:30
#       Factor = (Value - FromMin) / (FromMax - FromMin);
	sub		$tmp3 Value FromMin 	%line{30} %argrw{"wrr"}
	sub		$tmp4 FromMax FromMin 	%argrw{"wrr"}
	div		___370_Factor $tmp3 $tmp4 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:31
#       Factor = (Steps > 0) ? floor(Factor * (Steps + 1.0)) / Steps : 0.0;
	gt		$tmp5 Steps $const2 	%line{31} %argrw{"wrr"}
	if		$tmp5 14 15 	%argrw{"r"}
	add		$tmp7 Steps $const3 	%argrw{"wrr"}
	mul		$tmp8 ___370_Factor $tmp7 	%argrw{"wrr"}
	floor		$tmp6 $tmp8 	%argrw{"wr"}
	div		___370_Factor $tmp6 Steps 	%argrw{"wrr"}
	assign		___370_Factor $const4 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:33
#     else if (range_type == "smoothstep") {
	eq		$tmp9 range_type $const5 	%line{33} %argrw{"wrr"}
	if		$tmp9 22 68 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:34
#       Factor = (FromMin > FromMax) ? 1.0 - smoothstep(FromMax, FromMin, Value) :
	gt		$tmp10 FromMin FromMax 	%line{34} %argrw{"wrr"}
	if		$tmp10 21 22 	%argrw{"r"}
	smoothstep	$tmp11 FromMax FromMin Value 	%argrw{"wrrr"}
	sub		___370_Factor $const3 $tmp11 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:35
#                                      smoothstep(FromMin, FromMax, Value);
	smoothstep	___370_Factor FromMin FromMax Value 	%line{35} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:37
#     else if (range_type == "smootherstep") {
	eq		$tmp12 range_type $const6 	%line{37} %argrw{"wrr"}
	if		$tmp12 65 68 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:38
#       Factor = (FromMin > FromMax) ? 1.0 - smootherstep(FromMax, FromMin, Value) :
	gt		$tmp13 FromMin FromMax 	%line{38} %argrw{"wrr"}
	if		$tmp13 46 65 	%argrw{"r"}
	functioncall	$const6 45 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:14
#   float t = clamp(safe_divide((x - edge0), (edge1 - edge0)), 0.0, 1.0);
	sub		$tmp16 Value FromMax 	%line{14} %argrw{"wrr"}
	sub		$tmp17 FromMin FromMax 	%argrw{"wrr"}
	functioncall	$const7 35 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:9
#   return (b != 0.0) ? a / b : 0.0;
	neq		$tmp18 $tmp17 $const4 	%line{9} %argrw{"wrr"}
	if		$tmp18 33 34 	%argrw{"r"}
	div		$tmp15 $tmp16 $tmp17 	%argrw{"wrr"}
	assign		$tmp15 $const4 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:14
#   float t = clamp(safe_divide((x - edge0), (edge1 - edge0)), 0.0, 1.0);
	functioncall	$const8 38 	%line{14} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp19 $tmp15 $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		___369_t $tmp19 $const4 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:15
#   return t * t * t * (t * (t * 6.0 - 15.0) + 10.0);
	mul		$tmp20 ___369_t ___369_t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl"} %line{15} %argrw{"wrr"}
	mul		$tmp21 $tmp20 ___369_t 	%argrw{"wrr"}
	mul		$tmp22 ___369_t $const9 	%argrw{"wrr"}
	sub		$tmp23 $tmp22 $const10 	%argrw{"wrr"}
	mul		$tmp24 ___369_t $tmp23 	%argrw{"wrr"}
	add		$tmp25 $tmp24 $const11 	%argrw{"wrr"}
	mul		$tmp14 $tmp21 $tmp25 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:38
#       Factor = (FromMin > FromMax) ? 1.0 - smootherstep(FromMax, FromMin, Value) :
	sub		___370_Factor $const3 $tmp14 	%line{38} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:39
#                                      smootherstep(FromMin, FromMax, Value);
	functioncall	$const6 65 	%line{39} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:14
#   float t = clamp(safe_divide((x - edge0), (edge1 - edge0)), 0.0, 1.0);
	sub		$tmp27 Value FromMin 	%line{14} %argrw{"wrr"}
	sub		$tmp28 FromMax FromMin 	%argrw{"wrr"}
	functioncall	$const7 55 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:9
#   return (b != 0.0) ? a / b : 0.0;
	neq		$tmp29 $tmp28 $const4 	%line{9} %argrw{"wrr"}
	if		$tmp29 53 54 	%argrw{"r"}
	div		$tmp26 $tmp27 $tmp28 	%argrw{"wrr"}
	assign		$tmp26 $const4 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:14
#   float t = clamp(safe_divide((x - edge0), (edge1 - edge0)), 0.0, 1.0);
	functioncall	$const8 58 	%line{14} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp30 $tmp26 $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		___369_t $tmp30 $const4 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:15
#   return t * t * t * (t * (t * 6.0 - 15.0) + 10.0);
	mul		$tmp31 ___369_t ___369_t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl"} %line{15} %argrw{"wrr"}
	mul		$tmp32 $tmp31 ___369_t 	%argrw{"wrr"}
	mul		$tmp33 ___369_t $const9 	%argrw{"wrr"}
	sub		$tmp34 $tmp33 $const10 	%argrw{"wrr"}
	mul		$tmp35 ___369_t $tmp34 	%argrw{"wrr"}
	add		$tmp36 $tmp35 $const11 	%argrw{"wrr"}
	mul		___370_Factor $tmp32 $tmp36 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:42
#       Factor = (Value - FromMin) / (FromMax - FromMin);
	sub		$tmp37 Value FromMin 	%line{42} %argrw{"wrr"}
	sub		$tmp38 FromMax FromMin 	%argrw{"wrr"}
	div		___370_Factor $tmp37 $tmp38 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_map_range.osl:44
#     Result = ToMin + Factor * (ToMax - ToMin);
	sub		$tmp39 ToMax ToMin 	%line{44} %argrw{"wrr"}
	mul		$tmp40 ___370_Factor $tmp39 	%argrw{"wrr"}
	add		Result ToMin $tmp40 	%argrw{"wrr"}
	end
