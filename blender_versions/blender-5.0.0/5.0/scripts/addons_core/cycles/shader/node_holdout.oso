OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_holdout.oso
shader node_holdout
oparam	closure color	Holdout			%read{2147483647,-1} %write{0,0} %initexpr
const	string	$const1	"holdout"		%read{0,0} %write{2147483647,-1}
code Holdout
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_holdout.osl:7
# shader node_holdout(output closure color Holdout = holdout()) {}
	closure		Holdout $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_holdout.osl"} %line{7} %argrw{"wr"}
code ___main___
	end
