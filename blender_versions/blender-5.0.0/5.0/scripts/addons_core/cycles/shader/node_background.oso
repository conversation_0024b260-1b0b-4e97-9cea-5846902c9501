OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_background.oso
shader node_background
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	float	Strength	1		%read{1,1} %write{2147483647,-1}
oparam	closure color	Background			%read{2147483647,-1} %write{2,2}
temp	closure color	$tmp1	%read{2,2} %write{0,0}
const	string	$const1	"background"		%read{0,0} %write{2147483647,-1}
temp	color	$tmp2	%read{2,2} %write{1,1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_background.osl:11
#   Background = Color * Strength * background();
	closure		$tmp1 $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_background.osl"} %line{11} %argrw{"wr"}
	mul		$tmp2 Color Strength 	%argrw{"wrr"}
	mul		Background $tmp1 $tmp2 	%argrw{"wrr"}
	end
