OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_mix_color.oso
shader node_mix_color
param	string	blend_type	"mix"		%read{5,866} %write{2147483647,-1}
param	int	use_clamp	0		%read{0,0} %write{2147483647,-1}
param	int	use_clamp_result	0		%read{922,922} %write{2147483647,-1}
param	float	Factor	0.5		%read{2,4} %write{2147483647,-1}
param	color	A	0 0 0		%read{7,914} %write{2147483647,-1}
param	color	B	0 0 0		%read{7,915} %write{2147483647,-1}
oparam	color	Result	0 0 0		%read{926,926} %write{7,927}
local	float	___361_cmax	%read{287,788} %write{281,762}
local	float	___361_cmin	%read{287,768} %write{286,767}
local	float	___361_h	%read{317,802} %write{293,801}
local	float	___361_s	%read{294,802} %write{291,773}
local	float	___361_v	%read{321,802} %write{288,769}
local	float	___361_cdelta	%read{291,780} %write{287,768}
local	color	___361_c	%read{303,796} %write{299,780}
local	float	___370_i	%read{385,842} %write{384,818}
local	float	___370_f	%read{386,826} %write{385,819}
local	float	___370_p	%read{398,845} %write{388,822}
local	float	___370_q	%read{401,845} %write{391,825}
local	float	___370_t	%read{398,844} %write{395,829}
local	float	___370_h	%read{380,819} %write{374,817}
local	float	___370_s	%read{377,827} %write{375,809}
local	float	___370_v	%read{379,845} %write{376,810}
local	color	___370_rgb	%read{412,846} %write{379,845}
local	float	___390_tm	%read{22,22} %write{21,21}
local	float	___391_tm	%read{41,88} %write{32,32}
local	color	___391_outcol	%read{34,94} %write{33,93}
local	float	___399_tm	%read{109,131} %write{103,103}
local	color	___399_outcol	%read{108,138} %write{104,137}
local	color	___407_outcol	%read{169,214} %write{168,213}
local	float	___408_tmp	%read{175,183} %write{174,179}
local	float	___412_tmp	%read{190,198} %write{189,194}
local	float	___416_tmp	%read{205,213} %write{204,209}
local	float	___420_tmp	%read{223,270} %write{222,263}
local	float	___420_tm	%read{222,256} %write{218,218}
local	color	___420_outcol	%read{226,271} %write{219,270}
local	color	___433_outcol	%read{326,414} %write{275,413}
local	color	___433_hsv2	%read{322,371} %write{321,321}
local	color	___434_hsv	%read{374,376} %write{370,372}
local	color	___434_tmp	%read{413,413} %write{412,412}
local	float	___435_tm	%read{516,516} %write{418,418}
local	color	___435_outcol	%read{421,561} %write{419,560}
local	color	___435_hsv	%read{466,524} %write{465,520}
local	color	___436_hsv2	%read{517,517} %write{514,514}
local	float	___437_tm	%read{659,659} %write{565,565}
local	color	___437_hsv	%read{658,667} %write{611,663}
local	color	___437_hsv2	%read{660,660} %write{657,657}
local	color	___438_outcol	%read{758,848} %write{707,847}
local	color	___438_hsv2	%read{754,805} %write{753,753}
local	color	___439_hsv	%read{808,810} %write{802,806}
local	color	___439_tmp	%read{847,847} %write{846,846}
local	float	___440_tm	%read{858,858} %write{852,852}
local	color	___440_one	%read{854,859} %write{853,853}
local	color	___440_scr	%read{862,862} %write{857,857}
local	color	___441_outcol	%read{921,921} %write{869,920}
local	float	t	%read{7,918} %write{3,4}
const	float	$const1	0		%read{3,923} %write{2147483647,-1}
const	float	$const2	1		%read{2,924} %write{2147483647,-1}
const	string	$const3	"clamp"		%read{1,925} %write{2147483647,-1}
temp	float	$tmp1	%read{3,3} %write{2,2}
const	string	$const4	"mix"		%read{5,5} %write{2147483647,-1}
temp	int	$tmp2	%read{6,6} %write{5,5}
const	string	$const5	"add"		%read{8,8} %write{2147483647,-1}
temp	int	$tmp3	%read{9,9} %write{8,8}
const	string	$const6	"node_mix_add"		%read{10,10} %write{2147483647,-1}
temp	color	$tmp4	%read{12,12} %write{11,11}
const	string	$const7	"multiply"		%read{13,13} %write{2147483647,-1}
temp	int	$tmp5	%read{14,14} %write{13,13}
const	string	$const8	"node_mix_mul"		%read{15,15} %write{2147483647,-1}
temp	color	$tmp6	%read{17,17} %write{16,16}
const	string	$const9	"screen"		%read{18,18} %write{2147483647,-1}
temp	int	$tmp7	%read{19,19} %write{18,18}
const	string	$const10	"node_mix_screen"		%read{20,20} %write{2147483647,-1}
const	color	$const11	1 1 1		%read{23,853} %write{2147483647,-1}
temp	color	$tmp9	%read{25,25} %write{22,22}
temp	color	$tmp11	%read{24,24} %write{23,23}
temp	color	$tmp12	%read{25,25} %write{24,24}
temp	color	$tmp13	%read{27,27} %write{25,25}
temp	color	$tmp15	%read{27,27} %write{26,26}
temp	color	$tmp16	%read{28,28} %write{27,27}
const	string	$const12	"overlay"		%read{29,29} %write{2147483647,-1}
temp	int	$tmp17	%read{30,30} %write{29,29}
const	string	$const13	"node_mix_overlay"		%read{31,31} %write{2147483647,-1}
const	int	$const14	0		%read{34,886} %write{2147483647,-1}
temp	float	$tmp18	%read{35,35} %write{34,34}
const	float	$const15	0.5		%read{35,909} %write{2147483647,-1}
temp	int	$tmp19	%read{36,36} %write{35,35}
temp	float	$tmp20	%read{42,42} %write{37,37}
const	float	$const16	2		%read{38,916} %write{2147483647,-1}
temp	float	$tmp21	%read{40,40} %write{38,38}
temp	float	$tmp22	%read{40,40} %write{39,39}
temp	float	$tmp23	%read{41,41} %write{40,40}
temp	float	$tmp24	%read{42,42} %write{41,41}
temp	float	$tmp25	%read{43,43} %write{42,42}
temp	float	$tmp26	%read{47,47} %write{44,44}
temp	float	$tmp27	%read{46,46} %write{45,45}
temp	float	$tmp28	%read{47,47} %write{46,46}
temp	float	$tmp29	%read{48,48} %write{47,47}
temp	float	$tmp30	%read{51,51} %write{48,48}
temp	float	$tmp31	%read{50,50} %write{49,49}
temp	float	$tmp32	%read{51,51} %write{50,50}
temp	float	$tmp33	%read{52,52} %write{51,51}
temp	float	$tmp34	%read{53,53} %write{52,52}
const	int	$const17	1		%read{54,903} %write{2147483647,-1}
temp	float	$tmp35	%read{55,55} %write{54,54}
temp	int	$tmp36	%read{56,56} %write{55,55}
temp	float	$tmp37	%read{62,62} %write{57,57}
temp	float	$tmp38	%read{60,60} %write{58,58}
temp	float	$tmp39	%read{60,60} %write{59,59}
temp	float	$tmp40	%read{61,61} %write{60,60}
temp	float	$tmp41	%read{62,62} %write{61,61}
temp	float	$tmp42	%read{63,63} %write{62,62}
temp	float	$tmp43	%read{67,67} %write{64,64}
temp	float	$tmp44	%read{66,66} %write{65,65}
temp	float	$tmp45	%read{67,67} %write{66,66}
temp	float	$tmp46	%read{68,68} %write{67,67}
temp	float	$tmp47	%read{71,71} %write{68,68}
temp	float	$tmp48	%read{70,70} %write{69,69}
temp	float	$tmp49	%read{71,71} %write{70,70}
temp	float	$tmp50	%read{72,72} %write{71,71}
temp	float	$tmp51	%read{73,73} %write{72,72}
const	int	$const18	2		%read{74,920} %write{2147483647,-1}
temp	float	$tmp52	%read{75,75} %write{74,74}
temp	int	$tmp53	%read{76,76} %write{75,75}
temp	float	$tmp54	%read{82,82} %write{77,77}
temp	float	$tmp55	%read{80,80} %write{78,78}
temp	float	$tmp56	%read{80,80} %write{79,79}
temp	float	$tmp57	%read{81,81} %write{80,80}
temp	float	$tmp58	%read{82,82} %write{81,81}
temp	float	$tmp59	%read{83,83} %write{82,82}
temp	float	$tmp60	%read{87,87} %write{84,84}
temp	float	$tmp61	%read{86,86} %write{85,85}
temp	float	$tmp62	%read{87,87} %write{86,86}
temp	float	$tmp63	%read{88,88} %write{87,87}
temp	float	$tmp64	%read{91,91} %write{88,88}
temp	float	$tmp65	%read{90,90} %write{89,89}
temp	float	$tmp66	%read{91,91} %write{90,90}
temp	float	$tmp67	%read{92,92} %write{91,91}
temp	float	$tmp68	%read{93,93} %write{92,92}
const	string	$const19	"subtract"		%read{95,95} %write{2147483647,-1}
temp	int	$tmp69	%read{96,96} %write{95,95}
const	string	$const20	"node_mix_sub"		%read{97,97} %write{2147483647,-1}
temp	color	$tmp70	%read{99,99} %write{98,98}
const	string	$const21	"divide"		%read{100,100} %write{2147483647,-1}
temp	int	$tmp71	%read{101,101} %write{100,100}
const	string	$const22	"node_mix_div"		%read{102,102} %write{2147483647,-1}
temp	float	$tmp72	%read{106,106} %write{105,105}
temp	int	$tmp73	%read{107,107} %write{106,106}
temp	float	$tmp74	%read{109,109} %write{108,108}
temp	float	$tmp75	%read{114,114} %write{109,109}
temp	float	$tmp76	%read{111,111} %write{110,110}
temp	float	$tmp77	%read{113,113} %write{111,111}
temp	float	$tmp78	%read{113,113} %write{112,112}
temp	float	$tmp79	%read{114,114} %write{113,113}
temp	float	$tmp80	%read{115,115} %write{114,114}
temp	float	$tmp81	%read{117,117} %write{116,116}
temp	int	$tmp82	%read{118,118} %write{117,117}
temp	float	$tmp83	%read{120,120} %write{119,119}
temp	float	$tmp84	%read{125,125} %write{120,120}
temp	float	$tmp85	%read{122,122} %write{121,121}
temp	float	$tmp86	%read{124,124} %write{122,122}
temp	float	$tmp87	%read{124,124} %write{123,123}
temp	float	$tmp88	%read{125,125} %write{124,124}
temp	float	$tmp89	%read{126,126} %write{125,125}
temp	float	$tmp90	%read{128,128} %write{127,127}
temp	int	$tmp91	%read{129,129} %write{128,128}
temp	float	$tmp92	%read{131,131} %write{130,130}
temp	float	$tmp93	%read{136,136} %write{131,131}
temp	float	$tmp94	%read{133,133} %write{132,132}
temp	float	$tmp95	%read{135,135} %write{133,133}
temp	float	$tmp96	%read{135,135} %write{134,134}
temp	float	$tmp97	%read{136,136} %write{135,135}
temp	float	$tmp98	%read{137,137} %write{136,136}
const	string	$const23	"difference"		%read{139,139} %write{2147483647,-1}
temp	int	$tmp99	%read{140,140} %write{139,139}
const	string	$const24	"node_mix_diff"		%read{141,141} %write{2147483647,-1}
temp	color	$tmp100	%read{144,144} %write{143,143}
temp	color	$tmp101	%read{143,143} %write{142,142}
const	string	$const25	"exclusion"		%read{145,145} %write{2147483647,-1}
temp	int	$tmp102	%read{146,146} %write{145,145}
const	string	$const26	"node_mix_exclusion"		%read{147,147} %write{2147483647,-1}
temp	color	$tmp103	%read{154,154} %write{152,152}
temp	color	$tmp104	%read{151,151} %write{148,148}
temp	color	$tmp105	%read{150,150} %write{149,149}
temp	color	$tmp106	%read{151,151} %write{150,150}
temp	color	$tmp107	%read{152,152} %write{151,151}
temp	color	$tmp108	%read{154,154} %write{153,153}
const	string	$const27	"darken"		%read{155,155} %write{2147483647,-1}
temp	int	$tmp109	%read{156,156} %write{155,155}
const	string	$const28	"node_mix_dark"		%read{157,157} %write{2147483647,-1}
temp	color	$tmp110	%read{159,159} %write{158,158}
const	string	$const29	"lighten"		%read{160,160} %write{2147483647,-1}
temp	int	$tmp111	%read{161,161} %write{160,160}
const	string	$const30	"node_mix_light"		%read{162,162} %write{2147483647,-1}
temp	color	$tmp112	%read{164,164} %write{163,163}
const	string	$const31	"dodge"		%read{165,165} %write{2147483647,-1}
temp	int	$tmp113	%read{166,166} %write{165,165}
const	string	$const32	"node_mix_dodge"		%read{167,167} %write{2147483647,-1}
temp	float	$tmp114	%read{170,170} %write{169,169}
temp	int	$tmp115	%read{171,171} %write{170,170}
temp	float	$tmp116	%read{173,173} %write{172,172}
temp	float	$tmp117	%read{174,174} %write{173,173}
temp	int	$tmp118	%read{176,176} %write{175,175}
temp	float	$tmp119	%read{179,179} %write{178,178}
temp	int	$tmp120	%read{181,181} %write{180,180}
temp	float	$tmp121	%read{185,185} %write{184,184}
temp	int	$tmp122	%read{186,186} %write{185,185}
temp	float	$tmp123	%read{188,188} %write{187,187}
temp	float	$tmp124	%read{189,189} %write{188,188}
temp	int	$tmp125	%read{191,191} %write{190,190}
temp	float	$tmp126	%read{194,194} %write{193,193}
temp	int	$tmp127	%read{196,196} %write{195,195}
temp	float	$tmp128	%read{200,200} %write{199,199}
temp	int	$tmp129	%read{201,201} %write{200,200}
temp	float	$tmp130	%read{203,203} %write{202,202}
temp	float	$tmp131	%read{204,204} %write{203,203}
temp	int	$tmp132	%read{206,206} %write{205,205}
temp	float	$tmp133	%read{209,209} %write{208,208}
temp	int	$tmp134	%read{211,211} %write{210,210}
const	string	$const33	"burn"		%read{215,215} %write{2147483647,-1}
temp	int	$tmp135	%read{216,216} %write{215,215}
const	string	$const34	"node_mix_burn"		%read{217,217} %write{2147483647,-1}
temp	float	$tmp136	%read{221,221} %write{220,220}
temp	float	$tmp137	%read{222,222} %write{221,221}
temp	int	$tmp138	%read{224,224} %write{223,223}
temp	float	$tmp139	%read{227,227} %write{226,226}
temp	float	$tmp140	%read{228,228} %write{227,227}
temp	float	$tmp141	%read{229,229} %write{228,228}
temp	int	$tmp142	%read{231,231} %write{230,230}
temp	int	$tmp143	%read{234,234} %write{233,233}
temp	float	$tmp144	%read{238,238} %write{237,237}
temp	float	$tmp145	%read{239,239} %write{238,238}
temp	int	$tmp146	%read{241,241} %write{240,240}
temp	float	$tmp147	%read{244,244} %write{243,243}
temp	float	$tmp148	%read{245,245} %write{244,244}
temp	float	$tmp149	%read{246,246} %write{245,245}
temp	int	$tmp150	%read{248,248} %write{247,247}
temp	int	$tmp151	%read{251,251} %write{250,250}
temp	float	$tmp152	%read{255,255} %write{254,254}
temp	float	$tmp153	%read{256,256} %write{255,255}
temp	int	$tmp154	%read{258,258} %write{257,257}
temp	float	$tmp155	%read{261,261} %write{260,260}
temp	float	$tmp156	%read{262,262} %write{261,261}
temp	float	$tmp157	%read{263,263} %write{262,262}
temp	int	$tmp158	%read{265,265} %write{264,264}
temp	int	$tmp159	%read{268,268} %write{267,267}
const	string	$const35	"hue"		%read{272,272} %write{2147483647,-1}
temp	int	$tmp160	%read{273,273} %write{272,272}
const	string	$const36	"node_mix_hue"		%read{274,274} %write{2147483647,-1}
const	string	$const37	"rgb_to_hsv"		%read{276,757} %write{2147483647,-1}
temp	float	$tmp161	%read{281,281} %write{277,277}
temp	float	$tmp162	%read{281,281} %write{280,280}
temp	float	$tmp163	%read{280,280} %write{278,278}
temp	float	$tmp164	%read{280,280} %write{279,279}
temp	float	$tmp165	%read{286,286} %write{282,282}
temp	float	$tmp166	%read{286,286} %write{285,285}
temp	float	$tmp167	%read{285,285} %write{283,283}
temp	float	$tmp168	%read{285,285} %write{284,284}
temp	int	$tmp169	%read{290,290} %write{289,289}
temp	int	$tmp170	%read{295,295} %write{294,294}
temp	color	$tmp171	%read{298,298} %write{297,297}
temp	color	$tmp172	%read{299,299} %write{298,298}
temp	float	$tmp173	%read{301,301} %write{300,300}
temp	int	$tmp174	%read{302,302} %write{301,301}
temp	float	$tmp175	%read{305,305} %write{303,303}
temp	float	$tmp176	%read{305,305} %write{304,304}
temp	float	$tmp177	%read{307,307} %write{306,306}
temp	int	$tmp178	%read{308,308} %write{307,307}
temp	float	$tmp179	%read{310,310} %write{309,309}
temp	float	$tmp180	%read{312,312} %write{310,310}
temp	float	$tmp181	%read{312,312} %write{311,311}
const	float	$const38	4		%read{314,842} %write{2147483647,-1}
temp	float	$tmp182	%read{314,314} %write{313,313}
temp	float	$tmp183	%read{316,316} %write{314,314}
temp	float	$tmp184	%read{316,316} %write{315,315}
const	float	$const39	6		%read{317,817} %write{2147483647,-1}
temp	int	$tmp185	%read{319,319} %write{318,318}
temp	float	$tmp186	%read{323,323} %write{322,322}
temp	int	$tmp187	%read{324,324} %write{323,323}
temp	float	$tmp188	%read{330,330} %write{326,326}
temp	float	$tmp189	%read{330,330} %write{329,329}
temp	float	$tmp190	%read{329,329} %write{327,327}
temp	float	$tmp191	%read{329,329} %write{328,328}
temp	float	$tmp192	%read{335,335} %write{331,331}
temp	float	$tmp193	%read{335,335} %write{334,334}
temp	float	$tmp194	%read{334,334} %write{332,332}
temp	float	$tmp195	%read{334,334} %write{333,333}
temp	int	$tmp196	%read{339,339} %write{338,338}
temp	int	$tmp197	%read{344,344} %write{343,343}
temp	color	$tmp198	%read{347,347} %write{346,346}
temp	color	$tmp199	%read{348,348} %write{347,347}
temp	float	$tmp200	%read{350,350} %write{349,349}
temp	int	$tmp201	%read{351,351} %write{350,350}
temp	float	$tmp202	%read{354,354} %write{352,352}
temp	float	$tmp203	%read{354,354} %write{353,353}
temp	float	$tmp204	%read{356,356} %write{355,355}
temp	int	$tmp205	%read{357,357} %write{356,356}
temp	float	$tmp206	%read{359,359} %write{358,358}
temp	float	$tmp207	%read{361,361} %write{359,359}
temp	float	$tmp208	%read{361,361} %write{360,360}
temp	float	$tmp209	%read{363,363} %write{362,362}
temp	float	$tmp210	%read{365,365} %write{363,363}
temp	float	$tmp211	%read{365,365} %write{364,364}
temp	int	$tmp212	%read{368,368} %write{367,367}
temp	float	$tmp213	%read{372,372} %write{371,371}
const	string	$const40	"hsv_to_rgb"		%read{373,807} %write{2147483647,-1}
temp	int	$tmp214	%read{378,378} %write{377,377}
temp	int	$tmp215	%read{381,381} %write{380,380}
temp	float	$tmp216	%read{388,388} %write{387,387}
temp	float	$tmp217	%read{390,390} %write{389,389}
temp	float	$tmp218	%read{391,391} %write{390,390}
temp	float	$tmp219	%read{393,393} %write{392,392}
temp	float	$tmp220	%read{394,394} %write{393,393}
temp	float	$tmp221	%read{395,395} %write{394,394}
temp	int	$tmp222	%read{397,397} %write{396,396}
temp	int	$tmp223	%read{400,400} %write{399,399}
temp	int	$tmp224	%read{403,403} %write{402,402}
const	float	$const41	3		%read{405,839} %write{2147483647,-1}
temp	int	$tmp225	%read{406,406} %write{405,405}
temp	int	$tmp226	%read{409,409} %write{408,408}
const	string	$const42	"saturation"		%read{415,415} %write{2147483647,-1}
temp	int	$tmp227	%read{416,416} %write{415,415}
const	string	$const43	"node_mix_sat"		%read{417,417} %write{2147483647,-1}
temp	float	$tmp228	%read{425,425} %write{421,421}
temp	float	$tmp229	%read{425,425} %write{424,424}
temp	float	$tmp230	%read{424,424} %write{422,422}
temp	float	$tmp231	%read{424,424} %write{423,423}
temp	float	$tmp232	%read{430,430} %write{426,426}
temp	float	$tmp233	%read{430,430} %write{429,429}
temp	float	$tmp234	%read{429,429} %write{427,427}
temp	float	$tmp235	%read{429,429} %write{428,428}
temp	int	$tmp236	%read{434,434} %write{433,433}
temp	int	$tmp237	%read{439,439} %write{438,438}
temp	color	$tmp238	%read{442,442} %write{441,441}
temp	color	$tmp239	%read{443,443} %write{442,442}
temp	float	$tmp240	%read{445,445} %write{444,444}
temp	int	$tmp241	%read{446,446} %write{445,445}
temp	float	$tmp242	%read{449,449} %write{447,447}
temp	float	$tmp243	%read{449,449} %write{448,448}
temp	float	$tmp244	%read{451,451} %write{450,450}
temp	int	$tmp245	%read{452,452} %write{451,451}
temp	float	$tmp246	%read{454,454} %write{453,453}
temp	float	$tmp247	%read{456,456} %write{454,454}
temp	float	$tmp248	%read{456,456} %write{455,455}
temp	float	$tmp249	%read{458,458} %write{457,457}
temp	float	$tmp250	%read{460,460} %write{458,458}
temp	float	$tmp251	%read{460,460} %write{459,459}
temp	int	$tmp252	%read{463,463} %write{462,462}
temp	float	$tmp253	%read{467,467} %write{466,466}
temp	int	$tmp254	%read{468,468} %write{467,467}
temp	float	$tmp255	%read{474,474} %write{470,470}
temp	float	$tmp256	%read{474,474} %write{473,473}
temp	float	$tmp257	%read{473,473} %write{471,471}
temp	float	$tmp258	%read{473,473} %write{472,472}
temp	float	$tmp259	%read{479,479} %write{475,475}
temp	float	$tmp260	%read{479,479} %write{478,478}
temp	float	$tmp261	%read{478,478} %write{476,476}
temp	float	$tmp262	%read{478,478} %write{477,477}
temp	int	$tmp263	%read{483,483} %write{482,482}
temp	int	$tmp264	%read{488,488} %write{487,487}
temp	color	$tmp265	%read{491,491} %write{490,490}
temp	color	$tmp266	%read{492,492} %write{491,491}
temp	float	$tmp267	%read{494,494} %write{493,493}
temp	int	$tmp268	%read{495,495} %write{494,494}
temp	float	$tmp269	%read{498,498} %write{496,496}
temp	float	$tmp270	%read{498,498} %write{497,497}
temp	float	$tmp271	%read{500,500} %write{499,499}
temp	int	$tmp272	%read{501,501} %write{500,500}
temp	float	$tmp273	%read{503,503} %write{502,502}
temp	float	$tmp274	%read{505,505} %write{503,503}
temp	float	$tmp275	%read{505,505} %write{504,504}
temp	float	$tmp276	%read{507,507} %write{506,506}
temp	float	$tmp277	%read{509,509} %write{507,507}
temp	float	$tmp278	%read{509,509} %write{508,508}
temp	int	$tmp279	%read{512,512} %write{511,511}
temp	float	$tmp280	%read{516,516} %write{515,515}
temp	float	$tmp281	%read{519,519} %write{516,516}
temp	float	$tmp282	%read{518,518} %write{517,517}
temp	float	$tmp283	%read{519,519} %write{518,518}
temp	float	$tmp284	%read{520,520} %write{519,519}
temp	int	$tmp285	%read{526,526} %write{525,525}
temp	int	$tmp286	%read{529,529} %write{528,528}
temp	float	$tmp287	%read{536,536} %write{535,535}
temp	float	$tmp288	%read{538,538} %write{537,537}
temp	float	$tmp289	%read{539,539} %write{538,538}
temp	float	$tmp290	%read{541,541} %write{540,540}
temp	float	$tmp291	%read{542,542} %write{541,541}
temp	float	$tmp292	%read{543,543} %write{542,542}
temp	int	$tmp293	%read{545,545} %write{544,544}
temp	int	$tmp294	%read{548,548} %write{547,547}
temp	int	$tmp295	%read{551,551} %write{550,550}
temp	int	$tmp296	%read{554,554} %write{553,553}
temp	int	$tmp297	%read{557,557} %write{556,556}
const	string	$const44	"value"		%read{562,562} %write{2147483647,-1}
temp	int	$tmp298	%read{563,563} %write{562,562}
const	string	$const45	"node_mix_val"		%read{564,564} %write{2147483647,-1}
temp	float	$tmp299	%read{571,571} %write{567,567}
temp	float	$tmp300	%read{571,571} %write{570,570}
temp	float	$tmp301	%read{570,570} %write{568,568}
temp	float	$tmp302	%read{570,570} %write{569,569}
temp	float	$tmp303	%read{576,576} %write{572,572}
temp	float	$tmp304	%read{576,576} %write{575,575}
temp	float	$tmp305	%read{575,575} %write{573,573}
temp	float	$tmp306	%read{575,575} %write{574,574}
temp	int	$tmp307	%read{580,580} %write{579,579}
temp	int	$tmp308	%read{585,585} %write{584,584}
temp	color	$tmp309	%read{588,588} %write{587,587}
temp	color	$tmp310	%read{589,589} %write{588,588}
temp	float	$tmp311	%read{591,591} %write{590,590}
temp	int	$tmp312	%read{592,592} %write{591,591}
temp	float	$tmp313	%read{595,595} %write{593,593}
temp	float	$tmp314	%read{595,595} %write{594,594}
temp	float	$tmp315	%read{597,597} %write{596,596}
temp	int	$tmp316	%read{598,598} %write{597,597}
temp	float	$tmp317	%read{600,600} %write{599,599}
temp	float	$tmp318	%read{602,602} %write{600,600}
temp	float	$tmp319	%read{602,602} %write{601,601}
temp	float	$tmp320	%read{604,604} %write{603,603}
temp	float	$tmp321	%read{606,606} %write{604,604}
temp	float	$tmp322	%read{606,606} %write{605,605}
temp	int	$tmp323	%read{609,609} %write{608,608}
temp	float	$tmp324	%read{617,617} %write{613,613}
temp	float	$tmp325	%read{617,617} %write{616,616}
temp	float	$tmp326	%read{616,616} %write{614,614}
temp	float	$tmp327	%read{616,616} %write{615,615}
temp	float	$tmp328	%read{622,622} %write{618,618}
temp	float	$tmp329	%read{622,622} %write{621,621}
temp	float	$tmp330	%read{621,621} %write{619,619}
temp	float	$tmp331	%read{621,621} %write{620,620}
temp	int	$tmp332	%read{626,626} %write{625,625}
temp	int	$tmp333	%read{631,631} %write{630,630}
temp	color	$tmp334	%read{634,634} %write{633,633}
temp	color	$tmp335	%read{635,635} %write{634,634}
temp	float	$tmp336	%read{637,637} %write{636,636}
temp	int	$tmp337	%read{638,638} %write{637,637}
temp	float	$tmp338	%read{641,641} %write{639,639}
temp	float	$tmp339	%read{641,641} %write{640,640}
temp	float	$tmp340	%read{643,643} %write{642,642}
temp	int	$tmp341	%read{644,644} %write{643,643}
temp	float	$tmp342	%read{646,646} %write{645,645}
temp	float	$tmp343	%read{648,648} %write{646,646}
temp	float	$tmp344	%read{648,648} %write{647,647}
temp	float	$tmp345	%read{650,650} %write{649,649}
temp	float	$tmp346	%read{652,652} %write{650,650}
temp	float	$tmp347	%read{652,652} %write{651,651}
temp	int	$tmp348	%read{655,655} %write{654,654}
temp	float	$tmp349	%read{659,659} %write{658,658}
temp	float	$tmp350	%read{662,662} %write{659,659}
temp	float	$tmp351	%read{661,661} %write{660,660}
temp	float	$tmp352	%read{662,662} %write{661,661}
temp	float	$tmp353	%read{663,663} %write{662,662}
temp	int	$tmp354	%read{669,669} %write{668,668}
temp	int	$tmp355	%read{672,672} %write{671,671}
temp	float	$tmp356	%read{679,679} %write{678,678}
temp	float	$tmp357	%read{681,681} %write{680,680}
temp	float	$tmp358	%read{682,682} %write{681,681}
temp	float	$tmp359	%read{684,684} %write{683,683}
temp	float	$tmp360	%read{685,685} %write{684,684}
temp	float	$tmp361	%read{686,686} %write{685,685}
temp	int	$tmp362	%read{688,688} %write{687,687}
temp	int	$tmp363	%read{691,691} %write{690,690}
temp	int	$tmp364	%read{694,694} %write{693,693}
temp	int	$tmp365	%read{697,697} %write{696,696}
temp	int	$tmp366	%read{700,700} %write{699,699}
const	string	$const46	"color"		%read{704,704} %write{2147483647,-1}
temp	int	$tmp367	%read{705,705} %write{704,704}
const	string	$const47	"node_mix_color"		%read{706,706} %write{2147483647,-1}
temp	float	$tmp368	%read{713,713} %write{709,709}
temp	float	$tmp369	%read{713,713} %write{712,712}
temp	float	$tmp370	%read{712,712} %write{710,710}
temp	float	$tmp371	%read{712,712} %write{711,711}
temp	float	$tmp372	%read{718,718} %write{714,714}
temp	float	$tmp373	%read{718,718} %write{717,717}
temp	float	$tmp374	%read{717,717} %write{715,715}
temp	float	$tmp375	%read{717,717} %write{716,716}
temp	int	$tmp376	%read{722,722} %write{721,721}
temp	int	$tmp377	%read{727,727} %write{726,726}
temp	color	$tmp378	%read{730,730} %write{729,729}
temp	color	$tmp379	%read{731,731} %write{730,730}
temp	float	$tmp380	%read{733,733} %write{732,732}
temp	int	$tmp381	%read{734,734} %write{733,733}
temp	float	$tmp382	%read{737,737} %write{735,735}
temp	float	$tmp383	%read{737,737} %write{736,736}
temp	float	$tmp384	%read{739,739} %write{738,738}
temp	int	$tmp385	%read{740,740} %write{739,739}
temp	float	$tmp386	%read{742,742} %write{741,741}
temp	float	$tmp387	%read{744,744} %write{742,742}
temp	float	$tmp388	%read{744,744} %write{743,743}
temp	float	$tmp389	%read{746,746} %write{745,745}
temp	float	$tmp390	%read{748,748} %write{746,746}
temp	float	$tmp391	%read{748,748} %write{747,747}
temp	int	$tmp392	%read{751,751} %write{750,750}
temp	float	$tmp393	%read{755,755} %write{754,754}
temp	int	$tmp394	%read{756,756} %write{755,755}
temp	float	$tmp395	%read{762,762} %write{758,758}
temp	float	$tmp396	%read{762,762} %write{761,761}
temp	float	$tmp397	%read{761,761} %write{759,759}
temp	float	$tmp398	%read{761,761} %write{760,760}
temp	float	$tmp399	%read{767,767} %write{763,763}
temp	float	$tmp400	%read{767,767} %write{766,766}
temp	float	$tmp401	%read{766,766} %write{764,764}
temp	float	$tmp402	%read{766,766} %write{765,765}
temp	int	$tmp403	%read{771,771} %write{770,770}
temp	int	$tmp404	%read{776,776} %write{775,775}
temp	color	$tmp405	%read{779,779} %write{778,778}
temp	color	$tmp406	%read{780,780} %write{779,779}
temp	float	$tmp407	%read{782,782} %write{781,781}
temp	int	$tmp408	%read{783,783} %write{782,782}
temp	float	$tmp409	%read{786,786} %write{784,784}
temp	float	$tmp410	%read{786,786} %write{785,785}
temp	float	$tmp411	%read{788,788} %write{787,787}
temp	int	$tmp412	%read{789,789} %write{788,788}
temp	float	$tmp413	%read{791,791} %write{790,790}
temp	float	$tmp414	%read{793,793} %write{791,791}
temp	float	$tmp415	%read{793,793} %write{792,792}
temp	float	$tmp416	%read{795,795} %write{794,794}
temp	float	$tmp417	%read{797,797} %write{795,795}
temp	float	$tmp418	%read{797,797} %write{796,796}
temp	int	$tmp419	%read{800,800} %write{799,799}
temp	float	$tmp420	%read{804,804} %write{803,803}
temp	float	$tmp421	%read{806,806} %write{805,805}
temp	int	$tmp422	%read{812,812} %write{811,811}
temp	int	$tmp423	%read{815,815} %write{814,814}
temp	float	$tmp424	%read{822,822} %write{821,821}
temp	float	$tmp425	%read{824,824} %write{823,823}
temp	float	$tmp426	%read{825,825} %write{824,824}
temp	float	$tmp427	%read{827,827} %write{826,826}
temp	float	$tmp428	%read{828,828} %write{827,827}
temp	float	$tmp429	%read{829,829} %write{828,828}
temp	int	$tmp430	%read{831,831} %write{830,830}
temp	int	$tmp431	%read{834,834} %write{833,833}
temp	int	$tmp432	%read{837,837} %write{836,836}
temp	int	$tmp433	%read{840,840} %write{839,839}
temp	int	$tmp434	%read{843,843} %write{842,842}
const	string	$const48	"soft_light"		%read{849,849} %write{2147483647,-1}
temp	int	$tmp435	%read{850,850} %write{849,849}
const	string	$const49	"node_mix_soft"		%read{851,851} %write{2147483647,-1}
temp	color	$tmp436	%read{856,856} %write{854,854}
temp	color	$tmp437	%read{856,856} %write{855,855}
temp	color	$tmp438	%read{857,857} %write{856,856}
temp	color	$tmp439	%read{865,865} %write{858,858}
temp	color	$tmp440	%read{860,860} %write{859,859}
temp	color	$tmp441	%read{861,861} %write{860,860}
temp	color	$tmp442	%read{863,863} %write{861,861}
temp	color	$tmp443	%read{863,863} %write{862,862}
temp	color	$tmp444	%read{864,864} %write{863,863}
temp	color	$tmp445	%read{865,865} %write{864,864}
const	string	$const50	"linear_light"		%read{866,866} %write{2147483647,-1}
temp	int	$tmp446	%read{867,867} %write{866,866}
const	string	$const51	"node_mix_linear"		%read{868,868} %write{2147483647,-1}
temp	float	$tmp447	%read{871,871} %write{870,870}
temp	int	$tmp448	%read{872,872} %write{871,871}
temp	float	$tmp449	%read{878,878} %write{873,873}
temp	float	$tmp450	%read{875,875} %write{874,874}
temp	float	$tmp451	%read{876,876} %write{875,875}
temp	float	$tmp452	%read{877,877} %write{876,876}
temp	float	$tmp453	%read{878,878} %write{877,877}
temp	float	$tmp454	%read{879,879} %write{878,878}
temp	float	$tmp455	%read{885,885} %write{880,880}
temp	float	$tmp456	%read{882,882} %write{881,881}
temp	float	$tmp457	%read{883,883} %write{882,882}
temp	float	$tmp458	%read{884,884} %write{883,883}
temp	float	$tmp459	%read{885,885} %write{884,884}
temp	float	$tmp460	%read{886,886} %write{885,885}
temp	float	$tmp461	%read{888,888} %write{887,887}
temp	int	$tmp462	%read{889,889} %write{888,888}
temp	float	$tmp463	%read{895,895} %write{890,890}
temp	float	$tmp464	%read{892,892} %write{891,891}
temp	float	$tmp465	%read{893,893} %write{892,892}
temp	float	$tmp466	%read{894,894} %write{893,893}
temp	float	$tmp467	%read{895,895} %write{894,894}
temp	float	$tmp468	%read{896,896} %write{895,895}
temp	float	$tmp469	%read{902,902} %write{897,897}
temp	float	$tmp470	%read{899,899} %write{898,898}
temp	float	$tmp471	%read{900,900} %write{899,899}
temp	float	$tmp472	%read{901,901} %write{900,900}
temp	float	$tmp473	%read{902,902} %write{901,901}
temp	float	$tmp474	%read{903,903} %write{902,902}
temp	float	$tmp475	%read{905,905} %write{904,904}
temp	int	$tmp476	%read{906,906} %write{905,905}
temp	float	$tmp477	%read{912,912} %write{907,907}
temp	float	$tmp478	%read{909,909} %write{908,908}
temp	float	$tmp479	%read{910,910} %write{909,909}
temp	float	$tmp480	%read{911,911} %write{910,910}
temp	float	$tmp481	%read{912,912} %write{911,911}
temp	float	$tmp482	%read{913,913} %write{912,912}
temp	float	$tmp483	%read{919,919} %write{914,914}
temp	float	$tmp484	%read{916,916} %write{915,915}
temp	float	$tmp485	%read{917,917} %write{916,916}
temp	float	$tmp486	%read{918,918} %write{917,917}
temp	float	$tmp487	%read{919,919} %write{918,918}
temp	float	$tmp488	%read{920,920} %write{919,919}
temp	color	$tmp489	%read{927,927} %write{923,923}
temp	color	$tmp490	%read{926,926} %write{924,924}
temp	color	$tmp491	%read{927,927} %write{926,926}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:17
#   float t = (use_clamp) ? clamp(Factor, 0.0, 1.0) : Factor;
	if		use_clamp 4 5 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{17} %argrw{"r"}
	functioncall	$const3 4 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp1 Factor $const2 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		t $tmp1 $const1 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:17
#   float t = (use_clamp) ? clamp(Factor, 0.0, 1.0) : Factor;
	assign		t Factor 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{17} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:19
#   if (blend_type == "mix")
	eq		$tmp2 blend_type $const4 	%line{19} %argrw{"wrr"}
	if		$tmp2 8 8 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:20
#     Result = mix(A, B, t);
	mix		Result A B t 	%line{20} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:21
#   if (blend_type == "add")
	eq		$tmp3 blend_type $const5 	%line{21} %argrw{"wrr"}
	if		$tmp3 13 13 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:22
#     Result = node_mix_add(t, A, B);
	functioncall	$const6 13 	%line{22} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:12
#   return mix(col1, col1 + col2, t);
	add		$tmp4 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{12} %argrw{"wrr"}
	mix		Result A $tmp4 t 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:23
#   if (blend_type == "multiply")
	eq		$tmp5 blend_type $const7 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{23} %argrw{"wrr"}
	if		$tmp5 18 18 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:24
#     Result = node_mix_mul(t, A, B);
	functioncall	$const8 18 	%line{24} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:17
#   return mix(col1, col1 * col2, t);
	mul		$tmp6 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{17} %argrw{"wrr"}
	mix		Result A $tmp6 t 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:25
#   if (blend_type == "screen")
	eq		$tmp7 blend_type $const9 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{25} %argrw{"wrr"}
	if		$tmp7 29 29 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:26
#     Result = node_mix_screen(t, A, B);
	functioncall	$const10 29 	%line{26} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:22
#   float tm = 1.0 - t;
	sub		___390_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{22} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:24
#   return color(1.0) - (color(tm) + t * (color(1.0) - col2)) * (color(1.0) - col1);
	assign		$tmp9 ___390_tm 	%line{24} %argrw{"wr"}
	sub		$tmp11 $const11 B 	%argrw{"wrr"}
	mul		$tmp12 t $tmp11 	%argrw{"wrr"}
	add		$tmp13 $tmp9 $tmp12 	%argrw{"wrr"}
	sub		$tmp15 $const11 A 	%argrw{"wrr"}
	mul		$tmp16 $tmp13 $tmp15 	%argrw{"wrr"}
	sub		Result $const11 $tmp16 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:27
#   if (blend_type == "overlay")
	eq		$tmp17 blend_type $const12 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{27} %argrw{"wrr"}
	if		$tmp17 95 95 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:28
#     Result = node_mix_overlay(t, A, B);
	functioncall	$const13 95 	%line{28} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:29
#   float tm = 1.0 - t;
	sub		___391_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{29} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:31
#   color outcol = col1;
	assign		___391_outcol A 	%line{31} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:33
#   if (outcol[0] < 0.5) {
	compref		$tmp18 ___391_outcol $const14 	%line{33} %argrw{"wrr"}
	lt		$tmp19 $tmp18 $const15 	%argrw{"wrr"}
	if		$tmp19 44 54 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:34
#     outcol[0] *= tm + 2.0 * t * col2[0];
	compref		$tmp20 ___391_outcol $const14 	%line{34} %argrw{"wrr"}
	mul		$tmp21 $const16 t 	%argrw{"wrr"}
	compref		$tmp22 B $const14 	%argrw{"wrr"}
	mul		$tmp23 $tmp21 $tmp22 	%argrw{"wrr"}
	add		$tmp24 ___391_tm $tmp23 	%argrw{"wrr"}
	mul		$tmp25 $tmp20 $tmp24 	%argrw{"wrr"}
	compassign	___391_outcol $const14 $tmp25 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:37
#     outcol[0] = 1.0 - (tm + 2.0 * t * (1.0 - col2[0])) * (1.0 - outcol[0]);
	mul		$tmp26 $const16 t 	%line{37} %argrw{"wrr"}
	compref		$tmp27 B $const14 	%argrw{"wrr"}
	sub		$tmp28 $const2 $tmp27 	%argrw{"wrr"}
	mul		$tmp29 $tmp26 $tmp28 	%argrw{"wrr"}
	add		$tmp30 ___391_tm $tmp29 	%argrw{"wrr"}
	compref		$tmp31 ___391_outcol $const14 	%argrw{"wrr"}
	sub		$tmp32 $const2 $tmp31 	%argrw{"wrr"}
	mul		$tmp33 $tmp30 $tmp32 	%argrw{"wrr"}
	sub		$tmp34 $const2 $tmp33 	%argrw{"wrr"}
	compassign	___391_outcol $const14 $tmp34 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:40
#   if (outcol[1] < 0.5) {
	compref		$tmp35 ___391_outcol $const17 	%line{40} %argrw{"wrr"}
	lt		$tmp36 $tmp35 $const15 	%argrw{"wrr"}
	if		$tmp36 64 74 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:41
#     outcol[1] *= tm + 2.0 * t * col2[1];
	compref		$tmp37 ___391_outcol $const17 	%line{41} %argrw{"wrr"}
	mul		$tmp38 $const16 t 	%argrw{"wrr"}
	compref		$tmp39 B $const17 	%argrw{"wrr"}
	mul		$tmp40 $tmp38 $tmp39 	%argrw{"wrr"}
	add		$tmp41 ___391_tm $tmp40 	%argrw{"wrr"}
	mul		$tmp42 $tmp37 $tmp41 	%argrw{"wrr"}
	compassign	___391_outcol $const17 $tmp42 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:44
#     outcol[1] = 1.0 - (tm + 2.0 * t * (1.0 - col2[1])) * (1.0 - outcol[1]);
	mul		$tmp43 $const16 t 	%line{44} %argrw{"wrr"}
	compref		$tmp44 B $const17 	%argrw{"wrr"}
	sub		$tmp45 $const2 $tmp44 	%argrw{"wrr"}
	mul		$tmp46 $tmp43 $tmp45 	%argrw{"wrr"}
	add		$tmp47 ___391_tm $tmp46 	%argrw{"wrr"}
	compref		$tmp48 ___391_outcol $const17 	%argrw{"wrr"}
	sub		$tmp49 $const2 $tmp48 	%argrw{"wrr"}
	mul		$tmp50 $tmp47 $tmp49 	%argrw{"wrr"}
	sub		$tmp51 $const2 $tmp50 	%argrw{"wrr"}
	compassign	___391_outcol $const17 $tmp51 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:47
#   if (outcol[2] < 0.5) {
	compref		$tmp52 ___391_outcol $const18 	%line{47} %argrw{"wrr"}
	lt		$tmp53 $tmp52 $const15 	%argrw{"wrr"}
	if		$tmp53 84 94 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:48
#     outcol[2] *= tm + 2.0 * t * col2[2];
	compref		$tmp54 ___391_outcol $const18 	%line{48} %argrw{"wrr"}
	mul		$tmp55 $const16 t 	%argrw{"wrr"}
	compref		$tmp56 B $const18 	%argrw{"wrr"}
	mul		$tmp57 $tmp55 $tmp56 	%argrw{"wrr"}
	add		$tmp58 ___391_tm $tmp57 	%argrw{"wrr"}
	mul		$tmp59 $tmp54 $tmp58 	%argrw{"wrr"}
	compassign	___391_outcol $const18 $tmp59 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:51
#     outcol[2] = 1.0 - (tm + 2.0 * t * (1.0 - col2[2])) * (1.0 - outcol[2]);
	mul		$tmp60 $const16 t 	%line{51} %argrw{"wrr"}
	compref		$tmp61 B $const18 	%argrw{"wrr"}
	sub		$tmp62 $const2 $tmp61 	%argrw{"wrr"}
	mul		$tmp63 $tmp60 $tmp62 	%argrw{"wrr"}
	add		$tmp64 ___391_tm $tmp63 	%argrw{"wrr"}
	compref		$tmp65 ___391_outcol $const18 	%argrw{"wrr"}
	sub		$tmp66 $const2 $tmp65 	%argrw{"wrr"}
	mul		$tmp67 $tmp64 $tmp66 	%argrw{"wrr"}
	sub		$tmp68 $const2 $tmp67 	%argrw{"wrr"}
	compassign	___391_outcol $const18 $tmp68 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:54
#   return outcol;
	assign		Result ___391_outcol 	%line{54} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:29
#   if (blend_type == "subtract")
	eq		$tmp69 blend_type $const19 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{29} %argrw{"wrr"}
	if		$tmp69 100 100 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:30
#     Result = node_mix_sub(t, A, B);
	functioncall	$const20 100 	%line{30} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:59
#   return mix(col1, col1 - col2, t);
	sub		$tmp70 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{59} %argrw{"wrr"}
	mix		Result A $tmp70 t 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:31
#   if (blend_type == "divide")
	eq		$tmp71 blend_type $const21 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{31} %argrw{"wrr"}
	if		$tmp71 139 139 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:32
#     Result = node_mix_div(t, A, B);
	functioncall	$const22 139 	%line{32} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:64
#   float tm = 1.0 - t;
	sub		___399_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{64} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:66
#   color outcol = col1;
	assign		___399_outcol A 	%line{66} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:68
#   if (col2[0] != 0.0) {
	compref		$tmp72 B $const14 	%line{68} %argrw{"wrr"}
	neq		$tmp73 $tmp72 $const1 	%argrw{"wrr"}
	if		$tmp73 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:69
#     outcol[0] = tm * outcol[0] + t * outcol[0] / col2[0];
	compref		$tmp74 ___399_outcol $const14 	%line{69} %argrw{"wrr"}
	mul		$tmp75 ___399_tm $tmp74 	%argrw{"wrr"}
	compref		$tmp76 ___399_outcol $const14 	%argrw{"wrr"}
	mul		$tmp77 t $tmp76 	%argrw{"wrr"}
	compref		$tmp78 B $const14 	%argrw{"wrr"}
	div		$tmp79 $tmp77 $tmp78 	%argrw{"wrr"}
	add		$tmp80 $tmp75 $tmp79 	%argrw{"wrr"}
	compassign	___399_outcol $const14 $tmp80 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:71
#   if (col2[1] != 0.0) {
	compref		$tmp81 B $const17 	%line{71} %argrw{"wrr"}
	neq		$tmp82 $tmp81 $const1 	%argrw{"wrr"}
	if		$tmp82 127 127 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:72
#     outcol[1] = tm * outcol[1] + t * outcol[1] / col2[1];
	compref		$tmp83 ___399_outcol $const17 	%line{72} %argrw{"wrr"}
	mul		$tmp84 ___399_tm $tmp83 	%argrw{"wrr"}
	compref		$tmp85 ___399_outcol $const17 	%argrw{"wrr"}
	mul		$tmp86 t $tmp85 	%argrw{"wrr"}
	compref		$tmp87 B $const17 	%argrw{"wrr"}
	div		$tmp88 $tmp86 $tmp87 	%argrw{"wrr"}
	add		$tmp89 $tmp84 $tmp88 	%argrw{"wrr"}
	compassign	___399_outcol $const17 $tmp89 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:74
#   if (col2[2] != 0.0) {
	compref		$tmp90 B $const18 	%line{74} %argrw{"wrr"}
	neq		$tmp91 $tmp90 $const1 	%argrw{"wrr"}
	if		$tmp91 138 138 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:75
#     outcol[2] = tm * outcol[2] + t * outcol[2] / col2[2];
	compref		$tmp92 ___399_outcol $const18 	%line{75} %argrw{"wrr"}
	mul		$tmp93 ___399_tm $tmp92 	%argrw{"wrr"}
	compref		$tmp94 ___399_outcol $const18 	%argrw{"wrr"}
	mul		$tmp95 t $tmp94 	%argrw{"wrr"}
	compref		$tmp96 B $const18 	%argrw{"wrr"}
	div		$tmp97 $tmp95 $tmp96 	%argrw{"wrr"}
	add		$tmp98 $tmp93 $tmp97 	%argrw{"wrr"}
	compassign	___399_outcol $const18 $tmp98 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:78
#   return outcol;
	assign		Result ___399_outcol 	%line{78} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:33
#   if (blend_type == "difference")
	eq		$tmp99 blend_type $const23 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{33} %argrw{"wrr"}
	if		$tmp99 145 145 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:34
#     Result = node_mix_diff(t, A, B);
	functioncall	$const24 145 	%line{34} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:83
#   return mix(col1, abs(col1 - col2), t);
	sub		$tmp101 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{83} %argrw{"wrr"}
	abs		$tmp100 $tmp101 	%argrw{"wr"}
	mix		Result A $tmp100 t 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:35
#   if (blend_type == "exclusion")
	eq		$tmp102 blend_type $const25 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{35} %argrw{"wrr"}
	if		$tmp102 155 155 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:36
#     Result = node_mix_exclusion(t, A, B);
	functioncall	$const26 155 	%line{36} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:88
#   return max(mix(col1, col1 + col2 - 2.0 * col1 * col2, t), 0.0);
	add		$tmp104 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{88} %argrw{"wrr"}
	mul		$tmp105 $const16 A 	%argrw{"wrr"}
	mul		$tmp106 $tmp105 B 	%argrw{"wrr"}
	sub		$tmp107 $tmp104 $tmp106 	%argrw{"wrr"}
	mix		$tmp103 A $tmp107 t 	%argrw{"wrrr"}
	assign		$tmp108 $const1 	%argrw{"wr"}
	max		Result $tmp103 $tmp108 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:37
#   if (blend_type == "darken")
	eq		$tmp109 blend_type $const27 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{37} %argrw{"wrr"}
	if		$tmp109 160 160 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:38
#     Result = node_mix_dark(t, A, B);
	functioncall	$const28 160 	%line{38} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:93
#   return mix(col1, min(col1, col2), t);
	min		$tmp110 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{93} %argrw{"wrr"}
	mix		Result A $tmp110 t 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:39
#   if (blend_type == "lighten")
	eq		$tmp111 blend_type $const29 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{39} %argrw{"wrr"}
	if		$tmp111 165 165 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:40
#     Result = node_mix_light(t, A, B);
	functioncall	$const30 165 	%line{40} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:98
#   return mix(col1, max(col1, col2), t);
	max		$tmp112 A B 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{98} %argrw{"wrr"}
	mix		Result A $tmp112 t 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:41
#   if (blend_type == "dodge")
	eq		$tmp113 blend_type $const31 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{41} %argrw{"wrr"}
	if		$tmp113 215 215 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:42
#     Result = node_mix_dodge(t, A, B);
	functioncall	$const32 215 	%line{42} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:103
#   color outcol = col1;
	assign		___407_outcol A 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{103} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:105
#   if (outcol[0] != 0.0) {
	compref		$tmp114 ___407_outcol $const14 	%line{105} %argrw{"wrr"}
	neq		$tmp115 $tmp114 $const1 	%argrw{"wrr"}
	if		$tmp115 184 184 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:106
#     float tmp = 1.0 - t * col2[0];
	compref		$tmp116 B $const14 	%line{106} %argrw{"wrr"}
	mul		$tmp117 t $tmp116 	%argrw{"wrr"}
	sub		___408_tmp $const2 $tmp117 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:107
#     if (tmp <= 0.0) {
	le		$tmp118 ___408_tmp $const1 	%line{107} %argrw{"wrr"}
	if		$tmp118 178 184 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:108
#       outcol[0] = 1.0;
	compassign	___407_outcol $const14 $const2 	%line{108} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:110
#     else if ((tmp = outcol[0] / tmp) > 1.0) {
	compref		$tmp119 ___407_outcol $const14 	%line{110} %argrw{"wrr"}
	div		___408_tmp $tmp119 ___408_tmp 	%argrw{"wrr"}
	gt		$tmp120 ___408_tmp $const2 	%argrw{"wrr"}
	if		$tmp120 183 184 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:111
#       outcol[0] = 1.0;
	compassign	___407_outcol $const14 $const2 	%line{111} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:114
#       outcol[0] = tmp;
	compassign	___407_outcol $const14 ___408_tmp 	%line{114} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:117
#   if (outcol[1] != 0.0) {
	compref		$tmp121 ___407_outcol $const17 	%line{117} %argrw{"wrr"}
	neq		$tmp122 $tmp121 $const1 	%argrw{"wrr"}
	if		$tmp122 199 199 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:118
#     float tmp = 1.0 - t * col2[1];
	compref		$tmp123 B $const17 	%line{118} %argrw{"wrr"}
	mul		$tmp124 t $tmp123 	%argrw{"wrr"}
	sub		___412_tmp $const2 $tmp124 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:119
#     if (tmp <= 0.0) {
	le		$tmp125 ___412_tmp $const1 	%line{119} %argrw{"wrr"}
	if		$tmp125 193 199 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:120
#       outcol[1] = 1.0;
	compassign	___407_outcol $const17 $const2 	%line{120} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:122
#     else if ((tmp = outcol[1] / tmp) > 1.0) {
	compref		$tmp126 ___407_outcol $const17 	%line{122} %argrw{"wrr"}
	div		___412_tmp $tmp126 ___412_tmp 	%argrw{"wrr"}
	gt		$tmp127 ___412_tmp $const2 	%argrw{"wrr"}
	if		$tmp127 198 199 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:123
#       outcol[1] = 1.0;
	compassign	___407_outcol $const17 $const2 	%line{123} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:126
#       outcol[1] = tmp;
	compassign	___407_outcol $const17 ___412_tmp 	%line{126} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:129
#   if (outcol[2] != 0.0) {
	compref		$tmp128 ___407_outcol $const18 	%line{129} %argrw{"wrr"}
	neq		$tmp129 $tmp128 $const1 	%argrw{"wrr"}
	if		$tmp129 214 214 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:130
#     float tmp = 1.0 - t * col2[2];
	compref		$tmp130 B $const18 	%line{130} %argrw{"wrr"}
	mul		$tmp131 t $tmp130 	%argrw{"wrr"}
	sub		___416_tmp $const2 $tmp131 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:131
#     if (tmp <= 0.0) {
	le		$tmp132 ___416_tmp $const1 	%line{131} %argrw{"wrr"}
	if		$tmp132 208 214 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:132
#       outcol[2] = 1.0;
	compassign	___407_outcol $const18 $const2 	%line{132} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:134
#     else if ((tmp = outcol[2] / tmp) > 1.0) {
	compref		$tmp133 ___407_outcol $const18 	%line{134} %argrw{"wrr"}
	div		___416_tmp $tmp133 ___416_tmp 	%argrw{"wrr"}
	gt		$tmp134 ___416_tmp $const2 	%argrw{"wrr"}
	if		$tmp134 213 214 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:135
#       outcol[2] = 1.0;
	compassign	___407_outcol $const18 $const2 	%line{135} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:138
#       outcol[2] = tmp;
	compassign	___407_outcol $const18 ___416_tmp 	%line{138} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:142
#   return outcol;
	assign		Result ___407_outcol 	%line{142} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:43
#   if (blend_type == "burn")
	eq		$tmp135 blend_type $const33 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{43} %argrw{"wrr"}
	if		$tmp135 272 272 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:44
#     Result = node_mix_burn(t, A, B);
	functioncall	$const34 272 	%line{44} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:147
#   float tmp, tm = 1.0 - t;
	sub		___420_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{147} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:149
#   color outcol = col1;
	assign		___420_outcol A 	%line{149} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:151
#   tmp = tm + t * col2[0];
	compref		$tmp136 B $const14 	%line{151} %argrw{"wrr"}
	mul		$tmp137 t $tmp136 	%argrw{"wrr"}
	add		___420_tmp ___420_tm $tmp137 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:152
#   if (tmp <= 0.0) {
	le		$tmp138 ___420_tmp $const1 	%line{152} %argrw{"wrr"}
	if		$tmp138 226 237 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:153
#     outcol[0] = 0.0;
	compassign	___420_outcol $const14 $const1 	%line{153} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:155
#   else if ((tmp = (1.0 - (1.0 - outcol[0]) / tmp)) < 0.0) {
	compref		$tmp139 ___420_outcol $const14 	%line{155} %argrw{"wrr"}
	sub		$tmp140 $const2 $tmp139 	%argrw{"wrr"}
	div		$tmp141 $tmp140 ___420_tmp 	%argrw{"wrr"}
	sub		___420_tmp $const2 $tmp141 	%argrw{"wrr"}
	lt		$tmp142 ___420_tmp $const1 	%argrw{"wrr"}
	if		$tmp142 233 237 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:156
#     outcol[0] = 0.0;
	compassign	___420_outcol $const14 $const1 	%line{156} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:158
#   else if (tmp > 1.0) {
	gt		$tmp143 ___420_tmp $const2 	%line{158} %argrw{"wrr"}
	if		$tmp143 236 237 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:159
#     outcol[0] = 1.0;
	compassign	___420_outcol $const14 $const2 	%line{159} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:162
#     outcol[0] = tmp;
	compassign	___420_outcol $const14 ___420_tmp 	%line{162} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:165
#   tmp = tm + t * col2[1];
	compref		$tmp144 B $const17 	%line{165} %argrw{"wrr"}
	mul		$tmp145 t $tmp144 	%argrw{"wrr"}
	add		___420_tmp ___420_tm $tmp145 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:166
#   if (tmp <= 0.0) {
	le		$tmp146 ___420_tmp $const1 	%line{166} %argrw{"wrr"}
	if		$tmp146 243 254 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:167
#     outcol[1] = 0.0;
	compassign	___420_outcol $const17 $const1 	%line{167} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:169
#   else if ((tmp = (1.0 - (1.0 - outcol[1]) / tmp)) < 0.0) {
	compref		$tmp147 ___420_outcol $const17 	%line{169} %argrw{"wrr"}
	sub		$tmp148 $const2 $tmp147 	%argrw{"wrr"}
	div		$tmp149 $tmp148 ___420_tmp 	%argrw{"wrr"}
	sub		___420_tmp $const2 $tmp149 	%argrw{"wrr"}
	lt		$tmp150 ___420_tmp $const1 	%argrw{"wrr"}
	if		$tmp150 250 254 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:170
#     outcol[1] = 0.0;
	compassign	___420_outcol $const17 $const1 	%line{170} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:172
#   else if (tmp > 1.0) {
	gt		$tmp151 ___420_tmp $const2 	%line{172} %argrw{"wrr"}
	if		$tmp151 253 254 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:173
#     outcol[1] = 1.0;
	compassign	___420_outcol $const17 $const2 	%line{173} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:176
#     outcol[1] = tmp;
	compassign	___420_outcol $const17 ___420_tmp 	%line{176} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:179
#   tmp = tm + t * col2[2];
	compref		$tmp152 B $const18 	%line{179} %argrw{"wrr"}
	mul		$tmp153 t $tmp152 	%argrw{"wrr"}
	add		___420_tmp ___420_tm $tmp153 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:180
#   if (tmp <= 0.0) {
	le		$tmp154 ___420_tmp $const1 	%line{180} %argrw{"wrr"}
	if		$tmp154 260 271 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:181
#     outcol[2] = 0.0;
	compassign	___420_outcol $const18 $const1 	%line{181} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:183
#   else if ((tmp = (1.0 - (1.0 - outcol[2]) / tmp)) < 0.0) {
	compref		$tmp155 ___420_outcol $const18 	%line{183} %argrw{"wrr"}
	sub		$tmp156 $const2 $tmp155 	%argrw{"wrr"}
	div		$tmp157 $tmp156 ___420_tmp 	%argrw{"wrr"}
	sub		___420_tmp $const2 $tmp157 	%argrw{"wrr"}
	lt		$tmp158 ___420_tmp $const1 	%argrw{"wrr"}
	if		$tmp158 267 271 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:184
#     outcol[2] = 0.0;
	compassign	___420_outcol $const18 $const1 	%line{184} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:186
#   else if (tmp > 1.0) {
	gt		$tmp159 ___420_tmp $const2 	%line{186} %argrw{"wrr"}
	if		$tmp159 270 271 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:187
#     outcol[2] = 1.0;
	compassign	___420_outcol $const18 $const2 	%line{187} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:190
#     outcol[2] = tmp;
	compassign	___420_outcol $const18 ___420_tmp 	%line{190} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:193
#   return outcol;
	assign		Result ___420_outcol 	%line{193} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:45
#   if (blend_type == "hue")
	eq		$tmp160 blend_type $const35 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{45} %argrw{"wrr"}
	if		$tmp160 415 415 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:46
#     Result = node_mix_hue(t, A, B);
	functioncall	$const36 415 	%line{46} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:198
#   color outcol = col1;
	assign		___433_outcol A 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{198} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:199
#   color hsv2 = rgb_to_hsv(col2);
	functioncall	$const37 322 	%line{199} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp161 B $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp163 B $const17 	%argrw{"wrr"}
	compref		$tmp164 B $const18 	%argrw{"wrr"}
	max		$tmp162 $tmp163 $tmp164 	%argrw{"wrr"}
	max		___361_cmax $tmp161 $tmp162 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp165 B $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp167 B $const17 	%argrw{"wrr"}
	compref		$tmp168 B $const18 	%argrw{"wrr"}
	min		$tmp166 $tmp167 $tmp168 	%argrw{"wrr"}
	min		___361_cmin $tmp165 $tmp166 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp169 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp169 292 294 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp170 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp170 297 321 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp171 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp172 $tmp171 B 	%argrw{"wrr"}
	div		___361_c $tmp172 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp173 B $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp174 $tmp173 ___361_cmax 	%argrw{"wrr"}
	if		$tmp174 306 317 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp175 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp176 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp175 $tmp176 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp177 B $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp178 $tmp177 ___361_cmax 	%argrw{"wrr"}
	if		$tmp178 313 317 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp179 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp180 $const16 $tmp179 	%argrw{"wrr"}
	compref		$tmp181 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp180 $tmp181 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp182 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp183 $const38 $tmp182 	%argrw{"wrr"}
	compref		$tmp184 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp183 $tmp184 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp185 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp185 321 321 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___433_hsv2 ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:201
#   if (hsv2[1] != 0.0) {
	compref		$tmp186 ___433_hsv2 $const17 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{201} %argrw{"wrr"}
	neq		$tmp187 $tmp186 $const1 	%argrw{"wrr"}
	if		$tmp187 414 414 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:202
#     color hsv = rgb_to_hsv(outcol);
	functioncall	$const37 371 	%line{202} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp188 ___433_outcol $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp190 ___433_outcol $const17 	%argrw{"wrr"}
	compref		$tmp191 ___433_outcol $const18 	%argrw{"wrr"}
	max		$tmp189 $tmp190 $tmp191 	%argrw{"wrr"}
	max		___361_cmax $tmp188 $tmp189 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp192 ___433_outcol $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp194 ___433_outcol $const17 	%argrw{"wrr"}
	compref		$tmp195 ___433_outcol $const18 	%argrw{"wrr"}
	min		$tmp193 $tmp194 $tmp195 	%argrw{"wrr"}
	min		___361_cmin $tmp192 $tmp193 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp196 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp196 341 343 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp197 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp197 346 370 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp198 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp199 $tmp198 ___433_outcol 	%argrw{"wrr"}
	div		___361_c $tmp199 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp200 ___433_outcol $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp201 $tmp200 ___361_cmax 	%argrw{"wrr"}
	if		$tmp201 355 366 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp202 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp203 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp202 $tmp203 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp204 ___433_outcol $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp205 $tmp204 ___361_cmax 	%argrw{"wrr"}
	if		$tmp205 362 366 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp206 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp207 $const16 $tmp206 	%argrw{"wrr"}
	compref		$tmp208 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp207 $tmp208 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp209 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp210 $const38 $tmp209 	%argrw{"wrr"}
	compref		$tmp211 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp210 $tmp211 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp212 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp212 370 370 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___434_hsv ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:203
#     hsv[0] = hsv2[0];
	compref		$tmp213 ___433_hsv2 $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{203} %argrw{"wrr"}
	compassign	___434_hsv $const14 $tmp213 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:204
#     color tmp = hsv_to_rgb(hsv);
	functioncall	$const40 413 	%line{204} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:130
#   h = hsv[0];
	compref		___370_h ___434_hsv $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{130} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:131
#   s = hsv[1];
	compref		___370_s ___434_hsv $const17 	%line{131} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:132
#   v = hsv[2];
	compref		___370_v ___434_hsv $const18 	%line{132} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:134
#   if (s == 0.0) {
	eq		$tmp214 ___370_s $const1 	%line{134} %argrw{"wrr"}
	if		$tmp214 380 412 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:135
#     rgb = color(v, v, v);
	color		___370_rgb ___370_v ___370_v ___370_v 	%line{135} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:138
#     if (h == 1.0) {
	eq		$tmp215 ___370_h $const2 	%line{138} %argrw{"wrr"}
	if		$tmp215 383 383 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:139
#       h = 0.0;
	assign		___370_h $const1 	%line{139} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:142
#     h *= 6.0;
	mul		___370_h ___370_h $const39 	%line{142} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:143
#     i = floor(h);
	floor		___370_i ___370_h 	%line{143} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:144
#     f = h - i;
	sub		___370_f ___370_h ___370_i 	%line{144} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:145
#     rgb = color(f, f, f);
	color		___370_rgb ___370_f ___370_f ___370_f 	%line{145} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:146
#     p = v * (1.0 - s);
	sub		$tmp216 $const2 ___370_s 	%line{146} %argrw{"wrr"}
	mul		___370_p ___370_v $tmp216 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:147
#     q = v * (1.0 - (s * f));
	mul		$tmp217 ___370_s ___370_f 	%line{147} %argrw{"wrr"}
	sub		$tmp218 $const2 $tmp217 	%argrw{"wrr"}
	mul		___370_q ___370_v $tmp218 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:148
#     t = v * (1.0 - (s * (1.0 - f)));
	sub		$tmp219 $const2 ___370_f 	%line{148} %argrw{"wrr"}
	mul		$tmp220 ___370_s $tmp219 	%argrw{"wrr"}
	sub		$tmp221 $const2 $tmp220 	%argrw{"wrr"}
	mul		___370_t ___370_v $tmp221 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:150
#     if (i == 0.0) {
	eq		$tmp222 ___370_i $const1 	%line{150} %argrw{"wrr"}
	if		$tmp222 399 412 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:151
#       rgb = color(v, t, p);
	color		___370_rgb ___370_v ___370_t ___370_p 	%line{151} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:153
#     else if (i == 1.0) {
	eq		$tmp223 ___370_i $const2 	%line{153} %argrw{"wrr"}
	if		$tmp223 402 412 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:154
#       rgb = color(q, v, p);
	color		___370_rgb ___370_q ___370_v ___370_p 	%line{154} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:156
#     else if (i == 2.0) {
	eq		$tmp224 ___370_i $const16 	%line{156} %argrw{"wrr"}
	if		$tmp224 405 412 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:157
#       rgb = color(p, v, t);
	color		___370_rgb ___370_p ___370_v ___370_t 	%line{157} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:159
#     else if (i == 3.0) {
	eq		$tmp225 ___370_i $const41 	%line{159} %argrw{"wrr"}
	if		$tmp225 408 412 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:160
#       rgb = color(p, q, v);
	color		___370_rgb ___370_p ___370_q ___370_v 	%line{160} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:162
#     else if (i == 4.0) {
	eq		$tmp226 ___370_i $const38 	%line{162} %argrw{"wrr"}
	if		$tmp226 411 412 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:163
#       rgb = color(t, p, v);
	color		___370_rgb ___370_t ___370_p ___370_v 	%line{163} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:166
#       rgb = color(v, p, q);
	color		___370_rgb ___370_v ___370_p ___370_q 	%line{166} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:170
#   return rgb;
	assign		___434_tmp ___370_rgb 	%line{170} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:206
#     outcol = mix(outcol, tmp, t);
	mix		___433_outcol ___433_outcol ___434_tmp t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{206} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:209
#   return outcol;
	assign		Result ___433_outcol 	%line{209} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:47
#   if (blend_type == "saturation")
	eq		$tmp227 blend_type $const42 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{47} %argrw{"wrr"}
	if		$tmp227 562 562 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:48
#     Result = node_mix_sat(t, A, B);
	functioncall	$const43 562 	%line{48} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:214
#   float tm = 1.0 - t;
	sub		___435_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{214} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:216
#   color outcol = col1;
	assign		___435_outcol A 	%line{216} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:218
#   color hsv = rgb_to_hsv(outcol);
	functioncall	$const37 466 	%line{218} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp228 ___435_outcol $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp230 ___435_outcol $const17 	%argrw{"wrr"}
	compref		$tmp231 ___435_outcol $const18 	%argrw{"wrr"}
	max		$tmp229 $tmp230 $tmp231 	%argrw{"wrr"}
	max		___361_cmax $tmp228 $tmp229 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp232 ___435_outcol $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp234 ___435_outcol $const17 	%argrw{"wrr"}
	compref		$tmp235 ___435_outcol $const18 	%argrw{"wrr"}
	min		$tmp233 $tmp234 $tmp235 	%argrw{"wrr"}
	min		___361_cmin $tmp232 $tmp233 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp236 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp236 436 438 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp237 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp237 441 465 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp238 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp239 $tmp238 ___435_outcol 	%argrw{"wrr"}
	div		___361_c $tmp239 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp240 ___435_outcol $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp241 $tmp240 ___361_cmax 	%argrw{"wrr"}
	if		$tmp241 450 461 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp242 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp243 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp242 $tmp243 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp244 ___435_outcol $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp245 $tmp244 ___361_cmax 	%argrw{"wrr"}
	if		$tmp245 457 461 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp246 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp247 $const16 $tmp246 	%argrw{"wrr"}
	compref		$tmp248 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp247 $tmp248 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp249 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp250 $const38 $tmp249 	%argrw{"wrr"}
	compref		$tmp251 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp250 $tmp251 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp252 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp252 465 465 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___435_hsv ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:220
#   if (hsv[1] != 0.0) {
	compref		$tmp253 ___435_hsv $const17 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{220} %argrw{"wrr"}
	neq		$tmp254 $tmp253 $const1 	%argrw{"wrr"}
	if		$tmp254 561 561 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:221
#     color hsv2 = rgb_to_hsv(col2);
	functioncall	$const37 515 	%line{221} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp255 B $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp257 B $const17 	%argrw{"wrr"}
	compref		$tmp258 B $const18 	%argrw{"wrr"}
	max		$tmp256 $tmp257 $tmp258 	%argrw{"wrr"}
	max		___361_cmax $tmp255 $tmp256 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp259 B $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp261 B $const17 	%argrw{"wrr"}
	compref		$tmp262 B $const18 	%argrw{"wrr"}
	min		$tmp260 $tmp261 $tmp262 	%argrw{"wrr"}
	min		___361_cmin $tmp259 $tmp260 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp263 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp263 485 487 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp264 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp264 490 514 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp265 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp266 $tmp265 B 	%argrw{"wrr"}
	div		___361_c $tmp266 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp267 B $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp268 $tmp267 ___361_cmax 	%argrw{"wrr"}
	if		$tmp268 499 510 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp269 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp270 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp269 $tmp270 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp271 B $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp272 $tmp271 ___361_cmax 	%argrw{"wrr"}
	if		$tmp272 506 510 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp273 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp274 $const16 $tmp273 	%argrw{"wrr"}
	compref		$tmp275 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp274 $tmp275 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp276 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp277 $const38 $tmp276 	%argrw{"wrr"}
	compref		$tmp278 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp277 $tmp278 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp279 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp279 514 514 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___436_hsv2 ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:223
#     hsv[1] = tm * hsv[1] + t * hsv2[1];
	compref		$tmp280 ___435_hsv $const17 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{223} %argrw{"wrr"}
	mul		$tmp281 ___435_tm $tmp280 	%argrw{"wrr"}
	compref		$tmp282 ___436_hsv2 $const17 	%argrw{"wrr"}
	mul		$tmp283 t $tmp282 	%argrw{"wrr"}
	add		$tmp284 $tmp281 $tmp283 	%argrw{"wrr"}
	compassign	___435_hsv $const17 $tmp284 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:224
#     outcol = hsv_to_rgb(hsv);
	functioncall	$const40 561 	%line{224} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:130
#   h = hsv[0];
	compref		___370_h ___435_hsv $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{130} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:131
#   s = hsv[1];
	compref		___370_s ___435_hsv $const17 	%line{131} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:132
#   v = hsv[2];
	compref		___370_v ___435_hsv $const18 	%line{132} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:134
#   if (s == 0.0) {
	eq		$tmp285 ___370_s $const1 	%line{134} %argrw{"wrr"}
	if		$tmp285 528 560 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:135
#     rgb = color(v, v, v);
	color		___370_rgb ___370_v ___370_v ___370_v 	%line{135} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:138
#     if (h == 1.0) {
	eq		$tmp286 ___370_h $const2 	%line{138} %argrw{"wrr"}
	if		$tmp286 531 531 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:139
#       h = 0.0;
	assign		___370_h $const1 	%line{139} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:142
#     h *= 6.0;
	mul		___370_h ___370_h $const39 	%line{142} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:143
#     i = floor(h);
	floor		___370_i ___370_h 	%line{143} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:144
#     f = h - i;
	sub		___370_f ___370_h ___370_i 	%line{144} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:145
#     rgb = color(f, f, f);
	color		___370_rgb ___370_f ___370_f ___370_f 	%line{145} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:146
#     p = v * (1.0 - s);
	sub		$tmp287 $const2 ___370_s 	%line{146} %argrw{"wrr"}
	mul		___370_p ___370_v $tmp287 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:147
#     q = v * (1.0 - (s * f));
	mul		$tmp288 ___370_s ___370_f 	%line{147} %argrw{"wrr"}
	sub		$tmp289 $const2 $tmp288 	%argrw{"wrr"}
	mul		___370_q ___370_v $tmp289 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:148
#     t = v * (1.0 - (s * (1.0 - f)));
	sub		$tmp290 $const2 ___370_f 	%line{148} %argrw{"wrr"}
	mul		$tmp291 ___370_s $tmp290 	%argrw{"wrr"}
	sub		$tmp292 $const2 $tmp291 	%argrw{"wrr"}
	mul		___370_t ___370_v $tmp292 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:150
#     if (i == 0.0) {
	eq		$tmp293 ___370_i $const1 	%line{150} %argrw{"wrr"}
	if		$tmp293 547 560 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:151
#       rgb = color(v, t, p);
	color		___370_rgb ___370_v ___370_t ___370_p 	%line{151} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:153
#     else if (i == 1.0) {
	eq		$tmp294 ___370_i $const2 	%line{153} %argrw{"wrr"}
	if		$tmp294 550 560 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:154
#       rgb = color(q, v, p);
	color		___370_rgb ___370_q ___370_v ___370_p 	%line{154} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:156
#     else if (i == 2.0) {
	eq		$tmp295 ___370_i $const16 	%line{156} %argrw{"wrr"}
	if		$tmp295 553 560 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:157
#       rgb = color(p, v, t);
	color		___370_rgb ___370_p ___370_v ___370_t 	%line{157} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:159
#     else if (i == 3.0) {
	eq		$tmp296 ___370_i $const41 	%line{159} %argrw{"wrr"}
	if		$tmp296 556 560 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:160
#       rgb = color(p, q, v);
	color		___370_rgb ___370_p ___370_q ___370_v 	%line{160} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:162
#     else if (i == 4.0) {
	eq		$tmp297 ___370_i $const38 	%line{162} %argrw{"wrr"}
	if		$tmp297 559 560 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:163
#       rgb = color(t, p, v);
	color		___370_rgb ___370_t ___370_p ___370_v 	%line{163} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:166
#       rgb = color(v, p, q);
	color		___370_rgb ___370_v ___370_p ___370_q 	%line{166} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:170
#   return rgb;
	assign		___435_outcol ___370_rgb 	%line{170} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:227
#   return outcol;
	assign		Result ___435_outcol 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{227} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:49
#   if (blend_type == "value")
	eq		$tmp298 blend_type $const44 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{49} %argrw{"wrr"}
	if		$tmp298 704 704 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:50
#     Result = node_mix_val(t, A, B);
	functioncall	$const45 704 	%line{50} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:232
#   float tm = 1.0 - t;
	sub		___437_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{232} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:234
#   color hsv = rgb_to_hsv(col1);
	functioncall	$const37 612 	%line{234} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp299 A $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp301 A $const17 	%argrw{"wrr"}
	compref		$tmp302 A $const18 	%argrw{"wrr"}
	max		$tmp300 $tmp301 $tmp302 	%argrw{"wrr"}
	max		___361_cmax $tmp299 $tmp300 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp303 A $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp305 A $const17 	%argrw{"wrr"}
	compref		$tmp306 A $const18 	%argrw{"wrr"}
	min		$tmp304 $tmp305 $tmp306 	%argrw{"wrr"}
	min		___361_cmin $tmp303 $tmp304 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp307 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp307 582 584 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp308 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp308 587 611 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp309 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp310 $tmp309 A 	%argrw{"wrr"}
	div		___361_c $tmp310 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp311 A $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp312 $tmp311 ___361_cmax 	%argrw{"wrr"}
	if		$tmp312 596 607 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp313 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp314 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp313 $tmp314 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp315 A $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp316 $tmp315 ___361_cmax 	%argrw{"wrr"}
	if		$tmp316 603 607 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp317 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp318 $const16 $tmp317 	%argrw{"wrr"}
	compref		$tmp319 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp318 $tmp319 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp320 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp321 $const38 $tmp320 	%argrw{"wrr"}
	compref		$tmp322 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp321 $tmp322 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp323 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp323 611 611 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___437_hsv ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:235
#   color hsv2 = rgb_to_hsv(col2);
	functioncall	$const37 658 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{235} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp324 B $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp326 B $const17 	%argrw{"wrr"}
	compref		$tmp327 B $const18 	%argrw{"wrr"}
	max		$tmp325 $tmp326 $tmp327 	%argrw{"wrr"}
	max		___361_cmax $tmp324 $tmp325 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp328 B $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp330 B $const17 	%argrw{"wrr"}
	compref		$tmp331 B $const18 	%argrw{"wrr"}
	min		$tmp329 $tmp330 $tmp331 	%argrw{"wrr"}
	min		___361_cmin $tmp328 $tmp329 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp332 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp332 628 630 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp333 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp333 633 657 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp334 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp335 $tmp334 B 	%argrw{"wrr"}
	div		___361_c $tmp335 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp336 B $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp337 $tmp336 ___361_cmax 	%argrw{"wrr"}
	if		$tmp337 642 653 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp338 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp339 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp338 $tmp339 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp340 B $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp341 $tmp340 ___361_cmax 	%argrw{"wrr"}
	if		$tmp341 649 653 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp342 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp343 $const16 $tmp342 	%argrw{"wrr"}
	compref		$tmp344 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp343 $tmp344 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp345 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp346 $const38 $tmp345 	%argrw{"wrr"}
	compref		$tmp347 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp346 $tmp347 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp348 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp348 657 657 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___437_hsv2 ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:237
#   hsv[2] = tm * hsv[2] + t * hsv2[2];
	compref		$tmp349 ___437_hsv $const18 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{237} %argrw{"wrr"}
	mul		$tmp350 ___437_tm $tmp349 	%argrw{"wrr"}
	compref		$tmp351 ___437_hsv2 $const18 	%argrw{"wrr"}
	mul		$tmp352 t $tmp351 	%argrw{"wrr"}
	add		$tmp353 $tmp350 $tmp352 	%argrw{"wrr"}
	compassign	___437_hsv $const18 $tmp353 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:239
#   return hsv_to_rgb(hsv);
	functioncall	$const40 704 	%line{239} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:130
#   h = hsv[0];
	compref		___370_h ___437_hsv $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{130} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:131
#   s = hsv[1];
	compref		___370_s ___437_hsv $const17 	%line{131} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:132
#   v = hsv[2];
	compref		___370_v ___437_hsv $const18 	%line{132} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:134
#   if (s == 0.0) {
	eq		$tmp354 ___370_s $const1 	%line{134} %argrw{"wrr"}
	if		$tmp354 671 703 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:135
#     rgb = color(v, v, v);
	color		___370_rgb ___370_v ___370_v ___370_v 	%line{135} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:138
#     if (h == 1.0) {
	eq		$tmp355 ___370_h $const2 	%line{138} %argrw{"wrr"}
	if		$tmp355 674 674 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:139
#       h = 0.0;
	assign		___370_h $const1 	%line{139} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:142
#     h *= 6.0;
	mul		___370_h ___370_h $const39 	%line{142} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:143
#     i = floor(h);
	floor		___370_i ___370_h 	%line{143} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:144
#     f = h - i;
	sub		___370_f ___370_h ___370_i 	%line{144} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:145
#     rgb = color(f, f, f);
	color		___370_rgb ___370_f ___370_f ___370_f 	%line{145} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:146
#     p = v * (1.0 - s);
	sub		$tmp356 $const2 ___370_s 	%line{146} %argrw{"wrr"}
	mul		___370_p ___370_v $tmp356 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:147
#     q = v * (1.0 - (s * f));
	mul		$tmp357 ___370_s ___370_f 	%line{147} %argrw{"wrr"}
	sub		$tmp358 $const2 $tmp357 	%argrw{"wrr"}
	mul		___370_q ___370_v $tmp358 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:148
#     t = v * (1.0 - (s * (1.0 - f)));
	sub		$tmp359 $const2 ___370_f 	%line{148} %argrw{"wrr"}
	mul		$tmp360 ___370_s $tmp359 	%argrw{"wrr"}
	sub		$tmp361 $const2 $tmp360 	%argrw{"wrr"}
	mul		___370_t ___370_v $tmp361 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:150
#     if (i == 0.0) {
	eq		$tmp362 ___370_i $const1 	%line{150} %argrw{"wrr"}
	if		$tmp362 690 703 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:151
#       rgb = color(v, t, p);
	color		___370_rgb ___370_v ___370_t ___370_p 	%line{151} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:153
#     else if (i == 1.0) {
	eq		$tmp363 ___370_i $const2 	%line{153} %argrw{"wrr"}
	if		$tmp363 693 703 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:154
#       rgb = color(q, v, p);
	color		___370_rgb ___370_q ___370_v ___370_p 	%line{154} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:156
#     else if (i == 2.0) {
	eq		$tmp364 ___370_i $const16 	%line{156} %argrw{"wrr"}
	if		$tmp364 696 703 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:157
#       rgb = color(p, v, t);
	color		___370_rgb ___370_p ___370_v ___370_t 	%line{157} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:159
#     else if (i == 3.0) {
	eq		$tmp365 ___370_i $const41 	%line{159} %argrw{"wrr"}
	if		$tmp365 699 703 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:160
#       rgb = color(p, q, v);
	color		___370_rgb ___370_p ___370_q ___370_v 	%line{160} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:162
#     else if (i == 4.0) {
	eq		$tmp366 ___370_i $const38 	%line{162} %argrw{"wrr"}
	if		$tmp366 702 703 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:163
#       rgb = color(t, p, v);
	color		___370_rgb ___370_t ___370_p ___370_v 	%line{163} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:166
#       rgb = color(v, p, q);
	color		___370_rgb ___370_v ___370_p ___370_q 	%line{166} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:170
#   return rgb;
	assign		Result ___370_rgb 	%line{170} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:51
#   if (blend_type == "color")
	eq		$tmp367 blend_type $const46 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{51} %argrw{"wrr"}
	if		$tmp367 849 849 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:52
#     Result = node_mix_color(t, A, B);
	functioncall	$const47 849 	%line{52} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:244
#   color outcol = col1;
	assign		___438_outcol A 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{244} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:245
#   color hsv2 = rgb_to_hsv(col2);
	functioncall	$const37 754 	%line{245} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp368 B $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp370 B $const17 	%argrw{"wrr"}
	compref		$tmp371 B $const18 	%argrw{"wrr"}
	max		$tmp369 $tmp370 $tmp371 	%argrw{"wrr"}
	max		___361_cmax $tmp368 $tmp369 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp372 B $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp374 B $const17 	%argrw{"wrr"}
	compref		$tmp375 B $const18 	%argrw{"wrr"}
	min		$tmp373 $tmp374 $tmp375 	%argrw{"wrr"}
	min		___361_cmin $tmp372 $tmp373 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp376 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp376 724 726 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp377 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp377 729 753 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp378 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp379 $tmp378 B 	%argrw{"wrr"}
	div		___361_c $tmp379 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp380 B $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp381 $tmp380 ___361_cmax 	%argrw{"wrr"}
	if		$tmp381 738 749 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp382 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp383 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp382 $tmp383 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp384 B $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp385 $tmp384 ___361_cmax 	%argrw{"wrr"}
	if		$tmp385 745 749 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp386 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp387 $const16 $tmp386 	%argrw{"wrr"}
	compref		$tmp388 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp387 $tmp388 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp389 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp390 $const38 $tmp389 	%argrw{"wrr"}
	compref		$tmp391 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp390 $tmp391 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp392 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp392 753 753 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___438_hsv2 ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:247
#   if (hsv2[1] != 0.0) {
	compref		$tmp393 ___438_hsv2 $const17 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{247} %argrw{"wrr"}
	neq		$tmp394 $tmp393 $const1 	%argrw{"wrr"}
	if		$tmp394 848 848 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:248
#     color hsv = rgb_to_hsv(outcol);
	functioncall	$const37 803 	%line{248} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:85
#   cmax = max(rgb[0], max(rgb[1], rgb[2]));
	compref		$tmp395 ___438_outcol $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{85} %argrw{"wrr"}
	compref		$tmp397 ___438_outcol $const17 	%argrw{"wrr"}
	compref		$tmp398 ___438_outcol $const18 	%argrw{"wrr"}
	max		$tmp396 $tmp397 $tmp398 	%argrw{"wrr"}
	max		___361_cmax $tmp395 $tmp396 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:86
#   cmin = min(rgb[0], min(rgb[1], rgb[2]));
	compref		$tmp399 ___438_outcol $const14 	%line{86} %argrw{"wrr"}
	compref		$tmp401 ___438_outcol $const17 	%argrw{"wrr"}
	compref		$tmp402 ___438_outcol $const18 	%argrw{"wrr"}
	min		$tmp400 $tmp401 $tmp402 	%argrw{"wrr"}
	min		___361_cmin $tmp399 $tmp400 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:87
#   cdelta = cmax - cmin;
	sub		___361_cdelta ___361_cmax ___361_cmin 	%line{87} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:89
#   v = cmax;
	assign		___361_v ___361_cmax 	%line{89} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:91
#   if (cmax != 0.0) {
	neq		$tmp403 ___361_cmax $const1 	%line{91} %argrw{"wrr"}
	if		$tmp403 773 775 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:92
#     s = cdelta / cmax;
	div		___361_s ___361_cdelta ___361_cmax 	%line{92} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:95
#     s = 0.0;
	assign		___361_s $const1 	%line{95} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:96
#     h = 0.0;
	assign		___361_h $const1 	%line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:99
#   if (s == 0.0) {
	eq		$tmp404 ___361_s $const1 	%line{99} %argrw{"wrr"}
	if		$tmp404 778 802 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:100
#     h = 0.0;
	assign		___361_h $const1 	%line{100} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:103
#     c = (color(cmax, cmax, cmax) - rgb) / cdelta;
	color		$tmp405 ___361_cmax ___361_cmax ___361_cmax 	%line{103} %argrw{"wrrr"}
	sub		$tmp406 $tmp405 ___438_outcol 	%argrw{"wrr"}
	div		___361_c $tmp406 ___361_cdelta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:105
#     if (rgb[0] == cmax) {
	compref		$tmp407 ___438_outcol $const14 	%line{105} %argrw{"wrr"}
	eq		$tmp408 $tmp407 ___361_cmax 	%argrw{"wrr"}
	if		$tmp408 787 798 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:106
#       h = c[2] - c[1];
	compref		$tmp409 ___361_c $const18 	%line{106} %argrw{"wrr"}
	compref		$tmp410 ___361_c $const17 	%argrw{"wrr"}
	sub		___361_h $tmp409 $tmp410 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:108
#     else if (rgb[1] == cmax) {
	compref		$tmp411 ___438_outcol $const17 	%line{108} %argrw{"wrr"}
	eq		$tmp412 $tmp411 ___361_cmax 	%argrw{"wrr"}
	if		$tmp412 794 798 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:109
#       h = 2.0 + c[0] - c[2];
	compref		$tmp413 ___361_c $const14 	%line{109} %argrw{"wrr"}
	add		$tmp414 $const16 $tmp413 	%argrw{"wrr"}
	compref		$tmp415 ___361_c $const18 	%argrw{"wrr"}
	sub		___361_h $tmp414 $tmp415 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:112
#       h = 4.0 + c[1] - c[0];
	compref		$tmp416 ___361_c $const17 	%line{112} %argrw{"wrr"}
	add		$tmp417 $const38 $tmp416 	%argrw{"wrr"}
	compref		$tmp418 ___361_c $const14 	%argrw{"wrr"}
	sub		___361_h $tmp417 $tmp418 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:115
#     h /= 6.0;
	div		___361_h ___361_h $const39 	%line{115} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:117
#     if (h < 0.0) {
	lt		$tmp419 ___361_h $const1 	%line{117} %argrw{"wrr"}
	if		$tmp419 802 802 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:118
#       h += 1.0;
	add		___361_h ___361_h $const2 	%line{118} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:122
#   return color(h, s, v);
	color		___439_hsv ___361_h ___361_s ___361_v 	%line{122} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:249
#     hsv[0] = hsv2[0];
	compref		$tmp420 ___438_hsv2 $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{249} %argrw{"wrr"}
	compassign	___439_hsv $const14 $tmp420 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:250
#     hsv[1] = hsv2[1];
	compref		$tmp421 ___438_hsv2 $const17 	%line{250} %argrw{"wrr"}
	compassign	___439_hsv $const17 $tmp421 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:251
#     color tmp = hsv_to_rgb(hsv);
	functioncall	$const40 847 	%line{251} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:130
#   h = hsv[0];
	compref		___370_h ___439_hsv $const14 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{130} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:131
#   s = hsv[1];
	compref		___370_s ___439_hsv $const17 	%line{131} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:132
#   v = hsv[2];
	compref		___370_v ___439_hsv $const18 	%line{132} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:134
#   if (s == 0.0) {
	eq		$tmp422 ___370_s $const1 	%line{134} %argrw{"wrr"}
	if		$tmp422 814 846 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:135
#     rgb = color(v, v, v);
	color		___370_rgb ___370_v ___370_v ___370_v 	%line{135} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:138
#     if (h == 1.0) {
	eq		$tmp423 ___370_h $const2 	%line{138} %argrw{"wrr"}
	if		$tmp423 817 817 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:139
#       h = 0.0;
	assign		___370_h $const1 	%line{139} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:142
#     h *= 6.0;
	mul		___370_h ___370_h $const39 	%line{142} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:143
#     i = floor(h);
	floor		___370_i ___370_h 	%line{143} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:144
#     f = h - i;
	sub		___370_f ___370_h ___370_i 	%line{144} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:145
#     rgb = color(f, f, f);
	color		___370_rgb ___370_f ___370_f ___370_f 	%line{145} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:146
#     p = v * (1.0 - s);
	sub		$tmp424 $const2 ___370_s 	%line{146} %argrw{"wrr"}
	mul		___370_p ___370_v $tmp424 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:147
#     q = v * (1.0 - (s * f));
	mul		$tmp425 ___370_s ___370_f 	%line{147} %argrw{"wrr"}
	sub		$tmp426 $const2 $tmp425 	%argrw{"wrr"}
	mul		___370_q ___370_v $tmp426 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:148
#     t = v * (1.0 - (s * (1.0 - f)));
	sub		$tmp427 $const2 ___370_f 	%line{148} %argrw{"wrr"}
	mul		$tmp428 ___370_s $tmp427 	%argrw{"wrr"}
	sub		$tmp429 $const2 $tmp428 	%argrw{"wrr"}
	mul		___370_t ___370_v $tmp429 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:150
#     if (i == 0.0) {
	eq		$tmp430 ___370_i $const1 	%line{150} %argrw{"wrr"}
	if		$tmp430 833 846 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:151
#       rgb = color(v, t, p);
	color		___370_rgb ___370_v ___370_t ___370_p 	%line{151} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:153
#     else if (i == 1.0) {
	eq		$tmp431 ___370_i $const2 	%line{153} %argrw{"wrr"}
	if		$tmp431 836 846 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:154
#       rgb = color(q, v, p);
	color		___370_rgb ___370_q ___370_v ___370_p 	%line{154} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:156
#     else if (i == 2.0) {
	eq		$tmp432 ___370_i $const16 	%line{156} %argrw{"wrr"}
	if		$tmp432 839 846 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:157
#       rgb = color(p, v, t);
	color		___370_rgb ___370_p ___370_v ___370_t 	%line{157} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:159
#     else if (i == 3.0) {
	eq		$tmp433 ___370_i $const41 	%line{159} %argrw{"wrr"}
	if		$tmp433 842 846 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:160
#       rgb = color(p, q, v);
	color		___370_rgb ___370_p ___370_q ___370_v 	%line{160} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:162
#     else if (i == 4.0) {
	eq		$tmp434 ___370_i $const38 	%line{162} %argrw{"wrr"}
	if		$tmp434 845 846 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:163
#       rgb = color(t, p, v);
	color		___370_rgb ___370_t ___370_p ___370_v 	%line{163} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:166
#       rgb = color(v, p, q);
	color		___370_rgb ___370_v ___370_p ___370_q 	%line{166} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:170
#   return rgb;
	assign		___439_tmp ___370_rgb 	%line{170} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:253
#     outcol = mix(outcol, tmp, t);
	mix		___438_outcol ___438_outcol ___439_tmp t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{253} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:256
#   return outcol;
	assign		Result ___438_outcol 	%line{256} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:53
#   if (blend_type == "soft_light")
	eq		$tmp435 blend_type $const48 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{53} %argrw{"wrr"}
	if		$tmp435 866 866 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:54
#     Result = node_mix_soft(t, A, B);
	functioncall	$const49 866 	%line{54} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:261
#   float tm = 1.0 - t;
	sub		___440_tm $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{261} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:263
#   color one = color(1.0);
	assign		___440_one $const11 	%line{263} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:264
#   color scr = one - (one - col2) * (one - col1);
	sub		$tmp436 ___440_one B 	%line{264} %argrw{"wrr"}
	sub		$tmp437 ___440_one A 	%argrw{"wrr"}
	mul		$tmp438 $tmp436 $tmp437 	%argrw{"wrr"}
	sub		___440_scr ___440_one $tmp438 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:266
#   return tm * col1 + t * ((one - col1) * col2 * col1 + col1 * scr);
	mul		$tmp439 ___440_tm A 	%line{266} %argrw{"wrr"}
	sub		$tmp440 ___440_one A 	%argrw{"wrr"}
	mul		$tmp441 $tmp440 B 	%argrw{"wrr"}
	mul		$tmp442 $tmp441 A 	%argrw{"wrr"}
	mul		$tmp443 A ___440_scr 	%argrw{"wrr"}
	add		$tmp444 $tmp442 $tmp443 	%argrw{"wrr"}
	mul		$tmp445 t $tmp444 	%argrw{"wrr"}
	add		Result $tmp439 $tmp445 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:55
#   if (blend_type == "linear_light")
	eq		$tmp446 blend_type $const50 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{55} %argrw{"wrr"}
	if		$tmp446 922 922 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:56
#     Result = node_mix_linear(t, A, B);
	functioncall	$const51 922 	%line{56} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:271
#   color outcol = col1;
	assign		___441_outcol A 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h"} %line{271} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:273
#   if (col2[0] > 0.5) {
	compref		$tmp447 B $const14 	%line{273} %argrw{"wrr"}
	gt		$tmp448 $tmp447 $const15 	%argrw{"wrr"}
	if		$tmp448 880 887 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:274
#     outcol[0] = col1[0] + t * (2.0 * (col2[0] - 0.5));
	compref		$tmp449 A $const14 	%line{274} %argrw{"wrr"}
	compref		$tmp450 B $const14 	%argrw{"wrr"}
	sub		$tmp451 $tmp450 $const15 	%argrw{"wrr"}
	mul		$tmp452 $const16 $tmp451 	%argrw{"wrr"}
	mul		$tmp453 t $tmp452 	%argrw{"wrr"}
	add		$tmp454 $tmp449 $tmp453 	%argrw{"wrr"}
	compassign	___441_outcol $const14 $tmp454 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:277
#     outcol[0] = col1[0] + t * (2.0 * (col2[0]) - 1.0);
	compref		$tmp455 A $const14 	%line{277} %argrw{"wrr"}
	compref		$tmp456 B $const14 	%argrw{"wrr"}
	mul		$tmp457 $const16 $tmp456 	%argrw{"wrr"}
	sub		$tmp458 $tmp457 $const2 	%argrw{"wrr"}
	mul		$tmp459 t $tmp458 	%argrw{"wrr"}
	add		$tmp460 $tmp455 $tmp459 	%argrw{"wrr"}
	compassign	___441_outcol $const14 $tmp460 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:280
#   if (col2[1] > 0.5) {
	compref		$tmp461 B $const17 	%line{280} %argrw{"wrr"}
	gt		$tmp462 $tmp461 $const15 	%argrw{"wrr"}
	if		$tmp462 897 904 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:281
#     outcol[1] = col1[1] + t * (2.0 * (col2[1] - 0.5));
	compref		$tmp463 A $const17 	%line{281} %argrw{"wrr"}
	compref		$tmp464 B $const17 	%argrw{"wrr"}
	sub		$tmp465 $tmp464 $const15 	%argrw{"wrr"}
	mul		$tmp466 $const16 $tmp465 	%argrw{"wrr"}
	mul		$tmp467 t $tmp466 	%argrw{"wrr"}
	add		$tmp468 $tmp463 $tmp467 	%argrw{"wrr"}
	compassign	___441_outcol $const17 $tmp468 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:284
#     outcol[1] = col1[1] + t * (2.0 * (col2[1]) - 1.0);
	compref		$tmp469 A $const17 	%line{284} %argrw{"wrr"}
	compref		$tmp470 B $const17 	%argrw{"wrr"}
	mul		$tmp471 $const16 $tmp470 	%argrw{"wrr"}
	sub		$tmp472 $tmp471 $const2 	%argrw{"wrr"}
	mul		$tmp473 t $tmp472 	%argrw{"wrr"}
	add		$tmp474 $tmp469 $tmp473 	%argrw{"wrr"}
	compassign	___441_outcol $const17 $tmp474 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:287
#   if (col2[2] > 0.5) {
	compref		$tmp475 B $const18 	%line{287} %argrw{"wrr"}
	gt		$tmp476 $tmp475 $const15 	%argrw{"wrr"}
	if		$tmp476 914 921 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:288
#     outcol[2] = col1[2] + t * (2.0 * (col2[2] - 0.5));
	compref		$tmp477 A $const18 	%line{288} %argrw{"wrr"}
	compref		$tmp478 B $const18 	%argrw{"wrr"}
	sub		$tmp479 $tmp478 $const15 	%argrw{"wrr"}
	mul		$tmp480 $const16 $tmp479 	%argrw{"wrr"}
	mul		$tmp481 t $tmp480 	%argrw{"wrr"}
	add		$tmp482 $tmp477 $tmp481 	%argrw{"wrr"}
	compassign	___441_outcol $const18 $tmp482 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:291
#     outcol[2] = col1[2] + t * (2.0 * (col2[2]) - 1.0);
	compref		$tmp483 A $const18 	%line{291} %argrw{"wrr"}
	compref		$tmp484 B $const18 	%argrw{"wrr"}
	mul		$tmp485 $const16 $tmp484 	%argrw{"wrr"}
	sub		$tmp486 $tmp485 $const2 	%argrw{"wrr"}
	mul		$tmp487 t $tmp486 	%argrw{"wrr"}
	add		$tmp488 $tmp483 $tmp487 	%argrw{"wrr"}
	compassign	___441_outcol $const18 $tmp488 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color_blend.h:294
#   return outcol;
	assign		Result ___441_outcol 	%line{294} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:58
#   if (use_clamp_result)
	if		use_clamp_result 928 928 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl"} %line{58} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_color.osl:59
#     Result = clamp(Result, 0.0, 1.0);
	assign		$tmp489 $const1 	%line{59} %argrw{"wr"}
	assign		$tmp490 $const2 	%argrw{"wr"}
	functioncall	$const3 928 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:140
# color  clamp (color x, color minval, color maxval) { return max(min(x,maxval),minval); }
	min		$tmp491 Result $tmp490 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{140} %argrw{"wrr"}
	max		Result $tmp491 $tmp489 	%argrw{"wrr"}
	end
