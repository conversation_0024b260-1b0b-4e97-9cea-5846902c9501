OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_convert_from_vector.oso
shader node_convert_from_vector
param	vector	value_vector	0 0 0		%read{0,23} %write{2147483647,-1}
oparam	string	value_string	""		%read{2147483647,-1} %write{2147483647,-1}
oparam	float	value_float	0		%read{2147483647,-1} %write{5,5}
oparam	int	value_int	0		%read{2147483647,-1} %write{12,12}
oparam	color	value_color	0 0 0		%read{2147483647,-1} %write{16,16}
oparam	point	value_point	0 0 0		%read{2147483647,-1} %write{20,20}
oparam	normal	value_normal	0 0 0		%read{6,9} %write{24,24}
const	int	$const1	0		%read{0,21} %write{2147483647,-1}
temp	float	$tmp1	%read{2,2} %write{0,0}
const	int	$const2	1		%read{1,22} %write{2147483647,-1}
temp	float	$tmp2	%read{2,2} %write{1,1}
temp	float	$tmp3	%read{4,4} %write{2,2}
const	int	$const3	2		%read{3,23} %write{2147483647,-1}
temp	float	$tmp4	%read{4,4} %write{3,3}
temp	float	$tmp5	%read{5,5} %write{4,4}
const	float	$const4	0.333333343		%read{5,11} %write{2147483647,-1}
temp	float	$tmp6	%read{8,8} %write{6,6}
temp	float	$tmp7	%read{8,8} %write{7,7}
temp	float	$tmp8	%read{10,10} %write{8,8}
temp	float	$tmp9	%read{10,10} %write{9,9}
temp	float	$tmp10	%read{11,11} %write{10,10}
temp	float	$tmp11	%read{12,12} %write{11,11}
temp	float	$tmp12	%read{16,16} %write{13,13}
temp	float	$tmp13	%read{16,16} %write{14,14}
temp	float	$tmp14	%read{16,16} %write{15,15}
temp	float	$tmp15	%read{20,20} %write{17,17}
temp	float	$tmp16	%read{20,20} %write{18,18}
temp	float	$tmp17	%read{20,20} %write{19,19}
temp	float	$tmp18	%read{24,24} %write{21,21}
temp	float	$tmp19	%read{24,24} %write{22,22}
temp	float	$tmp20	%read{24,24} %write{23,23}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_vector.osl:15
#   value_float = (value_vector[0] + value_vector[1] + value_vector[2]) * (1.0 / 3.0);
	compref		$tmp1 value_vector $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_vector.osl"} %line{15} %argrw{"wrr"}
	compref		$tmp2 value_vector $const2 	%argrw{"wrr"}
	add		$tmp3 $tmp1 $tmp2 	%argrw{"wrr"}
	compref		$tmp4 value_vector $const3 	%argrw{"wrr"}
	add		$tmp5 $tmp3 $tmp4 	%argrw{"wrr"}
	mul		value_float $tmp5 $const4 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_vector.osl:16
#   value_int = (int)((value_normal[0] + value_normal[1] + value_normal[2]) * (1.0 / 3.0));
	compref		$tmp6 value_normal $const1 	%line{16} %argrw{"wrr"}
	compref		$tmp7 value_normal $const2 	%argrw{"wrr"}
	add		$tmp8 $tmp6 $tmp7 	%argrw{"wrr"}
	compref		$tmp9 value_normal $const3 	%argrw{"wrr"}
	add		$tmp10 $tmp8 $tmp9 	%argrw{"wrr"}
	mul		$tmp11 $tmp10 $const4 	%argrw{"wrr"}
	assign		value_int $tmp11 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_vector.osl:17
#   value_color = color(value_vector[0], value_vector[1], value_vector[2]);
	compref		$tmp12 value_vector $const1 	%line{17} %argrw{"wrr"}
	compref		$tmp13 value_vector $const2 	%argrw{"wrr"}
	compref		$tmp14 value_vector $const3 	%argrw{"wrr"}
	color		value_color $tmp12 $tmp13 $tmp14 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_vector.osl:18
#   value_point = point(value_vector[0], value_vector[1], value_vector[2]);
	compref		$tmp15 value_vector $const1 	%line{18} %argrw{"wrr"}
	compref		$tmp16 value_vector $const2 	%argrw{"wrr"}
	compref		$tmp17 value_vector $const3 	%argrw{"wrr"}
	point		value_point $tmp15 $tmp16 $tmp17 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_vector.osl:19
#   value_normal = normal(value_vector[0], value_vector[1], value_vector[2]);
	compref		$tmp18 value_vector $const1 	%line{19} %argrw{"wrr"}
	compref		$tmp19 value_vector $const2 	%argrw{"wrr"}
	compref		$tmp20 value_vector $const3 	%argrw{"wrr"}
	normal		value_normal $tmp18 $tmp19 $tmp20 	%argrw{"wrrr"}
	end
