OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_light_path.oso
shader node_light_path
oparam	float	IsCameraRay	0		%read{2147483647,-1} %write{1,1}
oparam	float	IsShadowRay	0		%read{2147483647,-1} %write{3,3}
oparam	float	IsDiffuseRay	0		%read{2147483647,-1} %write{5,5}
oparam	float	IsGlossyRay	0		%read{2147483647,-1} %write{7,7}
oparam	float	IsSingularRay	0		%read{2147483647,-1} %write{9,9}
oparam	float	IsReflectionRay	0		%read{2147483647,-1} %write{11,11}
oparam	float	IsTransmissionRay	0		%read{2147483647,-1} %write{13,13}
oparam	float	IsVolumeScatterRay	0		%read{2147483647,-1} %write{15,15}
oparam	float	RayLength	0		%read{2147483647,-1} %write{16,16}
oparam	float	RayDepth	0		%read{2147483647,-1} %write{19,19}
oparam	float	DiffuseDepth	0		%read{2147483647,-1} %write{22,22}
oparam	float	GlossyDepth	0		%read{2147483647,-1} %write{25,25}
oparam	float	TransparentDepth	0		%read{2147483647,-1} %write{28,28}
oparam	float	TransmissionDepth	0		%read{2147483647,-1} %write{31,31}
local	int	ray_depth	%read{19,19} %write{17,18}
local	int	diffuse_depth	%read{22,22} %write{20,21}
local	int	glossy_depth	%read{25,25} %write{23,24}
local	int	transparent_depth	%read{28,28} %write{26,27}
local	int	transmission_depth	%read{31,31} %write{29,30}
temp	int	$tmp1	%read{1,1} %write{0,0}
const	string	$const1	"camera"		%read{0,0} %write{2147483647,-1}
temp	int	$tmp2	%read{3,3} %write{2,2}
const	string	$const2	"shadow"		%read{2,2} %write{2147483647,-1}
temp	int	$tmp3	%read{5,5} %write{4,4}
const	string	$const3	"diffuse"		%read{4,4} %write{2147483647,-1}
temp	int	$tmp4	%read{7,7} %write{6,6}
const	string	$const4	"glossy"		%read{6,6} %write{2147483647,-1}
temp	int	$tmp5	%read{9,9} %write{8,8}
const	string	$const5	"singular"		%read{8,8} %write{2147483647,-1}
temp	int	$tmp6	%read{11,11} %write{10,10}
const	string	$const6	"reflection"		%read{10,10} %write{2147483647,-1}
temp	int	$tmp7	%read{13,13} %write{12,12}
const	string	$const7	"refraction"		%read{12,12} %write{2147483647,-1}
temp	int	$tmp8	%read{15,15} %write{14,14}
const	string	$const8	"volume_scatter"		%read{14,14} %write{2147483647,-1}
temp	int	$tmp9	%read{2147483647,-1} %write{16,16}
const	string	$const9	"path:ray_length"		%read{16,16} %write{2147483647,-1}
const	int	$const10	0		%read{17,29} %write{2147483647,-1}
temp	int	$tmp10	%read{2147483647,-1} %write{18,18}
const	string	$const11	"path:ray_depth"		%read{18,18} %write{2147483647,-1}
temp	int	$tmp11	%read{2147483647,-1} %write{21,21}
const	string	$const12	"path:diffuse_depth"		%read{21,21} %write{2147483647,-1}
temp	int	$tmp12	%read{2147483647,-1} %write{24,24}
const	string	$const13	"path:glossy_depth"		%read{24,24} %write{2147483647,-1}
temp	int	$tmp13	%read{2147483647,-1} %write{27,27}
const	string	$const14	"path:transparent_depth"		%read{27,27} %write{2147483647,-1}
temp	int	$tmp14	%read{2147483647,-1} %write{30,30}
const	string	$const15	"path:transmission_depth"		%read{30,30} %write{2147483647,-1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:22
#   IsCameraRay = raytype("camera");
	raytype		$tmp1 $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl"} %line{22} %argrw{"wr"}
	assign		IsCameraRay $tmp1 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:23
#   IsShadowRay = raytype("shadow");
	raytype		$tmp2 $const2 	%line{23} %argrw{"wr"}
	assign		IsShadowRay $tmp2 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:24
#   IsDiffuseRay = raytype("diffuse");
	raytype		$tmp3 $const3 	%line{24} %argrw{"wr"}
	assign		IsDiffuseRay $tmp3 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:25
#   IsGlossyRay = raytype("glossy");
	raytype		$tmp4 $const4 	%line{25} %argrw{"wr"}
	assign		IsGlossyRay $tmp4 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:26
#   IsSingularRay = raytype("singular");
	raytype		$tmp5 $const5 	%line{26} %argrw{"wr"}
	assign		IsSingularRay $tmp5 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:27
#   IsReflectionRay = raytype("reflection");
	raytype		$tmp6 $const6 	%line{27} %argrw{"wr"}
	assign		IsReflectionRay $tmp6 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:28
#   IsTransmissionRay = raytype("refraction");
	raytype		$tmp7 $const7 	%line{28} %argrw{"wr"}
	assign		IsTransmissionRay $tmp7 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:29
#   IsVolumeScatterRay = raytype("volume_scatter");
	raytype		$tmp8 $const8 	%line{29} %argrw{"wr"}
	assign		IsVolumeScatterRay $tmp8 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:31
#   getattribute("path:ray_length", RayLength);
	getattribute	$tmp9 $const9 RayLength 	%line{31} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:33
#   int ray_depth = 0;
	assign		ray_depth $const10 	%line{33} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:34
#   getattribute("path:ray_depth", ray_depth);
	getattribute	$tmp10 $const11 ray_depth 	%line{34} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:35
#   RayDepth = (float)ray_depth;
	assign		RayDepth ray_depth 	%line{35} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:37
#   int diffuse_depth = 0;
	assign		diffuse_depth $const10 	%line{37} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:38
#   getattribute("path:diffuse_depth", diffuse_depth);
	getattribute	$tmp11 $const12 diffuse_depth 	%line{38} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:39
#   DiffuseDepth = (float)diffuse_depth;
	assign		DiffuseDepth diffuse_depth 	%line{39} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:41
#   int glossy_depth = 0;
	assign		glossy_depth $const10 	%line{41} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:42
#   getattribute("path:glossy_depth", glossy_depth);
	getattribute	$tmp12 $const13 glossy_depth 	%line{42} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:43
#   GlossyDepth = (float)glossy_depth;
	assign		GlossyDepth glossy_depth 	%line{43} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:45
#   int transparent_depth = 0;
	assign		transparent_depth $const10 	%line{45} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:46
#   getattribute("path:transparent_depth", transparent_depth);
	getattribute	$tmp13 $const14 transparent_depth 	%line{46} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:47
#   TransparentDepth = (float)transparent_depth;
	assign		TransparentDepth transparent_depth 	%line{47} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:49
#   int transmission_depth = 0;
	assign		transmission_depth $const10 	%line{49} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:50
#   getattribute("path:transmission_depth", transmission_depth);
	getattribute	$tmp14 $const15 transmission_depth 	%line{50} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_path.osl:51
#   TransmissionDepth = (float)transmission_depth;
	assign		TransmissionDepth transmission_depth 	%line{51} %argrw{"wr"}
	end
