OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.oso
shader node_diffuse_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{4,8} %write{2147483647,-1}
param	float	Roughness	0		%read{1,10} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{3,10} %write{0,0} %initexpr
oparam	closure color	BSDF			%read{2147483647,-1} %write{4,10}
global	normal	N	%read{0,0} %write{2147483647,-1}
const	float	$const1	0		%read{1,5} %write{2147483647,-1}
temp	int	$tmp1	%read{2,2} %write{1,1}
temp	closure color	$tmp2	%read{4,4} %write{3,3}
const	string	$const2	"diffuse"		%read{3,3} %write{2147483647,-1}
temp	color	$tmp3	%read{10,10} %write{9,9}
temp	color	$tmp4	%read{9,9} %write{5,5}
const	float	$const3	1		%read{6,6} %write{2147483647,-1}
temp	color	$tmp5	%read{8,8} %write{6,6}
const	string	$const4	"clamp"		%read{7,7} %write{2147483647,-1}
temp	color	$tmp6	%read{9,9} %write{8,8}
const	string	$const5	"oren_nayar_diffuse_bsdf"		%read{10,10} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl:9
#                          normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl"} %line{9} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl:12
#   if (Roughness == 0.0)
	eq		$tmp1 Roughness $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl"} %line{12} %argrw{"wrr"}
	if		$tmp1 5 11 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl:13
#     BSDF = Color * diffuse(Normal);
	closure		$tmp2 $const2 Normal 	%line{13} %argrw{"wrr"}
	mul		BSDF $tmp2 Color 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl:15
#     BSDF = oren_nayar_diffuse_bsdf(Normal, clamp(Color, 0.0, 1.0), Roughness);
	assign		$tmp4 $const1 	%line{15} %argrw{"wr"}
	assign		$tmp5 $const3 	%argrw{"wr"}
	functioncall	$const4 10 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:140
# color  clamp (color x, color minval, color maxval) { return max(min(x,maxval),minval); }
	min		$tmp6 Color $tmp5 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{140} %argrw{"wrr"}
	max		$tmp3 $tmp6 $tmp4 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl:15
#     BSDF = oren_nayar_diffuse_bsdf(Normal, clamp(Color, 0.0, 1.0), Roughness);
	closure		BSDF $const5 Normal $tmp3 Roughness 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_diffuse_bsdf.osl"} %line{15} %argrw{"wrrrr"}
	end
