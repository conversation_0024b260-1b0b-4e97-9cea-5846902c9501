OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.oso
shader node_ambient_occlusion
param	color	ColorIn	1 1 1		%read{16,16} %write{2147483647,-1}
param	int	samples	16		%read{10,10} %write{2147483647,-1}
param	float	Distance	1		%read{1,15} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{9,9} %write{0,0} %initexpr
param	int	inside	0		%read{14,14} %write{2147483647,-1}
param	int	only_local	0		%read{15,15} %write{2147483647,-1}
oparam	color	ColorOut	1 1 1		%read{2147483647,-1} %write{16,16}
oparam	float	AO	1		%read{16,16} %write{15,15}
global	normal	N	%read{0,0} %write{2147483647,-1}
local	int	global_radius	%read{15,15} %write{8,8}
local	normal	normalized_normal	%read{11,13} %write{9,9}
const	float	$const1	0		%read{1,1} %write{2147483647,-1}
temp	int	$tmp1	%read{2,2} %write{1,1}
temp	int	$tmp2	%read{3,8} %write{2,7}
const	int	$const2	0		%read{2,11} %write{2147483647,-1}
temp	int	$tmp3	%read{5,5} %write{4,4}
temp	int	$tmp4	%read{6,6} %write{5,5}
temp	int	$tmp5	%read{7,7} %write{6,6}
const	string	$const3	"@ao"		%read{15,15} %write{2147483647,-1}
temp	float	$tmp6	%read{15,15} %write{10,10}
temp	float	$tmp7	%read{15,15} %write{11,11}
const	int	$const4	1		%read{12,12} %write{2147483647,-1}
temp	float	$tmp8	%read{15,15} %write{12,12}
const	int	$const5	2		%read{13,13} %write{2147483647,-1}
temp	float	$tmp9	%read{15,15} %write{13,13}
temp	float	$tmp10	%read{15,15} %write{14,14}
const	string	$const6	"sblur"		%read{15,15} %write{2147483647,-1}
const	string	$const7	"tblur"		%read{15,15} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:10
#                               normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl"} %line{10} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:16
#   int global_radius = (Distance == 0.0 && !isconnected(Distance));
	eq		$tmp1 Distance $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl"} %line{16} %argrw{"wrr"}
	neq		$tmp2 $tmp1 $const2 	%argrw{"wrr"}
	if		$tmp2 8 8 	%argrw{"r"}
	isconnected	$tmp3 Distance 	%argrw{"wr"}
	eq		$tmp4 $tmp3 $const2 	%argrw{"wrr"}
	neq		$tmp5 $tmp4 $const2 	%argrw{"wrr"}
	assign		$tmp2 $tmp5 	%argrw{"wr"}
	assign		global_radius $tmp2 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:18
#   normal normalized_normal = normalize(Normal);
	normalize	normalized_normal Normal 	%line{18} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:21
#   AO = texture("@ao",
	assign		$tmp6 samples 	%line{21} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:24
#                normalized_normal[0],
	compref		$tmp7 normalized_normal $const2 	%line{24} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:25
#                normalized_normal[1],
	compref		$tmp8 normalized_normal $const4 	%line{25} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:26
#                normalized_normal[2],
	compref		$tmp9 normalized_normal $const5 	%line{26} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:21
#   AO = texture("@ao",
	assign		$tmp10 inside 	%line{21} %argrw{"wr"}
	texture		AO $const3 $tmp6 Distance $tmp7 $tmp8 $tmp9 $tmp10 $const6 only_local $const7 global_radius 	%argrw{"wrrrrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ambient_occlusion.osl:32
#   ColorOut = ColorIn * AO;
	mul		ColorOut ColorIn AO 	%line{32} %argrw{"wrr"}
	end
