OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_ies_light.oso
shader node_ies_light
param	int	use_mapping	0		%read{2,2} %write{2147483647,-1}
param	matrix	mapping	0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0		%read{3,3} %write{2147483647,-1} %derivs
param	string	filename	""		%read{12,12} %write{2147483647,-1}
param	float	Strength	1		%read{13,13} %write{2147483647,-1}
param	point	Vector	0 0 0		%read{1,1} %write{0,0} %derivs %initexpr
oparam	float	Fac	0		%read{2147483647,-1} %write{13,13}
global	vector	I	%read{0,0} %write{2147483647,-1} %derivs
local	point	p	%read{3,9} %write{1,4} %derivs
local	float	v_angle	%read{12,12} %write{7,7} %derivs
local	float	h_angle	%read{12,12} %write{11,11} %derivs
const	int	$const1	2		%read{5,5} %write{2147483647,-1}
temp	float	$tmp1	%read{6,6} %write{5,5} %derivs
temp	float	$tmp2	%read{7,7} %write{6,6} %derivs
temp	float	$tmp3	%read{11,11} %write{10,10} %derivs
const	int	$const2	0		%read{8,8} %write{2147483647,-1}
temp	float	$tmp4	%read{10,10} %write{8,8} %derivs
const	int	$const3	1		%read{9,9} %write{2147483647,-1}
temp	float	$tmp5	%read{10,10} %write{9,9} %derivs
const	float	$const4	3.14159274		%read{11,11} %write{2147483647,-1}
temp	float	$tmp6	%read{13,13} %write{12,12}
code Vector
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:13
#                       point Vector = I,
	assign		Vector I 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl"} %line{13} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:16
#   point p = Vector;
	assign		p Vector 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl"} %line{16} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:18
#   if (use_mapping) {
	if		use_mapping 4 4 	%line{18} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:19
#     p = transform(mapping, p);
	transform	p mapping p 	%line{19} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:22
#   p = normalize((vector)p);
	normalize	p p 	%line{22} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:24
#   float v_angle = acos(-p[2]);
	compref		$tmp1 p $const1 	%line{24} %argrw{"wrr"}
	neg		$tmp2 $tmp1 	%argrw{"wr"}
	acos		v_angle $tmp2 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:25
#   float h_angle = atan2(p[0], p[1]) + M_PI;
	compref		$tmp4 p $const2 	%line{25} %argrw{"wrr"}
	compref		$tmp5 p $const3 	%argrw{"wrr"}
	atan2		$tmp3 $tmp4 $tmp5 	%argrw{"wrr"}
	add		h_angle $tmp3 $const4 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ies_light.osl:27
#   Fac = Strength * texture(filename, h_angle, v_angle);
	texture		$tmp6 filename h_angle v_angle 	%line{27} %argrw{"wrrr"} %argderivs{2,3}
	mul		Fac Strength $tmp6 	%argrw{"wrr"}
	end
