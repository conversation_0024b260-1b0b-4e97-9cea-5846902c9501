OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_mix_closure.oso
shader node_mix_closure
param	float	Fac	0.5		%read{1,1} %write{2147483647,-1}
param	closure color	Closure1			%read{4,4} %write{2147483647,-1}
param	closure color	Closure2			%read{5,5} %write{2147483647,-1}
oparam	closure color	Closure			%read{2147483647,-1} %write{6,6}
local	float	t	%read{3,5} %write{2,2}
const	float	$const1	0		%read{2,2} %write{2147483647,-1}
const	float	$const2	1		%read{1,3} %write{2147483647,-1}
const	string	$const3	"clamp"		%read{0,0} %write{2147483647,-1}
temp	float	$tmp1	%read{2,2} %write{1,1}
temp	float	$tmp2	%read{4,4} %write{3,3}
temp	closure color	$tmp3	%read{6,6} %write{4,4}
temp	closure color	$tmp4	%read{6,6} %write{5,5}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_closure.osl:12
#   float t = clamp(Fac, 0.0, 1.0);
	functioncall	$const3 3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_closure.osl"} %line{12} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp1 Fac $const2 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		t $tmp1 $const1 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_closure.osl:13
#   Closure = (1.0 - t) * Closure1 + t * Closure2;
	sub		$tmp2 $const2 t 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_mix_closure.osl"} %line{13} %argrw{"wrr"}
	mul		$tmp3 Closure1 $tmp2 	%argrw{"wrr"}
	mul		$tmp4 Closure2 t 	%argrw{"wrr"}
	add		Closure $tmp3 $tmp4 	%argrw{"wrr"}
	end
