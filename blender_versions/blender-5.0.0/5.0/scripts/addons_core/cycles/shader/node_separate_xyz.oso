OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_separate_xyz.oso
shader node_separate_xyz
param	vector	Vector	0.800000012 0.800000012 0.800000012		%read{0,2} %write{2147483647,-1}
oparam	float	X	0		%read{2147483647,-1} %write{0,0}
oparam	float	Y	0		%read{2147483647,-1} %write{1,1}
oparam	float	Z	0		%read{2147483647,-1} %write{2,2}
const	int	$const1	0		%read{0,0} %write{2147483647,-1}
const	int	$const2	1		%read{1,1} %write{2147483647,-1}
const	int	$const3	2		%read{2,2} %write{2147483647,-1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_separate_xyz.osl:12
#   X = Vector[0];
	compref		X Vector $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_separate_xyz.osl"} %line{12} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_separate_xyz.osl:13
#   Y = Vector[1];
	compref		Y Vector $const2 	%line{13} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_separate_xyz.osl:14
#   Z = Vector[2];
	compref		Z Vector $const3 	%line{14} %argrw{"wrr"}
	end
