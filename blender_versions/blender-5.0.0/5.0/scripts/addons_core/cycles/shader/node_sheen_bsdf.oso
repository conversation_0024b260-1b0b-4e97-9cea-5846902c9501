OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.oso
shader node_sheen_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	string	distribution	"microfiber"		%read{5,9} %write{2147483647,-1}
param	float	Roughness	0		%read{3,3} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{7,11} %write{0,0} %initexpr
oparam	closure color	BSDF			%read{2147483647,-1} %write{8,12}
global	normal	N	%read{0,0} %write{2147483647,-1}
local	color	base_color	%read{8,12} %write{1,1}
local	float	roughness	%read{7,11} %write{4,4}
const	color	$const1	0 0 0		%read{1,1} %write{2147483647,-1}
const	float	$const2	0		%read{4,4} %write{2147483647,-1}
const	float	$const3	1		%read{3,3} %write{2147483647,-1}
const	string	$const4	"clamp"		%read{2,2} %write{2147483647,-1}
temp	float	$tmp2	%read{4,4} %write{3,3}
const	string	$const5	"ashikhmin"		%read{5,5} %write{2147483647,-1}
temp	int	$tmp3	%read{6,6} %write{5,5}
temp	closure color	$tmp4	%read{8,8} %write{7,7}
const	string	$const6	"ashikhmin_velvet"		%read{7,7} %write{2147483647,-1}
const	string	$const7	"microfiber"		%read{9,9} %write{2147483647,-1}
temp	int	$tmp5	%read{10,10} %write{9,9}
temp	closure color	$tmp6	%read{12,12} %write{11,11}
const	string	$const8	"sheen"		%read{11,11} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:11
#                        normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl"} %line{11} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:14
#   color base_color = max(Color, color(0.0));
	max		base_color Color $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl"} %line{14} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:15
#   float roughness = clamp(Roughness, 0.0, 1.0);
	functioncall	$const4 5 	%line{15} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp2 Roughness $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		roughness $tmp2 $const2 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:17
#   if (distribution == "ashikhmin")
	eq		$tmp3 distribution $const5 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl"} %line{17} %argrw{"wrr"}
	if		$tmp3 9 13 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:18
#     BSDF = base_color * ashikhmin_velvet(Normal, roughness);
	closure		$tmp4 $const6 Normal roughness 	%line{18} %argrw{"wrrr"}
	mul		BSDF $tmp4 base_color 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:19
#   else if (distribution == "microfiber")
	eq		$tmp5 distribution $const7 	%line{19} %argrw{"wrr"}
	if		$tmp5 13 13 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sheen_bsdf.osl:20
#     BSDF = base_color * sheen(Normal, roughness);
	closure		$tmp6 $const8 Normal roughness 	%line{20} %argrw{"wrrr"}
	mul		BSDF $tmp6 base_color 	%argrw{"wrr"}
	end
