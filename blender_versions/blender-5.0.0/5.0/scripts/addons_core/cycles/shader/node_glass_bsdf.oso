OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_glass_bsdf.oso
shader node_glass_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	string	distribution	"ggx"		%read{18,18} %write{2147483647,-1}
param	float	Roughness	0.200000003		%read{3,3} %write{2147483647,-1}
param	float	IOR	1.45000005		%read{6,6} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{18,18} %write{0,0} %initexpr
oparam	closure color	BSDF			%read{2147483647,-1} %write{18,18}
global	normal	N	%read{0,0} %write{2147483647,-1}
local	float	___349_f0	%read{14,14} %write{13,13}
local	color	base_color	%read{18,18} %write{1,1}
local	float	r2	%read{5,18} %write{4,5}
local	float	eta	%read{9,17} %write{6,9}
local	color	F0	%read{18,18} %write{15,15}
local	color	F90	%read{18,18} %write{16,16}
const	color	$const1	0 0 0		%read{1,1} %write{2147483647,-1}
const	float	$const2	0		%read{4,4} %write{2147483647,-1}
const	float	$const3	1		%read{3,12} %write{2147483647,-1}
const	string	$const4	"clamp"		%read{2,2} %write{2147483647,-1}
temp	float	$tmp2	%read{4,4} %write{3,3}
const	float	$const5	9.99999975e-06		%read{6,6} %write{2147483647,-1}
temp	int	$tmp3	%read{8,8} %write{7,7}
temp	float	$tmp4	%read{15,15} %write{14,14}
const	string	$const6	"F0_from_ior"		%read{10,10} %write{2147483647,-1}
temp	float	$tmp5	%read{13,13} %write{11,11}
temp	float	$tmp6	%read{13,13} %write{12,12}
const	color	$const7	1 1 1		%read{16,16} %write{2147483647,-1}
const	vector	$const8	0 0 0		%read{18,18} %write{2147483647,-1}
temp	float	$tmp8	%read{18,18} %write{17,17}
const	string	$const9	"generalized_schlick_bsdf"		%read{18,18} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:12
#                        normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl"} %line{12} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:15
#   color base_color = max(Color, color(0.0));
	max		base_color Color $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl"} %line{15} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:16
#   float r2 = clamp(Roughness, 0.0, 1.0);
	functioncall	$const4 5 	%line{16} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp2 Roughness $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		r2 $tmp2 $const2 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:17
#   r2 = r2 * r2;
	mul		r2 r2 r2 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl"} %line{17} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:18
#   float eta = max(IOR, 1e-5);
	max		eta IOR $const5 	%line{18} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:19
#   eta = backfacing() ? 1.0 / eta : eta;
	backfacing	$tmp3 	%line{19} %argrw{"w"}
	if		$tmp3 10 10 	%argrw{"r"}
	div		eta $const3 eta 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:20
#   color F0 = F0_from_ior(eta);
	functioncall	$const6 15 	%line{20} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_fresnel.h:42
#   float f0 = (eta - 1.0) / (eta + 1.0);
	sub		$tmp5 eta $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_fresnel.h"} %line{42} %argrw{"wrr"}
	add		$tmp6 eta $const3 	%argrw{"wrr"}
	div		___349_f0 $tmp5 $tmp6 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_fresnel.h:43
#   return f0 * f0;
	mul		$tmp4 ___349_f0 ___349_f0 	%line{43} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:20
#   color F0 = F0_from_ior(eta);
	assign		F0 $tmp4 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl"} %line{20} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:21
#   color F90 = color(1.0);
	assign		F90 $const7 	%line{21} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:24
#       Normal, vector(0.0), base_color, base_color, r2, r2, F0, F90, -eta, distribution);
	neg		$tmp8 eta 	%line{24} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glass_bsdf.osl:23
#   BSDF = generalized_schlick_bsdf(
	closure		BSDF $const9 Normal $const8 base_color base_color r2 r2 F0 F90 $tmp8 distribution 	%line{23} %argrw{"wrrrrrrrrrrr"}
	end
