OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_sky_texture.oso
shader node_sky_texture
param	int	use_mapping	0		%read{2,2} %write{2147483647,-1}
param	matrix	mapping	0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0		%read{3,3} %write{2147483647,-1} %derivs
param	vector	Vector	0 0 0		%read{1,1} %write{0,0} %derivs %initexpr
param	string	sky_type	"nishita_improved"		%read{2147483647,-1} %write{2147483647,-1}
param	float	theta	0		%read{2147483647,-1} %write{2147483647,-1}
param	float	phi	0		%read{2147483647,-1} %write{2147483647,-1}
param	string	filename	""		%read{98,115} %write{2147483647,-1}
param	color	radiance	0 0 0		%read{2147483647,-1} %write{2147483647,-1}
param	float[9]	config_x	0 0 0 0 0 0 0 0 0		%read{2147483647,-1} %write{2147483647,-1}
param	float[9]	config_y	0 0 0 0 0 0 0 0 0		%read{2147483647,-1} %write{2147483647,-1}
param	float[9]	config_z	0 0 0 0 0 0 0 0 0		%read{2147483647,-1} %write{2147483647,-1}
param	float[10]	nishita_data	0 0 0 0 0 0 0 0 0 0		%read{5,60} %write{2147483647,-1} %derivs
oparam	color	Color	0 0 0		%read{2147483647,-1} %write{136,136}
global	point	P	%read{0,0} %write{2147483647,-1} %derivs
local	float	___414_sun_elevation	%read{23,76} %write{5,5}
local	float	___414_sun_rotation	%read{21,109} %write{6,6} %derivs
local	float	___414_angular_diameter	%read{9,69} %write{7,7}
local	float	___414_sun_intensity	%read{72,79} %write{8,8}
local	int	___414_sun_disc	%read{44,44} %write{9,9}
local	float	___414_alpha	%read{2147483647,-1} %write{10,115}
local	color	___414_xyz	%read{87,119} %write{72,116}
local	vector	___414_direction	%read{39,107} %write{17,17} %derivs
local	vector	___415_sun_dir	%read{32,34} %write{30,30}
local	float	___415_sun_dir_angle	%read{41,80} %write{37,37}
local	float	___415_half_angular	%read{41,80} %write{38,38}
local	float	___415_dir_elevation	%read{68,92} %write{40,40} %derivs
local	color	___416_pixel_bottom	%read{71,78} %write{57,57}
local	color	___416_pixel_top	%read{71,78} %write{61,61}
local	float	___416_y	%read{71,78} %write{70,77}
local	float	___416_angle_fraction	%read{81,81} %write{80,80}
local	float	___416_limb_darkening	%read{87,87} %write{86,86}
local	float	___421_x	%read{95,98} %write{91,97} %derivs
local	float	___421_y	%read{98,98} %write{94,94} %derivs
local	float	___425_mul	%read{116,116} %write{106,106}
local	float	___425_x	%read{112,115} %write{110,114} %derivs
local	float	___425_y	%read{115,115} %write{111,111} %derivs
local	vector	p	%read{3,103} %write{1,3} %derivs
const	string	$const1	"sky_radiance_nishita"		%read{4,4} %write{2147483647,-1}
const	int	$const2	6		%read{5,5} %write{2147483647,-1}
const	int	$const3	7		%read{6,6} %write{2147483647,-1}
const	int	$const4	8		%read{7,7} %write{2147483647,-1}
const	int	$const5	9		%read{8,8} %write{2147483647,-1}
const	int	$const6	0		%read{9,117} %write{2147483647,-1}
const	float	$const7	1		%read{10,114} %write{2147483647,-1}
const	string	$const8	"sky_spherical_coordinates"		%read{11,11} %write{2147483647,-1}
temp	float	$tmp1	%read{17,17} %write{13,13} %derivs
const	int	$const9	2		%read{12,119} %write{2147483647,-1}
temp	float	$tmp2	%read{13,13} %write{12,12} %derivs
temp	float	$tmp3	%read{17,17} %write{16,16} %derivs
temp	float	$tmp4	%read{16,16} %write{14,14} %derivs
const	int	$const10	1		%read{15,118} %write{2147483647,-1}
temp	float	$tmp5	%read{16,16} %write{15,15} %derivs
const	float	$const11	0		%read{17,74} %write{2147483647,-1}
temp	float	$tmp6	%read{19,19} %write{18,18}
temp	int	$tmp7	%read{20,20} %write{19,19}
const	float	$const12	1.57079637		%read{21,92} %write{2147483647,-1}
temp	float	$tmp8	%read{24,27} %write{21,21}
const	string	$const13	"geographical_to_direction"		%read{22,22} %write{2147483647,-1}
temp	float	$tmp9	%read{25,25} %write{23,23}
temp	float	$tmp10	%read{25,25} %write{24,24}
temp	float	$tmp11	%read{30,30} %write{25,25}
temp	float	$tmp12	%read{28,28} %write{26,26}
temp	float	$tmp13	%read{28,28} %write{27,27}
temp	float	$tmp14	%read{30,30} %write{28,28}
temp	float	$tmp15	%read{30,30} %write{29,29}
const	string	$const14	"precise_angle"		%read{31,31} %write{2147483647,-1}
const	float	$const15	2		%read{37,37} %write{2147483647,-1}
temp	float	$tmp16	%read{37,37} %write{36,36}
temp	float	$tmp17	%read{36,36} %write{33,33}
temp	vector	$tmp18	%read{33,33} %write{32,32}
temp	float	$tmp19	%read{36,36} %write{35,35}
temp	vector	$tmp20	%read{35,35} %write{34,34}
const	float	$const16	0.5		%read{38,70} %write{2147483647,-1}
temp	float	$tmp21	%read{40,40} %write{39,39} %derivs
temp	int	$tmp22	%read{42,42} %write{41,41}
temp	int	$tmp23	%read{43,47} %write{42,46}
temp	int	$tmp24	%read{45,45} %write{44,44}
temp	int	$tmp25	%read{46,46} %write{45,45}
temp	int	$tmp26	%read{48,53} %write{47,52}
temp	int	$tmp27	%read{50,50} %write{49,49}
const	string	$const17	"importance_bake"		%read{49,49} %write{2147483647,-1}
temp	int	$tmp28	%read{51,51} %write{50,50}
temp	int	$tmp29	%read{52,52} %write{51,51}
temp	float	$tmp30	%read{57,57} %write{54,54}
temp	float	$tmp31	%read{57,57} %write{55,55}
temp	float	$tmp32	%read{57,57} %write{56,56}
const	int	$const18	3		%read{58,58} %write{2147483647,-1}
temp	float	$tmp33	%read{61,61} %write{58,58}
const	int	$const19	4		%read{59,59} %write{2147483647,-1}
temp	float	$tmp34	%read{61,61} %write{59,59}
const	int	$const20	5		%read{60,60} %write{2147483647,-1}
temp	float	$tmp35	%read{61,61} %write{60,60}
temp	float	$tmp36	%read{63,63} %write{62,62}
temp	int	$tmp37	%read{64,64} %write{63,63}
temp	float	$tmp38	%read{66,66} %write{65,65}
temp	int	$tmp39	%read{67,67} %write{66,66}
temp	float	$tmp40	%read{69,69} %write{68,68}
temp	float	$tmp41	%read{70,70} %write{69,69}
temp	color	$tmp42	%read{72,72} %write{71,71}
temp	float	$tmp43	%read{74,74} %write{73,73}
temp	int	$tmp44	%read{75,75} %write{74,74}
temp	float	$tmp45	%read{77,77} %write{76,76}
temp	color	$tmp46	%read{79,79} %write{78,78}
const	float	$const21	0.600000024		%read{85,85} %write{2147483647,-1}
temp	float	$tmp47	%read{84,84} %write{83,83}
temp	float	$tmp48	%read{82,82} %write{81,81}
temp	float	$tmp49	%read{83,83} %write{82,82}
temp	float	$tmp50	%read{85,85} %write{84,84}
temp	float	$tmp51	%read{86,86} %write{85,85}
temp	float	$tmp52	%read{89,89} %write{88,88} %derivs
const	float	$const22	3.14159274		%read{89,108} %write{2147483647,-1}
temp	float	$tmp53	%read{90,90} %write{89,89} %derivs
temp	float	$tmp54	%read{91,91} %write{90,90} %derivs
const	float	$const23	6.28318548		%read{91,110} %write{2147483647,-1}
temp	float	$tmp55	%read{94,94} %write{93,93} %derivs
temp	float	$tmp56	%read{93,93} %write{92,92} %derivs
temp	int	$tmp57	%read{96,96} %write{95,95}
const	string	$const24	"wrap"		%read{98,115} %write{2147483647,-1}
const	string	$const25	"clamp"		%read{98,98} %write{2147483647,-1}
const	string	$const26	"interp"		%read{98,115} %write{2147483647,-1}
const	string	$const27	"linear"		%read{98,115} %write{2147483647,-1}
const	string	$const28	"alpha"		%read{98,115} %write{2147483647,-1}
temp	float	$tmp58	%read{100,100} %write{99,99}
const	float	$const29	-0.400000006		%read{100,100} %write{2147483647,-1}
temp	int	$tmp59	%read{101,101} %write{100,100}
const	color	$const30	0 0 0		%read{102,102} %write{2147483647,-1}
temp	float	$tmp60	%read{104,104} %write{103,103}
const	float	$const31	2.5		%read{104,104} %write{2147483647,-1}
temp	float	$tmp61	%read{105,105} %write{104,104}
temp	float	$tmp62	%read{106,106} %write{105,105}
const	float	$const32	3		%read{106,106} %write{2147483647,-1}
temp	float	$tmp63	%read{108,108} %write{107,107} %derivs
temp	float	$tmp64	%read{109,109} %write{108,108} %derivs
temp	float	$tmp65	%read{110,110} %write{109,109} %derivs
const	float	$const33	1.5		%read{111,111} %write{2147483647,-1}
temp	int	$tmp66	%read{113,113} %write{112,112}
temp	color	$tmp67	%read{116,116} %write{115,115}
const	string	$const34	"periodic"		%read{115,115} %write{2147483647,-1}
temp	float	$tmp68	%read{121,131} %write{117,117}
temp	float	$tmp69	%read{122,132} %write{118,118}
temp	float	$tmp70	%read{124,134} %write{119,119}
const	string	$const35	"xyz_to_rgb"		%read{120,120} %write{2147483647,-1}
const	float	$const36	3.24047899		%read{121,121} %write{2147483647,-1}
temp	float	$tmp71	%read{123,123} %write{121,121}
const	float	$const37	-1.53715003		%read{122,122} %write{2147483647,-1}
temp	float	$tmp72	%read{123,123} %write{122,122}
temp	float	$tmp73	%read{125,125} %write{123,123}
const	float	$const38	-0.498535007		%read{124,124} %write{2147483647,-1}
temp	float	$tmp74	%read{125,125} %write{124,124}
temp	float	$tmp75	%read{136,136} %write{125,125}
const	float	$const39	-0.969255984		%read{126,126} %write{2147483647,-1}
temp	float	$tmp76	%read{128,128} %write{126,126}
const	float	$const40	1.87599099		%read{127,127} %write{2147483647,-1}
temp	float	$tmp77	%read{128,128} %write{127,127}
temp	float	$tmp78	%read{130,130} %write{128,128}
const	float	$const41	0.0415560007		%read{129,129} %write{2147483647,-1}
temp	float	$tmp79	%read{130,130} %write{129,129}
temp	float	$tmp80	%read{136,136} %write{130,130}
const	float	$const42	0.055647999		%read{131,131} %write{2147483647,-1}
temp	float	$tmp81	%read{133,133} %write{131,131}
const	float	$const43	-0.204043001		%read{132,132} %write{2147483647,-1}
temp	float	$tmp82	%read{133,133} %write{132,132}
temp	float	$tmp83	%read{135,135} %write{133,133}
const	float	$const44	1.05731106		%read{134,134} %write{2147483647,-1}
temp	float	$tmp84	%read{135,135} %write{134,134}
temp	float	$tmp85	%read{136,136} %write{135,135}
code Vector
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:121
#     vector Vector = P,
	assign		Vector P 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl"} %line{121} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:133
#   vector p = Vector;
	assign		p Vector 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl"} %line{133} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:135
#   if (use_mapping)
	if		use_mapping 4 4 	%line{135} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:136
#     p = transform(mapping, p);
	transformv	p mapping p 	%line{136} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:138
#   Color = sky_radiance_nishita(p, nishita_data, filename);
	functioncall	$const1 137 	%line{138} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:39
#   float sun_elevation = nishita_data[6];
	aref		___414_sun_elevation nishita_data $const2 	%line{39} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:40
#   float sun_rotation = nishita_data[7];
	aref		___414_sun_rotation nishita_data $const3 	%line{40} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:41
#   float angular_diameter = nishita_data[8];
	aref		___414_angular_diameter nishita_data $const4 	%line{41} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:42
#   float sun_intensity = nishita_data[9];
	aref		___414_sun_intensity nishita_data $const5 	%line{42} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:43
#   int sun_disc = angular_diameter > 0;
	gt		___414_sun_disc ___414_angular_diameter $const6 	%line{43} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:44
#   float alpha = 1.0;
	assign		___414_alpha $const7 	%line{44} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:47
#   vector direction = sky_spherical_coordinates(dir);
	functioncall	$const8 18 	%line{47} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:22
#   return vector(acos(dir[2]), atan2(dir[0], dir[1]), 0);
	compref		$tmp2 p $const9 	%line{22} %argrw{"wrr"}
	acos		$tmp1 $tmp2 	%argrw{"wr"}
	compref		$tmp4 p $const6 	%argrw{"wrr"}
	compref		$tmp5 p $const10 	%argrw{"wrr"}
	atan2		$tmp3 $tmp4 $tmp5 	%argrw{"wrr"}
	vector		___414_direction $tmp1 $tmp3 $const11 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:50
#   if (dir[2] >= 0.0) {
	compref		$tmp6 p $const9 	%line{50} %argrw{"wrr"}
	ge		$tmp7 $tmp6 $const11 	%argrw{"wrr"}
	if		$tmp7 99 117 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:52
#     vector sun_dir = geographical_to_direction(sun_elevation, sun_rotation + M_PI_2);
	add		$tmp8 ___414_sun_rotation $const12 	%line{52} %argrw{"wrr"}
	functioncall	$const13 31 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:28
#   return vector(cos(lat) * cos(lon), cos(lat) * sin(lon), sin(lat));
	cos		$tmp9 ___414_sun_elevation 	%line{28} %argrw{"wr"}
	cos		$tmp10 $tmp8 	%argrw{"wr"}
	mul		$tmp11 $tmp9 $tmp10 	%argrw{"wrr"}
	cos		$tmp12 ___414_sun_elevation 	%argrw{"wr"}
	sin		$tmp13 $tmp8 	%argrw{"wr"}
	mul		$tmp14 $tmp12 $tmp13 	%argrw{"wrr"}
	sin		$tmp15 ___414_sun_elevation 	%argrw{"wr"}
	vector		___415_sun_dir $tmp11 $tmp14 $tmp15 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:53
#     float sun_dir_angle = precise_angle(dir, sun_dir);
	functioncall	$const14 38 	%line{53} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:33
#   return 2.0 * atan2(length(a - b), length(a + b));
	sub		$tmp18 p ___415_sun_dir 	%line{33} %argrw{"wrr"}
	length		$tmp17 $tmp18 	%argrw{"wr"}
	add		$tmp20 p ___415_sun_dir 	%argrw{"wrr"}
	length		$tmp19 $tmp20 	%argrw{"wr"}
	atan2		$tmp16 $tmp17 $tmp19 	%argrw{"wrr"}
	mul		___415_sun_dir_angle $const15 $tmp16 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:54
#     float half_angular = angular_diameter * 0.5;
	mul		___415_half_angular ___414_angular_diameter $const16 	%line{54} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:55
#     float dir_elevation = M_PI_2 - direction[0];
	compref		$tmp21 ___414_direction $const6 	%line{55} %argrw{"wrr"}
	sub		___415_dir_elevation $const12 $tmp21 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:59
#     if (sun_dir_angle < half_angular && sun_disc == 1 && raytype("importance_bake") != 1) {
	lt		$tmp22 ___415_sun_dir_angle ___415_half_angular 	%line{59} %argrw{"wrr"}
	neq		$tmp23 $tmp22 $const6 	%argrw{"wrr"}
	if		$tmp23 47 47 	%argrw{"r"}
	eq		$tmp24 ___414_sun_disc $const10 	%argrw{"wrr"}
	neq		$tmp25 $tmp24 $const6 	%argrw{"wrr"}
	assign		$tmp23 $tmp25 	%argrw{"wr"}
	neq		$tmp26 $tmp23 $const6 	%argrw{"wrr"}
	if		$tmp26 53 53 	%argrw{"r"}
	raytype		$tmp27 $const17 	%argrw{"wr"}
	neq		$tmp28 $tmp27 $const10 	%argrw{"wrr"}
	neq		$tmp29 $tmp28 $const6 	%argrw{"wrr"}
	assign		$tmp26 $tmp29 	%argrw{"wr"}
	if		$tmp26 88 99 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:61
#       color pixel_bottom = color(nishita_data[0], nishita_data[1], nishita_data[2]);
	aref		$tmp30 nishita_data $const6 	%line{61} %argrw{"wrr"}
	aref		$tmp31 nishita_data $const10 	%argrw{"wrr"}
	aref		$tmp32 nishita_data $const9 	%argrw{"wrr"}
	color		___416_pixel_bottom $tmp30 $tmp31 $tmp32 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:62
#       color pixel_top = color(nishita_data[3], nishita_data[4], nishita_data[5]);
	aref		$tmp33 nishita_data $const18 	%line{62} %argrw{"wrr"}
	aref		$tmp34 nishita_data $const19 	%argrw{"wrr"}
	aref		$tmp35 nishita_data $const20 	%argrw{"wrr"}
	color		___416_pixel_top $tmp33 $tmp34 $tmp35 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:66
#       if (sun_elevation - half_angular > 0.0) {
	sub		$tmp36 ___414_sun_elevation ___415_half_angular 	%line{66} %argrw{"wrr"}
	gt		$tmp37 $tmp36 $const11 	%argrw{"wrr"}
	if		$tmp37 73 80 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:67
#         if ((sun_elevation + half_angular) > 0.0) {
	add		$tmp38 ___414_sun_elevation ___415_half_angular 	%line{67} %argrw{"wrr"}
	gt		$tmp39 $tmp38 $const11 	%argrw{"wrr"}
	if		$tmp39 73 73 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:68
#           y = ((dir_elevation - sun_elevation) / angular_diameter) + 0.5;
	sub		$tmp40 ___415_dir_elevation ___414_sun_elevation 	%line{68} %argrw{"wrr"}
	div		$tmp41 $tmp40 ___414_angular_diameter 	%argrw{"wrr"}
	add		___416_y $tmp41 $const16 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:69
#           xyz = mix(pixel_bottom, pixel_top, y) * sun_intensity;
	mix		$tmp42 ___416_pixel_bottom ___416_pixel_top ___416_y 	%line{69} %argrw{"wrrr"}
	mul		___414_xyz $tmp42 ___414_sun_intensity 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:73
#         if (sun_elevation + half_angular > 0.0) {
	add		$tmp43 ___414_sun_elevation ___415_half_angular 	%line{73} %argrw{"wrr"}
	gt		$tmp44 $tmp43 $const11 	%argrw{"wrr"}
	if		$tmp44 80 80 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:74
#           y = dir_elevation / (sun_elevation + half_angular);
	add		$tmp45 ___414_sun_elevation ___415_half_angular 	%line{74} %argrw{"wrr"}
	div		___416_y ___415_dir_elevation $tmp45 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:75
#           xyz = mix(pixel_bottom, pixel_top, y) * sun_intensity;
	mix		$tmp46 ___416_pixel_bottom ___416_pixel_top ___416_y 	%line{75} %argrw{"wrrr"}
	mul		___414_xyz $tmp46 ___414_sun_intensity 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:79
#       float angle_fraction = sun_dir_angle / half_angular;
	div		___416_angle_fraction ___415_sun_dir_angle ___415_half_angular 	%line{79} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:80
#       float limb_darkening = (1.0 - 0.6 * (1.0 - sqrt(1.0 - angle_fraction * angle_fraction)));
	mul		$tmp48 ___416_angle_fraction ___416_angle_fraction 	%line{80} %argrw{"wrr"}
	sub		$tmp49 $const7 $tmp48 	%argrw{"wrr"}
	sqrt		$tmp47 $tmp49 	%argrw{"wr"}
	sub		$tmp50 $const7 $tmp47 	%argrw{"wrr"}
	mul		$tmp51 $const21 $tmp50 	%argrw{"wrr"}
	sub		___416_limb_darkening $const7 $tmp51 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:81
#       xyz *= limb_darkening;
	mul		___414_xyz ___414_xyz ___416_limb_darkening 	%line{81} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:86
#       float x = (direction[1] + M_PI + sun_rotation) / M_2PI;
	compref		$tmp52 ___414_direction $const10 	%line{86} %argrw{"wrr"}
	add		$tmp53 $tmp52 $const22 	%argrw{"wrr"}
	add		$tmp54 $tmp53 ___414_sun_rotation 	%argrw{"wrr"}
	div		___421_x $tmp54 $const23 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:88
#       float y = 1.0 - sqrt(dir_elevation / M_PI_2);
	div		$tmp56 ___415_dir_elevation $const12 	%line{88} %argrw{"wrr"}
	sqrt		$tmp55 $tmp56 	%argrw{"wr"}
	sub		___421_y $const7 $tmp55 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:89
#       if (x > 1.0) {
	gt		$tmp57 ___421_x $const7 	%line{89} %argrw{"wrr"}
	if		$tmp57 98 98 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:90
#         x = x - 1.0;
	sub		___421_x ___421_x $const7 	%line{90} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:92
#       xyz = (color)texture(filename, x, y, "wrap", "clamp", "interp", "linear", "alpha", alpha);
	texture		___414_xyz filename ___421_x ___421_y $const24 $const25 $const26 $const27 $const28 ___414_alpha 	%line{92} %argrw{"wrrrrrrrrw"} %argderivs{2,3}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:97
#     if (dir[2] < -0.4) {
	compref		$tmp58 p $const9 	%line{97} %argrw{"wrr"}
	lt		$tmp59 $tmp58 $const29 	%argrw{"wrr"}
	if		$tmp59 103 117 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:98
#       xyz = color(0, 0, 0);
	assign		___414_xyz $const30 	%line{98} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:102
#       float mul = pow(1.0 + dir[2] * 2.5, 3.0);
	compref		$tmp60 p $const9 	%line{102} %argrw{"wrr"}
	mul		$tmp61 $tmp60 $const31 	%argrw{"wrr"}
	add		$tmp62 $const7 $tmp61 	%argrw{"wrr"}
	pow		___425_mul $tmp62 $const32 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:104
#       float x = (direction[1] + M_PI + sun_rotation) / M_2PI;
	compref		$tmp63 ___414_direction $const10 	%line{104} %argrw{"wrr"}
	add		$tmp64 $tmp63 $const22 	%argrw{"wrr"}
	add		$tmp65 $tmp64 ___414_sun_rotation 	%argrw{"wrr"}
	div		___425_x $tmp65 $const23 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:105
#       float y = 1.5;
	assign		___425_y $const33 	%line{105} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:106
#       if (x > 1.0) {
	gt		$tmp66 ___425_x $const7 	%line{106} %argrw{"wrr"}
	if		$tmp66 115 115 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:107
#         x = x - 1.0;
	sub		___425_x ___425_x $const7 	%line{107} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:109
#       xyz = (color)texture(
	texture		$tmp67 filename ___425_x ___425_y $const24 $const34 $const26 $const27 $const28 ___414_alpha 	%line{109} %argrw{"wrrrrrrrrw"} %argderivs{2,3}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:110
#                 filename, x, y, "wrap", "periodic", "interp", "linear", "alpha", alpha) *
	mul		___414_xyz $tmp67 ___425_mul 	%line{110} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_sky_texture.osl:115
#   return xyz_to_rgb(xyz[0], xyz[1], xyz[2]);
	compref		$tmp68 ___414_xyz $const6 	%line{115} %argrw{"wrr"}
	compref		$tmp69 ___414_xyz $const10 	%argrw{"wrr"}
	compref		$tmp70 ___414_xyz $const9 	%argrw{"wrr"}
	functioncall	$const35 137 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:75
#   return color(3.240479 * x + -1.537150 * y + -0.498535 * z,
	mul		$tmp71 $const36 $tmp68 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{75} %argrw{"wrr"}
	mul		$tmp72 $const37 $tmp69 	%argrw{"wrr"}
	add		$tmp73 $tmp71 $tmp72 	%argrw{"wrr"}
	mul		$tmp74 $const38 $tmp70 	%argrw{"wrr"}
	add		$tmp75 $tmp73 $tmp74 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:76
#                -0.969256 * x + 1.875991 * y + 0.041556 * z,
	mul		$tmp76 $const39 $tmp68 	%line{76} %argrw{"wrr"}
	mul		$tmp77 $const40 $tmp69 	%argrw{"wrr"}
	add		$tmp78 $tmp76 $tmp77 	%argrw{"wrr"}
	mul		$tmp79 $const41 $tmp70 	%argrw{"wrr"}
	add		$tmp80 $tmp78 $tmp79 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:77
#                0.055648 * x + -0.204043 * y + 1.057311 * z);
	mul		$tmp81 $const42 $tmp68 	%line{77} %argrw{"wrr"}
	mul		$tmp82 $const43 $tmp69 	%argrw{"wrr"}
	add		$tmp83 $tmp81 $tmp82 	%argrw{"wrr"}
	mul		$tmp84 $const44 $tmp70 	%argrw{"wrr"}
	add		$tmp85 $tmp83 $tmp84 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:75
#   return color(3.240479 * x + -1.537150 * y + -0.498535 * z,
	color		Color $tmp75 $tmp80 $tmp85 	%line{75} %argrw{"wrrr"}
	end
