OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_vector_transform.oso
shader node_vector_transform
param	string	transform_type	"vector"		%read{0,11} %write{2147483647,-1}
param	string	convert_from	"world"		%read{7,14} %write{2147483647,-1}
param	string	convert_to	"object"		%read{7,14} %write{2147483647,-1}
param	vector	VectorIn	0 0 0		%read{7,13} %write{2147483647,-1}
oparam	vector	VectorOut	0 0 0		%read{10,10} %write{7,14}
local	point	___369_Point	%read{14,14} %write{13,13}
const	string	$const1	"vector"		%read{0,0} %write{2147483647,-1}
temp	int	$tmp1	%read{1,1} %write{0,0}
temp	int	$tmp2	%read{2,6} %write{1,5}
const	int	$const2	0		%read{1,4} %write{2147483647,-1}
const	string	$const3	"normal"		%read{3,8} %write{2147483647,-1}
temp	int	$tmp3	%read{4,4} %write{3,3}
temp	int	$tmp4	%read{5,5} %write{4,4}
temp	int	$tmp5	%read{9,9} %write{8,8}
const	string	$const4	"point"		%read{11,11} %write{2147483647,-1}
temp	int	$tmp6	%read{12,12} %write{11,11}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:13
#   if (transform_type == "vector" || transform_type == "normal") {
	eq		$tmp1 transform_type $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl"} %line{13} %argrw{"wrr"}
	neq		$tmp2 $tmp1 $const2 	%argrw{"wrr"}
	if		$tmp2 3 6 	%argrw{"r"}
	eq		$tmp3 transform_type $const3 	%argrw{"wrr"}
	neq		$tmp4 $tmp3 $const2 	%argrw{"wrr"}
	assign		$tmp2 $tmp4 	%argrw{"wr"}
	if		$tmp2 11 15 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:14
#     VectorOut = transform(convert_from, convert_to, VectorIn);
	transformv	VectorOut convert_from convert_to VectorIn 	%line{14} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:15
#     if (transform_type == "normal")
	eq		$tmp5 transform_type $const3 	%line{15} %argrw{"wrr"}
	if		$tmp5 11 11 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:16
#       VectorOut = normalize(VectorOut);
	normalize	VectorOut VectorOut 	%line{16} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:18
#   else if (transform_type == "point") {
	eq		$tmp6 transform_type $const4 	%line{18} %argrw{"wrr"}
	if		$tmp6 15 15 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:19
#     point Point = (point)VectorIn;
	assign		___369_Point VectorIn 	%line{19} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_transform.osl:20
#     VectorOut = transform(convert_from, convert_to, Point);
	transform	VectorOut convert_from convert_to ___369_Point 	%line{20} %argrw{"wrrr"}
	end
