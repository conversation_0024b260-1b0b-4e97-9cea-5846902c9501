OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_output_volume.oso
volume node_output_volume
param	closure color	Volume			%read{0,0} %write{2147483647,-1}
global	closure color	Ci	%read{2147483647,-1} %write{0,0}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_output_volume.osl:9
#   Ci = Volume;
	assign		Ci Volume 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_output_volume.osl"} %line{9} %argrw{"wr"}
	end
