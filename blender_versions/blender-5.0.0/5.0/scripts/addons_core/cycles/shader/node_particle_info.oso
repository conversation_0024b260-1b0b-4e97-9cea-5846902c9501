OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_particle_info.oso
shader node_particle_info
oparam	float	Index	0		%read{2147483647,-1} %write{2,2}
oparam	float	Random	0		%read{2147483647,-1} %write{3,3}
oparam	float	Age	0		%read{2147483647,-1} %write{4,4}
oparam	float	Lifetime	0		%read{2147483647,-1} %write{5,5}
oparam	point	Location	0 0 0		%read{2147483647,-1} %write{6,6}
oparam	float	Size	0		%read{2147483647,-1} %write{7,7}
oparam	vector	Velocity	0 0 0		%read{2147483647,-1} %write{0,8} %initexpr
oparam	vector	AngularVelocity	0 0 0		%read{2147483647,-1} %write{1,9} %initexpr
const	point	$const1	0 0 0		%read{0,1} %write{2147483647,-1}
temp	int	$tmp1	%read{2147483647,-1} %write{2,2}
const	string	$const2	"particle:index"		%read{2,2} %write{2147483647,-1}
temp	int	$tmp2	%read{2147483647,-1} %write{3,3}
const	string	$const3	"particle:random"		%read{3,3} %write{2147483647,-1}
temp	int	$tmp3	%read{2147483647,-1} %write{4,4}
const	string	$const4	"particle:age"		%read{4,4} %write{2147483647,-1}
temp	int	$tmp4	%read{2147483647,-1} %write{5,5}
const	string	$const5	"particle:lifetime"		%read{5,5} %write{2147483647,-1}
temp	int	$tmp5	%read{2147483647,-1} %write{6,6}
const	string	$const6	"particle:location"		%read{6,6} %write{2147483647,-1}
temp	int	$tmp6	%read{2147483647,-1} %write{7,7}
const	string	$const7	"particle:size"		%read{7,7} %write{2147483647,-1}
temp	int	$tmp7	%read{2147483647,-1} %write{8,8}
const	string	$const8	"particle:velocity"		%read{8,8} %write{2147483647,-1}
temp	int	$tmp8	%read{2147483647,-1} %write{9,9}
const	string	$const9	"particle:angular_velocity"		%read{9,9} %write{2147483647,-1}
code Velocity
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:13
#                           output vector Velocity = point(0.0, 0.0, 0.0),
	assign		Velocity $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl"} %line{13} %argrw{"wr"}
code AngularVelocity
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:14
#                           output vector AngularVelocity = point(0.0, 0.0, 0.0))
	assign		AngularVelocity $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl"} %line{14} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:16
#   getattribute("particle:index", Index);
	getattribute	$tmp1 $const2 Index 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl"} %line{16} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:17
#   getattribute("particle:random", Random);
	getattribute	$tmp2 $const3 Random 	%line{17} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:18
#   getattribute("particle:age", Age);
	getattribute	$tmp3 $const4 Age 	%line{18} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:19
#   getattribute("particle:lifetime", Lifetime);
	getattribute	$tmp4 $const5 Lifetime 	%line{19} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:20
#   getattribute("particle:location", Location);
	getattribute	$tmp5 $const6 Location 	%line{20} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:21
#   getattribute("particle:size", Size);
	getattribute	$tmp6 $const7 Size 	%line{21} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:22
#   getattribute("particle:velocity", Velocity);
	getattribute	$tmp7 $const8 Velocity 	%line{22} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_particle_info.osl:23
#   getattribute("particle:angular_velocity", AngularVelocity);
	getattribute	$tmp8 $const9 AngularVelocity 	%line{23} %argrw{"wrw"}
	end
