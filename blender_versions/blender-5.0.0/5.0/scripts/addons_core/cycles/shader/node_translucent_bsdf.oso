OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_translucent_bsdf.oso
shader node_translucent_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{2,2} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{1,1} %write{0,0} %initexpr
oparam	closure color	BSDF			%read{2147483647,-1} %write{2,2}
global	normal	N	%read{0,0} %write{2147483647,-1}
temp	closure color	$tmp1	%read{2,2} %write{1,1}
const	string	$const1	"translucent"		%read{1,1} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_translucent_bsdf.osl:7
# shader node_translucent_bsdf(color Color = 0.8, normal Normal = N, output closure color BSDF = 0)
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_translucent_bsdf.osl"} %line{7} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_translucent_bsdf.osl:9
#   BSDF = Color * translucent(Normal);
	closure		$tmp1 $const1 Normal 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_translucent_bsdf.osl"} %line{9} %argrw{"wrr"}
	mul		BSDF $tmp1 Color 	%argrw{"wrr"}
	end
