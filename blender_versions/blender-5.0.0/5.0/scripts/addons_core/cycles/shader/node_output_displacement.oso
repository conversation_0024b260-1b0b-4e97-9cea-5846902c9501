OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_output_displacement.oso
displacement node_output_displacement
param	vector	Displacement	0 0 0		%read{0,0} %write{2147483647,-1}
global	point	P	%read{0,0} %write{0,0}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_output_displacement.osl:9
#   P += Displacement;
	add		P P Displacement 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_output_displacement.osl"} %line{9} %argrw{"wrr"}
	end
