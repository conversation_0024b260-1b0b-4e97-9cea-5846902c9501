OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_convert_from_color.oso
shader node_convert_from_color
param	color	value_color	0 0 0		%read{0,27} %write{2147483647,-1}
oparam	string	value_string	""		%read{2147483647,-1} %write{2147483647,-1}
oparam	float	value_float	0		%read{2147483647,-1} %write{7,7}
oparam	int	value_int	0		%read{2147483647,-1} %write{16,16}
oparam	vector	value_vector	0 0 0		%read{2147483647,-1} %write{20,20}
oparam	point	value_point	0 0 0		%read{2147483647,-1} %write{24,24}
oparam	normal	value_normal	0 0 0		%read{2147483647,-1} %write{28,28}
const	int	$const1	0		%read{0,25} %write{2147483647,-1}
temp	float	$tmp1	%read{1,1} %write{0,0}
const	float	$const2	0.212599993		%read{1,9} %write{2147483647,-1}
temp	float	$tmp2	%read{4,4} %write{1,1}
const	int	$const3	1		%read{2,26} %write{2147483647,-1}
temp	float	$tmp3	%read{3,3} %write{2,2}
const	float	$const4	0.715200007		%read{3,11} %write{2147483647,-1}
temp	float	$tmp4	%read{4,4} %write{3,3}
temp	float	$tmp5	%read{7,7} %write{4,4}
const	int	$const5	2		%read{5,27} %write{2147483647,-1}
temp	float	$tmp6	%read{6,6} %write{5,5}
const	float	$const6	0.0722000003		%read{6,14} %write{2147483647,-1}
temp	float	$tmp7	%read{7,7} %write{6,6}
temp	float	$tmp8	%read{9,9} %write{8,8}
temp	float	$tmp9	%read{12,12} %write{9,9}
temp	float	$tmp10	%read{11,11} %write{10,10}
temp	float	$tmp11	%read{12,12} %write{11,11}
temp	float	$tmp12	%read{15,15} %write{12,12}
temp	float	$tmp13	%read{14,14} %write{13,13}
temp	float	$tmp14	%read{15,15} %write{14,14}
temp	float	$tmp15	%read{16,16} %write{15,15}
temp	float	$tmp16	%read{20,20} %write{17,17}
temp	float	$tmp17	%read{20,20} %write{18,18}
temp	float	$tmp18	%read{20,20} %write{19,19}
temp	float	$tmp19	%read{24,24} %write{21,21}
temp	float	$tmp20	%read{24,24} %write{22,22}
temp	float	$tmp21	%read{24,24} %write{23,23}
temp	float	$tmp22	%read{28,28} %write{25,25}
temp	float	$tmp23	%read{28,28} %write{26,26}
temp	float	$tmp24	%read{28,28} %write{27,27}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_color.osl:15
#   value_float = value_color[0] * 0.2126 + value_color[1] * 0.7152 + value_color[2] * 0.0722;
	compref		$tmp1 value_color $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_color.osl"} %line{15} %argrw{"wrr"}
	mul		$tmp2 $tmp1 $const2 	%argrw{"wrr"}
	compref		$tmp3 value_color $const3 	%argrw{"wrr"}
	mul		$tmp4 $tmp3 $const4 	%argrw{"wrr"}
	add		$tmp5 $tmp2 $tmp4 	%argrw{"wrr"}
	compref		$tmp6 value_color $const5 	%argrw{"wrr"}
	mul		$tmp7 $tmp6 $const6 	%argrw{"wrr"}
	add		value_float $tmp5 $tmp7 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_color.osl:16
#   value_int = (int)(value_color[0] * 0.2126 + value_color[1] * 0.7152 + value_color[2] * 0.0722);
	compref		$tmp8 value_color $const1 	%line{16} %argrw{"wrr"}
	mul		$tmp9 $tmp8 $const2 	%argrw{"wrr"}
	compref		$tmp10 value_color $const3 	%argrw{"wrr"}
	mul		$tmp11 $tmp10 $const4 	%argrw{"wrr"}
	add		$tmp12 $tmp9 $tmp11 	%argrw{"wrr"}
	compref		$tmp13 value_color $const5 	%argrw{"wrr"}
	mul		$tmp14 $tmp13 $const6 	%argrw{"wrr"}
	add		$tmp15 $tmp12 $tmp14 	%argrw{"wrr"}
	assign		value_int $tmp15 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_color.osl:17
#   value_vector = vector(value_color[0], value_color[1], value_color[2]);
	compref		$tmp16 value_color $const1 	%line{17} %argrw{"wrr"}
	compref		$tmp17 value_color $const3 	%argrw{"wrr"}
	compref		$tmp18 value_color $const5 	%argrw{"wrr"}
	vector		value_vector $tmp16 $tmp17 $tmp18 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_color.osl:18
#   value_point = point(value_color[0], value_color[1], value_color[2]);
	compref		$tmp19 value_color $const1 	%line{18} %argrw{"wrr"}
	compref		$tmp20 value_color $const3 	%argrw{"wrr"}
	compref		$tmp21 value_color $const5 	%argrw{"wrr"}
	point		value_point $tmp19 $tmp20 $tmp21 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_color.osl:19
#   value_normal = normal(value_color[0], value_color[1], value_color[2]);
	compref		$tmp22 value_color $const1 	%line{19} %argrw{"wrr"}
	compref		$tmp23 value_color $const3 	%argrw{"wrr"}
	compref		$tmp24 value_color $const5 	%argrw{"wrr"}
	normal		value_normal $tmp22 $tmp23 $tmp24 	%argrw{"wrrr"}
	end
