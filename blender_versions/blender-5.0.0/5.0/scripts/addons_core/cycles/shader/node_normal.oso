OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_normal.oso
shader node_normal
param	normal	direction	0 0 0		%read{0,0} %write{2147483647,-1}
param	normal	NormalIn	0 0 0		%read{1,1} %write{2147483647,-1}
oparam	normal	NormalOut	0 0 0		%read{2,2} %write{0,0}
oparam	float	Dot	1		%read{2147483647,-1} %write{2,2}
temp	normal	$tmp1	%read{2,2} %write{1,1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_normal.osl:12
#   NormalOut = normalize(direction);
	normalize	NormalOut direction 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_normal.osl"} %line{12} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_normal.osl:13
#   Dot = dot(NormalOut, normalize(NormalIn));
	normalize	$tmp1 NormalIn 	%line{13} %argrw{"wr"}
	dot		Dot NormalOut $tmp1 	%argrw{"wrr"}
	end
