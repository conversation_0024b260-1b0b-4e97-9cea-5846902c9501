OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_vertex_color.oso
shader node_vertex_color
param	string	bump_offset	"center"		%read{11,19} %write{2147483647,-1}
param	float	bump_filter_width	0.100000001		%read{14,25} %write{2147483647,-1} %derivs
param	string	layer_name	""		%read{0,3} %write{2147483647,-1} %derivs
oparam	color	Color	0 0 0		%read{13,23} %write{9,23} %derivs
oparam	float	Alpha	0		%read{16,26} %write{10,26} %derivs
local	float[4]	vertex_color	%read{6,10} %write{4,4} %derivs
local	string	vertex_color_layer	%read{4,4} %write{2,3} %derivs
const	string	$const1	""		%read{0,0} %write{2147483647,-1}
temp	int	$tmp1	%read{1,1} %write{0,0}
const	string	$const2	"geom:vertex_color"		%read{2,2} %write{2147483647,-1}
temp	int	$tmp2	%read{5,5} %write{4,4}
const	int	$const3	0		%read{6,6} %write{2147483647,-1}
temp	float	$tmp3	%read{9,9} %write{6,6} %derivs
const	int	$const4	1		%read{7,7} %write{2147483647,-1}
temp	float	$tmp4	%read{9,9} %write{7,7} %derivs
const	int	$const5	2		%read{8,8} %write{2147483647,-1}
temp	float	$tmp5	%read{9,9} %write{8,8} %derivs
const	int	$const6	3		%read{10,10} %write{2147483647,-1}
const	string	$const7	"dx"		%read{11,11} %write{2147483647,-1}
temp	int	$tmp6	%read{12,12} %write{11,11}
temp	color	$tmp7	%read{14,14} %write{13,13} %derivs
temp	color	$tmp8	%read{15,15} %write{14,14} %derivs
temp	float	$tmp9	%read{17,17} %write{16,16} %derivs
temp	float	$tmp10	%read{18,18} %write{17,17} %derivs
const	string	$const8	"dy"		%read{19,19} %write{2147483647,-1}
temp	int	$tmp11	%read{20,20} %write{19,19}
temp	color	$tmp12	%read{22,22} %write{21,21} %derivs
temp	color	$tmp13	%read{23,23} %write{22,22} %derivs
temp	float	$tmp14	%read{25,25} %write{24,24} %derivs
temp	float	$tmp15	%read{26,26} %write{25,25} %derivs
const	string	$const9	"%s"		%read{27,27} %write{2147483647,-1}
const	string	$const10	"Invalid attribute."		%read{27,27} %write{2147483647,-1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:16
#   if (layer_name == "") {
	eq		$tmp1 layer_name $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl"} %line{16} %argrw{"wrr"}
	if		$tmp1 3 4 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:17
#     vertex_color_layer = "geom:vertex_color";
	assign		vertex_color_layer $const2 	%line{17} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:20
#     vertex_color_layer = layer_name;
	assign		vertex_color_layer layer_name 	%line{20} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:23
#   if (getattribute(vertex_color_layer, vertex_color)) {
	getattribute	$tmp2 vertex_color_layer vertex_color 	%line{23} %argrw{"wrw"}
	if		$tmp2 27 28 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:24
#     Color = color(vertex_color[0], vertex_color[1], vertex_color[2]);
	aref		$tmp3 vertex_color $const3 	%line{24} %argrw{"wrr"}
	aref		$tmp4 vertex_color $const4 	%argrw{"wrr"}
	aref		$tmp5 vertex_color $const5 	%argrw{"wrr"}
	color		Color $tmp3 $tmp4 $tmp5 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:25
#     Alpha = vertex_color[3];
	aref		Alpha vertex_color $const6 	%line{25} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:27
#     if (bump_offset == "dx") {
	eq		$tmp6 bump_offset $const7 	%line{27} %argrw{"wrr"}
	if		$tmp6 19 27 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:28
#       Color += Dx(Color) * bump_filter_width;
	Dx		$tmp7 Color 	%line{28} %argrw{"wr"} %argderivs{1}
	mul		$tmp8 $tmp7 bump_filter_width 	%argrw{"wrr"}
	add		Color Color $tmp8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:29
#       Alpha += Dx(Alpha) * bump_filter_width;
	Dx		$tmp9 Alpha 	%line{29} %argrw{"wr"} %argderivs{1}
	mul		$tmp10 $tmp9 bump_filter_width 	%argrw{"wrr"}
	add		Alpha Alpha $tmp10 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:31
#     else if (bump_offset == "dy") {
	eq		$tmp11 bump_offset $const8 	%line{31} %argrw{"wrr"}
	if		$tmp11 27 27 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:32
#       Color += Dy(Color) * bump_filter_width;
	Dy		$tmp12 Color 	%line{32} %argrw{"wr"} %argderivs{1}
	mul		$tmp13 $tmp12 bump_filter_width 	%argrw{"wrr"}
	add		Color Color $tmp13 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:33
#       Alpha += Dy(Alpha) * bump_filter_width;
	Dy		$tmp14 Alpha 	%line{33} %argrw{"wr"} %argderivs{1}
	mul		$tmp15 $tmp14 bump_filter_width 	%argrw{"wrr"}
	add		Alpha Alpha $tmp15 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vertex_color.osl:37
#     warning("%s", "Invalid attribute.");
	warning		$const9 $const10 	%line{37} %argrw{"rr"}
	end
