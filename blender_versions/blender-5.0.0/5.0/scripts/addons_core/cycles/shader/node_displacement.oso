OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_displacement.oso
shader node_displacement
param	string	space	"object"		%read{2,10} %write{2147483647,-1}
param	float	Height	0		%read{7,7} %write{2147483647,-1}
param	float	Midlevel	0.5		%read{7,7} %write{2147483647,-1}
param	float	Scale	1		%read{9,9} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{1,1} %write{0,0} %initexpr
oparam	vector	Displacement	0 0 0		%read{5,12} %write{1,12}
global	normal	N	%read{0,0} %write{2147483647,-1}
const	string	$const1	"object"		%read{2,12} %write{2147483647,-1}
temp	int	$tmp1	%read{3,3} %write{2,2}
const	string	$const2	"transform"		%read{4,4} %write{2147483647,-1}
const	string	$const3	"common"		%read{5,5} %write{2147483647,-1}
temp	vector	$tmp2	%read{8,8} %write{6,6}
temp	float	$tmp3	%read{8,8} %write{7,7}
temp	vector	$tmp4	%read{9,9} %write{8,8}
temp	int	$tmp5	%read{11,11} %write{10,10}
const	string	$const4	"world"		%read{12,12} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:11
#                          normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl"} %line{11} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:14
#   Displacement = Normal;
	assign		Displacement Normal 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl"} %line{14} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:15
#   if (space == "object") {
	eq		$tmp1 space $const1 	%line{15} %argrw{"wrr"}
	if		$tmp1 6 6 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:16
#     Displacement = transform("object", Displacement);
	functioncall	$const2 6 	%line{16} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:262
# vector transform (string to, vector p) { return transform("common",to,p); }
	transformv	Displacement $const3 $const1 Displacement 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{262} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:19
#   Displacement = normalize(Displacement) * (Height - Midlevel) * Scale;
	normalize	$tmp2 Displacement 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl"} %line{19} %argrw{"wr"}
	sub		$tmp3 Height Midlevel 	%argrw{"wrr"}
	mul		$tmp4 $tmp2 $tmp3 	%argrw{"wrr"}
	mul		Displacement $tmp4 Scale 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:21
#   if (space == "object") {
	eq		$tmp5 space $const1 	%line{21} %argrw{"wrr"}
	if		$tmp5 13 13 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_displacement.osl:22
#     Displacement = transform("object", "world", Displacement);
	transformv	Displacement $const1 $const4 Displacement 	%line{22} %argrw{"wrrr"}
	end
