OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_combine_xyz.oso
shader node_combine_xyz
param	float	X	0		%read{0,0} %write{2147483647,-1}
param	float	Y	0		%read{0,0} %write{2147483647,-1}
param	float	Z	0		%read{0,0} %write{2147483647,-1}
oparam	vector	Vector	0.800000012 0.800000012 0.800000012		%read{2147483647,-1} %write{0,0}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_combine_xyz.osl:9
#   Vector = vector(X, Y, Z);
	vector		Vector X Y Z 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_combine_xyz.osl"} %line{9} %argrw{"wrrr"}
	end
