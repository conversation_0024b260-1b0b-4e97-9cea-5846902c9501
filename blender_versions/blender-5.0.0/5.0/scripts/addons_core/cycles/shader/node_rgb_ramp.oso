OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_rgb_ramp.oso
shader node_rgb_ramp
param	color[]	ramp_color	0 0 0		%read{2,57} %write{2147483647,-1}
param	float[]	ramp_alpha	0		%read{63,118} %write{2147483647,-1}
param	int	interpolate	1		%read{48,109} %write{2147483647,-1}
param	float	Fac	0		%read{1,94} %write{2147483647,-1}
oparam	color	Color	0 0 0		%read{2147483647,-1} %write{30,60}
oparam	float	Alpha	1		%read{2147483647,-1} %write{91,121}
local	float	___345_f	%read{3,46} %write{1,37}
local	int	___345_table_size	%read{20,44} %write{2,2}
local	color	___346_t0	%read{18,30} %write{16,21}
local	color	___346_dy	%read{26,26} %write{18,24}
local	int	___345_i	%read{39,56} %write{38,44}
local	float	___345_t	%read{50,58} %write{46,46}
local	color	___345_result	%read{55,60} %write{47,59}
local	float	___352_f	%read{64,107} %write{62,98}
local	int	___352_table_size	%read{81,105} %write{63,63}
local	float	___353_t0	%read{79,91} %write{77,82}
local	float	___353_dy	%read{87,87} %write{79,85}
local	int	___352_i	%read{100,117} %write{99,105}
local	float	___352_t	%read{111,119} %write{107,107}
local	float	___352_result	%read{116,121} %write{108,120}
const	int	$const1	0		%read{4,112} %write{2147483647,-1}
const	string	$const2	"rgb_ramp_lookup"		%read{0,61} %write{2147483647,-1}
const	float	$const3	0		%read{3,111} %write{2147483647,-1}
temp	int	$tmp1	%read{4,4} %write{3,3}
temp	int	$tmp2	%read{5,9} %write{4,8}
const	float	$const4	1		%read{6,115} %write{2147483647,-1}
temp	int	$tmp3	%read{7,7} %write{6,6}
temp	int	$tmp4	%read{8,8} %write{7,7}
temp	int	$tmp5	%read{10,13} %write{9,12}
temp	int	$tmp6	%read{12,12} %write{11,11}
temp	int	$tmp7	%read{15,15} %write{14,14}
const	int	$const5	1		%read{17,117} %write{2147483647,-1}
temp	color	$tmp8	%read{18,18} %write{17,17}
temp	int	$tmp9	%read{21,21} %write{20,20}
const	int	$const6	2		%read{22,83} %write{2147483647,-1}
temp	int	$tmp10	%read{23,23} %write{22,22}
temp	color	$tmp11	%read{24,24} %write{23,23}
temp	color	$tmp12	%read{29,29} %write{26,26}
temp	int	$tmp13	%read{28,28} %write{27,27}
temp	color	$tmp14	%read{30,30} %write{29,29}
temp	color	$tmp15	%read{29,29} %write{28,28}
temp	float	$tmp16	%read{37,37} %write{34,34}
const	string	$const7	"clamp"		%read{32,93} %write{2147483647,-1}
temp	float	$tmp17	%read{34,34} %write{33,33}
temp	int	$tmp18	%read{36,36} %write{35,35}
temp	float	$tmp19	%read{37,37} %write{36,36}
temp	int	$tmp20	%read{40,40} %write{39,39}
temp	int	$tmp21	%read{43,43} %write{42,42}
temp	float	$tmp22	%read{46,46} %write{45,45}
temp	int	$tmp23	%read{49,53} %write{48,52}
temp	int	$tmp24	%read{51,51} %write{50,50}
temp	int	$tmp25	%read{52,52} %write{51,51}
temp	float	$tmp26	%read{55,55} %write{54,54}
temp	color	$tmp27	%read{59,59} %write{55,55}
temp	int	$tmp28	%read{57,57} %write{56,56}
temp	color	$tmp29	%read{58,58} %write{57,57}
temp	color	$tmp30	%read{59,59} %write{58,58}
temp	int	$tmp31	%read{65,65} %write{64,64}
temp	int	$tmp32	%read{66,70} %write{65,69}
temp	int	$tmp33	%read{68,68} %write{67,67}
temp	int	$tmp34	%read{69,69} %write{68,68}
temp	int	$tmp35	%read{71,74} %write{70,73}
temp	int	$tmp36	%read{73,73} %write{72,72}
temp	int	$tmp37	%read{76,76} %write{75,75}
temp	float	$tmp38	%read{79,79} %write{78,78}
temp	int	$tmp39	%read{82,82} %write{81,81}
temp	int	$tmp40	%read{84,84} %write{83,83}
temp	float	$tmp41	%read{85,85} %write{84,84}
temp	float	$tmp42	%read{90,90} %write{87,87}
temp	int	$tmp43	%read{89,89} %write{88,88}
temp	float	$tmp44	%read{91,91} %write{90,90}
temp	float	$tmp45	%read{90,90} %write{89,89}
temp	float	$tmp46	%read{98,98} %write{95,95}
temp	float	$tmp47	%read{95,95} %write{94,94}
temp	int	$tmp48	%read{97,97} %write{96,96}
temp	float	$tmp49	%read{98,98} %write{97,97}
temp	int	$tmp50	%read{101,101} %write{100,100}
temp	int	$tmp51	%read{104,104} %write{103,103}
temp	float	$tmp52	%read{107,107} %write{106,106}
temp	int	$tmp53	%read{110,114} %write{109,113}
temp	int	$tmp54	%read{112,112} %write{111,111}
temp	int	$tmp55	%read{113,113} %write{112,112}
temp	float	$tmp56	%read{116,116} %write{115,115}
temp	float	$tmp57	%read{120,120} %write{116,116}
temp	int	$tmp58	%read{118,118} %write{117,117}
temp	float	$tmp59	%read{119,119} %write{118,118}
temp	float	$tmp60	%read{120,120} %write{119,119}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_rgb_ramp.osl:16
#   Color = rgb_ramp_lookup(ramp_color, Fac, interpolate, 0);
	functioncall	$const2 61 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_rgb_ramp.osl"} %line{16} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:9
#   float f = at;
	assign		___345_f Fac 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h"} %line{9} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:10
#   int table_size = arraylength(ramp);
	arraylength	___345_table_size ramp_color 	%line{10} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:12
#   if ((f < 0.0 || f > 1.0) && extrapolate) {
	lt		$tmp1 ___345_f $const3 	%line{12} %argrw{"wrr"}
	neq		$tmp2 $tmp1 $const1 	%argrw{"wrr"}
	if		$tmp2 6 9 	%argrw{"r"}
	gt		$tmp3 ___345_f $const4 	%argrw{"wrr"}
	neq		$tmp4 $tmp3 $const1 	%argrw{"wrr"}
	assign		$tmp2 $tmp4 	%argrw{"wr"}
	neq		$tmp5 $tmp2 $const1 	%argrw{"wrr"}
	if		$tmp5 13 13 	%argrw{"r"}
	neq		$tmp6 $const1 $const1 	%argrw{"wrr"}
	assign		$tmp5 $tmp6 	%argrw{"wr"}
	if		$tmp5 32 32 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:14
#     if (f < 0.0) {
	lt		$tmp7 ___345_f $const3 	%line{14} %argrw{"wrr"}
	if		$tmp7 20 26 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:15
#       t0 = ramp[0];
	aref		___346_t0 ramp_color $const1 	%line{15} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:16
#       dy = t0 - ramp[1];
	aref		$tmp8 ramp_color $const5 	%line{16} %argrw{"wrr"}
	sub		___346_dy ___346_t0 $tmp8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:17
#       f = -f;
	neg		___345_f ___345_f 	%line{17} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:20
#       t0 = ramp[table_size - 1];
	sub		$tmp9 ___345_table_size $const5 	%line{20} %argrw{"wrr"}
	aref		___346_t0 ramp_color $tmp9 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:21
#       dy = t0 - ramp[table_size - 2];
	sub		$tmp10 ___345_table_size $const6 	%line{21} %argrw{"wrr"}
	aref		$tmp11 ramp_color $tmp10 	%argrw{"wrr"}
	sub		___346_dy ___346_t0 $tmp11 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:22
#       f = f - 1.0;
	sub		___345_f ___345_f $const4 	%line{22} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:24
#     return t0 + dy * f * (table_size - 1);
	mul		$tmp12 ___346_dy ___345_f 	%line{24} %argrw{"wrr"}
	sub		$tmp13 ___345_table_size $const5 	%argrw{"wrr"}
	assign		$tmp15 $tmp13 	%argrw{"wr"}
	mul		$tmp14 $tmp12 $tmp15 	%argrw{"wrr"}
	add		Color ___346_t0 $tmp14 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:27
#   f = clamp(at, 0.0, 1.0) * (table_size - 1);
	functioncall	$const7 35 	%line{27} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp17 Fac $const4 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		$tmp16 $tmp17 $const3 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:27
#   f = clamp(at, 0.0, 1.0) * (table_size - 1);
	sub		$tmp18 ___345_table_size $const5 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h"} %line{27} %argrw{"wrr"}
	assign		$tmp19 $tmp18 	%argrw{"wr"}
	mul		___345_f $tmp16 $tmp19 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:30
#   int i = (int)f;
	assign		___345_i ___345_f 	%line{30} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:31
#   if (i < 0) {
	lt		$tmp20 ___345_i $const1 	%line{31} %argrw{"wrr"}
	if		$tmp20 42 42 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:32
#     i = 0;
	assign		___345_i $const1 	%line{32} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:34
#   if (i >= table_size) {
	ge		$tmp21 ___345_i ___345_table_size 	%line{34} %argrw{"wrr"}
	if		$tmp21 45 45 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:35
#     i = table_size - 1;
	sub		___345_i ___345_table_size $const5 	%line{35} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:37
#   float t = f - (float)i;
	assign		$tmp22 ___345_i 	%line{37} %argrw{"wr"}
	sub		___345_t ___345_f $tmp22 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:39
#   color result = ramp[i];
	aref		___345_result ramp_color ___345_i 	%line{39} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:41
#   if (interpolate && t > 0.0) {
	neq		$tmp23 interpolate $const1 	%line{41} %argrw{"wrr"}
	if		$tmp23 53 53 	%argrw{"r"}
	gt		$tmp24 ___345_t $const3 	%argrw{"wrr"}
	neq		$tmp25 $tmp24 $const1 	%argrw{"wrr"}
	assign		$tmp23 $tmp25 	%argrw{"wr"}
	if		$tmp23 60 60 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:42
#     result = (1.0 - t) * result + t * ramp[i + 1];
	sub		$tmp26 $const4 ___345_t 	%line{42} %argrw{"wrr"}
	mul		$tmp27 $tmp26 ___345_result 	%argrw{"wrr"}
	add		$tmp28 ___345_i $const5 	%argrw{"wrr"}
	aref		$tmp29 ramp_color $tmp28 	%argrw{"wrr"}
	mul		$tmp30 ___345_t $tmp29 	%argrw{"wrr"}
	add		___345_result $tmp27 $tmp30 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:45
#   return result;
	assign		Color ___345_result 	%line{45} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_rgb_ramp.osl:17
#   Alpha = rgb_ramp_lookup(ramp_alpha, Fac, interpolate, 0);
	functioncall	$const2 122 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_rgb_ramp.osl"} %line{17} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:50
#   float f = at;
	assign		___352_f Fac 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h"} %line{50} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:51
#   int table_size = arraylength(ramp);
	arraylength	___352_table_size ramp_alpha 	%line{51} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:53
#   if ((f < 0.0 || f > 1.0) && extrapolate) {
	lt		$tmp31 ___352_f $const3 	%line{53} %argrw{"wrr"}
	neq		$tmp32 $tmp31 $const1 	%argrw{"wrr"}
	if		$tmp32 67 70 	%argrw{"r"}
	gt		$tmp33 ___352_f $const4 	%argrw{"wrr"}
	neq		$tmp34 $tmp33 $const1 	%argrw{"wrr"}
	assign		$tmp32 $tmp34 	%argrw{"wr"}
	neq		$tmp35 $tmp32 $const1 	%argrw{"wrr"}
	if		$tmp35 74 74 	%argrw{"r"}
	neq		$tmp36 $const1 $const1 	%argrw{"wrr"}
	assign		$tmp35 $tmp36 	%argrw{"wr"}
	if		$tmp35 93 93 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:55
#     if (f < 0.0) {
	lt		$tmp37 ___352_f $const3 	%line{55} %argrw{"wrr"}
	if		$tmp37 81 87 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:56
#       t0 = ramp[0];
	aref		___353_t0 ramp_alpha $const1 	%line{56} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:57
#       dy = t0 - ramp[1];
	aref		$tmp38 ramp_alpha $const5 	%line{57} %argrw{"wrr"}
	sub		___353_dy ___353_t0 $tmp38 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:58
#       f = -f;
	neg		___352_f ___352_f 	%line{58} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:61
#       t0 = ramp[table_size - 1];
	sub		$tmp39 ___352_table_size $const5 	%line{61} %argrw{"wrr"}
	aref		___353_t0 ramp_alpha $tmp39 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:62
#       dy = t0 - ramp[table_size - 2];
	sub		$tmp40 ___352_table_size $const6 	%line{62} %argrw{"wrr"}
	aref		$tmp41 ramp_alpha $tmp40 	%argrw{"wrr"}
	sub		___353_dy ___353_t0 $tmp41 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:63
#       f = f - 1.0;
	sub		___352_f ___352_f $const4 	%line{63} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:65
#     return t0 + dy * f * (table_size - 1);
	mul		$tmp42 ___353_dy ___352_f 	%line{65} %argrw{"wrr"}
	sub		$tmp43 ___352_table_size $const5 	%argrw{"wrr"}
	assign		$tmp45 $tmp43 	%argrw{"wr"}
	mul		$tmp44 $tmp42 $tmp45 	%argrw{"wrr"}
	add		Alpha ___353_t0 $tmp44 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:68
#   f = clamp(at, 0.0, 1.0) * (table_size - 1);
	functioncall	$const7 96 	%line{68} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp47 Fac $const4 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		$tmp46 $tmp47 $const3 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:68
#   f = clamp(at, 0.0, 1.0) * (table_size - 1);
	sub		$tmp48 ___352_table_size $const5 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h"} %line{68} %argrw{"wrr"}
	assign		$tmp49 $tmp48 	%argrw{"wr"}
	mul		___352_f $tmp46 $tmp49 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:71
#   int i = (int)f;
	assign		___352_i ___352_f 	%line{71} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:72
#   if (i < 0) {
	lt		$tmp50 ___352_i $const1 	%line{72} %argrw{"wrr"}
	if		$tmp50 103 103 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:73
#     i = 0;
	assign		___352_i $const1 	%line{73} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:75
#   if (i >= table_size) {
	ge		$tmp51 ___352_i ___352_table_size 	%line{75} %argrw{"wrr"}
	if		$tmp51 106 106 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:76
#     i = table_size - 1;
	sub		___352_i ___352_table_size $const5 	%line{76} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:78
#   float t = f - (float)i;
	assign		$tmp52 ___352_i 	%line{78} %argrw{"wr"}
	sub		___352_t ___352_f $tmp52 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:80
#   float result = ramp[i];
	aref		___352_result ramp_alpha ___352_i 	%line{80} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:82
#   if (interpolate && t > 0.0) {
	neq		$tmp53 interpolate $const1 	%line{82} %argrw{"wrr"}
	if		$tmp53 114 114 	%argrw{"r"}
	gt		$tmp54 ___352_t $const3 	%argrw{"wrr"}
	neq		$tmp55 $tmp54 $const1 	%argrw{"wrr"}
	assign		$tmp53 $tmp55 	%argrw{"wr"}
	if		$tmp53 121 121 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:83
#     result = (1.0 - t) * result + t * ramp[i + 1];
	sub		$tmp56 $const4 ___352_t 	%line{83} %argrw{"wrr"}
	mul		$tmp57 $tmp56 ___352_result 	%argrw{"wrr"}
	add		$tmp58 ___352_i $const5 	%argrw{"wrr"}
	aref		$tmp59 ramp_alpha $tmp58 	%argrw{"wrr"}
	mul		$tmp60 ___352_t $tmp59 	%argrw{"wrr"}
	add		___352_result $tmp57 $tmp60 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ramp_util.h:86
#   return result;
	assign		Alpha ___352_result 	%line{86} %argrw{"wr"}
	end
