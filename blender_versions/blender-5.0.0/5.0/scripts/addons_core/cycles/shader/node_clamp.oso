OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_clamp.oso
shader node_clamp
param	string	clamp_type	"minmax"		%read{0,0} %write{2147483647,-1}
param	float	Value	1		%read{8,11} %write{2147483647,-1}
param	float	Min	0		%read{3,12} %write{2147483647,-1}
param	float	Max	1		%read{3,11} %write{2147483647,-1}
oparam	float	Result	0		%read{2147483647,-1} %write{9,12}
const	string	$const1	"range"		%read{0,0} %write{2147483647,-1}
temp	int	$tmp1	%read{1,1} %write{0,0}
temp	int	$tmp2	%read{2,6} %write{1,5}
const	int	$const2	0		%read{1,4} %write{2147483647,-1}
temp	int	$tmp3	%read{4,4} %write{3,3}
temp	int	$tmp4	%read{5,5} %write{4,4}
const	string	$const3	"clamp"		%read{7,10} %write{2147483647,-1}
temp	float	$tmp5	%read{9,9} %write{8,8}
temp	float	$tmp6	%read{12,12} %write{11,11}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_clamp.osl:13
#   Result = (clamp_type == "range" && (Min > Max)) ? clamp(Value, Max, Min) :
	eq		$tmp1 clamp_type $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_clamp.osl"} %line{13} %argrw{"wrr"}
	neq		$tmp2 $tmp1 $const2 	%argrw{"wrr"}
	if		$tmp2 6 6 	%argrw{"r"}
	gt		$tmp3 Min Max 	%argrw{"wrr"}
	neq		$tmp4 $tmp3 $const2 	%argrw{"wrr"}
	assign		$tmp2 $tmp4 	%argrw{"wr"}
	if		$tmp2 10 13 	%argrw{"r"}
	functioncall	$const3 10 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp5 Value Min 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		Result $tmp5 Max 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_clamp.osl:14
#                                                     clamp(Value, Min, Max);
	functioncall	$const3 13 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_clamp.osl"} %line{14} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp6 Value Max 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		Result $tmp6 Min 	%argrw{"wrr"}
	end
