OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.oso
shader node_glossy_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	string	distribution	"ggx"		%read{79,83} %write{2147483647,-1}
param	float	Roughness	0.200000003		%read{3,3} %write{2147483647,-1}
param	float	Anisotropy	0		%read{7,7} %write{2147483647,-1}
param	float	Rotation	0		%read{15,17} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{19,83} %write{0,0} %initexpr
param	normal	Tangent	0 0 0		%read{9,9} %write{2147483647,-1}
oparam	closure color	BSDF			%read{2147483647,-1} %write{82,84}
global	normal	N	%read{0,0} %write{2147483647,-1}
local	vector	___257_axis	%read{23,25} %write{20,20}
local	float	___257_cosang	%read{22,63} %write{21,21}
local	float	___257_sinang	%read{33,58} %write{21,21}
local	float	___257_cosang1	%read{32,57} %write{22,22}
local	float	___257_x	%read{26,58} %write{23,23}
local	float	___257_y	%read{31,56} %write{24,24}
local	float	___257_z	%read{33,61} %write{25,25}
local	matrix	___257_M	%read{67,67} %write{65,65}
local	color	base_color	%read{81,84} %write{1,1}
local	float	roughness	%read{5,78} %write{4,5}
local	float	roughness_u	%read{81,83} %write{13,76}
local	float	roughness_v	%read{81,83} %write{14,78}
local	float	aniso	%read{10,77} %write{8,8}
local	vector	T	%read{66,83} %write{9,68}
const	color	$const1	0 0 0		%read{1,1} %write{2147483647,-1}
const	float	$const2	0		%read{4,83} %write{2147483647,-1}
const	float	$const3	1		%read{3,77} %write{2147483647,-1}
const	string	$const4	"clamp"		%read{2,6} %write{2147483647,-1}
temp	float	$tmp2	%read{4,4} %write{3,3}
const	float	$const5	-0.99000001		%read{8,8} %write{2147483647,-1}
const	float	$const6	0.99000001		%read{7,7} %write{2147483647,-1}
temp	float	$tmp3	%read{8,8} %write{7,7}
temp	float	$tmp4	%read{11,11} %write{10,10}
const	float	$const7	9.99999975e-05		%read{11,11} %write{2147483647,-1}
temp	int	$tmp5	%read{12,12} %write{11,11}
temp	int	$tmp6	%read{16,16} %write{15,15}
const	float	$const8	6.28318548		%read{17,17} %write{2147483647,-1}
temp	float	$tmp7	%read{21,21} %write{17,17}
const	point	$const9	0 0 0		%read{19,68} %write{2147483647,-1}
const	string	$const10	"rotate"		%read{18,18} %write{2147483647,-1}
temp	vector	$tmp9	%read{20,20} %write{19,19}
const	int	$const11	0		%read{23,83} %write{2147483647,-1}
const	int	$const12	1		%read{24,24} %write{2147483647,-1}
const	int	$const13	2		%read{25,25} %write{2147483647,-1}
temp	float	$tmp10	%read{30,30} %write{26,26}
temp	float	$tmp11	%read{28,28} %write{27,27}
temp	float	$tmp12	%read{29,29} %write{28,28}
temp	float	$tmp13	%read{30,30} %write{29,29}
temp	float	$tmp14	%read{65,65} %write{30,30}
temp	float	$tmp15	%read{32,32} %write{31,31}
temp	float	$tmp16	%read{34,34} %write{32,32}
temp	float	$tmp17	%read{34,34} %write{33,33}
temp	float	$tmp18	%read{65,65} %write{34,34}
temp	float	$tmp19	%read{36,36} %write{35,35}
temp	float	$tmp20	%read{38,38} %write{36,36}
temp	float	$tmp21	%read{38,38} %write{37,37}
temp	float	$tmp22	%read{65,65} %write{38,38}
temp	float	$tmp23	%read{40,40} %write{39,39}
temp	float	$tmp24	%read{42,42} %write{40,40}
temp	float	$tmp25	%read{42,42} %write{41,41}
temp	float	$tmp26	%read{65,65} %write{42,42}
temp	float	$tmp27	%read{47,47} %write{43,43}
temp	float	$tmp28	%read{45,45} %write{44,44}
temp	float	$tmp29	%read{46,46} %write{45,45}
temp	float	$tmp30	%read{47,47} %write{46,46}
temp	float	$tmp31	%read{65,65} %write{47,47}
temp	float	$tmp32	%read{49,49} %write{48,48}
temp	float	$tmp33	%read{51,51} %write{49,49}
temp	float	$tmp34	%read{51,51} %write{50,50}
temp	float	$tmp35	%read{65,65} %write{51,51}
temp	float	$tmp36	%read{53,53} %write{52,52}
temp	float	$tmp37	%read{55,55} %write{53,53}
temp	float	$tmp38	%read{55,55} %write{54,54}
temp	float	$tmp39	%read{65,65} %write{55,55}
temp	float	$tmp40	%read{57,57} %write{56,56}
temp	float	$tmp41	%read{59,59} %write{57,57}
temp	float	$tmp42	%read{59,59} %write{58,58}
temp	float	$tmp43	%read{65,65} %write{59,59}
temp	float	$tmp44	%read{64,64} %write{60,60}
temp	float	$tmp45	%read{62,62} %write{61,61}
temp	float	$tmp46	%read{63,63} %write{62,62}
temp	float	$tmp47	%read{64,64} %write{63,63}
temp	float	$tmp48	%read{65,65} %write{64,64}
temp	vector	$tmp49	%read{68,68} %write{67,67}
temp	vector	$tmp50	%read{67,67} %write{66,66}
temp	int	$tmp51	%read{70,70} %write{69,69}
temp	float	$tmp52	%read{72,72} %write{71,71}
temp	float	$tmp53	%read{74,74} %write{73,73}
temp	float	$tmp54	%read{76,76} %write{75,75}
temp	float	$tmp55	%read{78,78} %write{77,77}
const	string	$const14	"Multiscatter GGX"		%read{79,79} %write{2147483647,-1}
temp	int	$tmp56	%read{80,80} %write{79,79}
temp	closure color	$tmp57	%read{82,82} %write{81,81}
const	string	$const15	"microfacet_multi_ggx_aniso"		%read{81,81} %write{2147483647,-1}
temp	closure color	$tmp58	%read{84,84} %write{83,83}
const	string	$const16	"microfacet"		%read{83,83} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:13
#                         normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl"} %line{13} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:18
#   color base_color = max(Color, color(0.0));
	max		base_color Color $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl"} %line{18} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:19
#   float roughness = clamp(Roughness, 0.0, 1.0);
	functioncall	$const4 5 	%line{19} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp2 Roughness $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		roughness $tmp2 $const2 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:20
#   roughness = roughness * roughness;
	mul		roughness roughness roughness 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl"} %line{20} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:22
#   float aniso = clamp(Anisotropy, -0.99, 0.99);
	functioncall	$const4 9 	%line{22} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:141
# float  clamp (float x, float minval, float maxval) { return max(min(x,maxval),minval); }
	min		$tmp3 Anisotropy $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{141} %argrw{"wrr"}
	max		aniso $tmp3 $const5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:25
#   vector T = Tangent;
	assign		T Tangent 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl"} %line{25} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:27
#   if (abs(aniso) <= 1e-4) {
	abs		$tmp4 aniso 	%line{27} %argrw{"wr"}
	le		$tmp5 $tmp4 $const7 	%argrw{"wrr"}
	if		$tmp5 15 79 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:28
#     roughness_u = roughness;
	assign		roughness_u roughness 	%line{28} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:29
#     roughness_v = roughness;
	assign		roughness_v roughness 	%line{29} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:32
#     if (Rotation != 0.0)
	neq		$tmp6 Rotation $const2 	%line{32} %argrw{"wrr"}
	if		$tmp6 69 69 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:33
#       T = rotate(T, Rotation * M_2PI, point(0.0, 0.0, 0.0), Normal);
	mul		$tmp7 Rotation $const8 	%line{33} %argrw{"wrr"}
	functioncall	$const10 69 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:270
#     vector axis = normalize (b - a);
	sub		$tmp9 Normal $const9 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{270} %argrw{"wrr"}
	normalize	___257_axis $tmp9 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:272
#     sincos (angle, sinang, cosang);
	sincos		$tmp7 ___257_sinang ___257_cosang 	%line{272} %argrw{"rww"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:273
#     float cosang1 = 1.0 - cosang;
	sub		___257_cosang1 $const3 ___257_cosang 	%line{273} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:274
#     float x = axis[0], y = axis[1], z = axis[2];
	compref		___257_x ___257_axis $const11 	%line{274} %argrw{"wrr"}
	compref		___257_y ___257_axis $const12 	%argrw{"wrr"}
	compref		___257_z ___257_axis $const13 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	mul		$tmp10 ___257_x ___257_x 	%line{275} %argrw{"wrr"}
	mul		$tmp11 ___257_x ___257_x 	%argrw{"wrr"}
	sub		$tmp12 $const3 $tmp11 	%argrw{"wrr"}
	mul		$tmp13 $tmp12 ___257_cosang 	%argrw{"wrr"}
	add		$tmp14 $tmp10 $tmp13 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:276
#                        x * y * cosang1 + z * sinang,
	mul		$tmp15 ___257_x ___257_y 	%line{276} %argrw{"wrr"}
	mul		$tmp16 $tmp15 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp17 ___257_z ___257_sinang 	%argrw{"wrr"}
	add		$tmp18 $tmp16 $tmp17 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:277
#                        x * z * cosang1 - y * sinang,
	mul		$tmp19 ___257_x ___257_z 	%line{277} %argrw{"wrr"}
	mul		$tmp20 $tmp19 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp21 ___257_y ___257_sinang 	%argrw{"wrr"}
	sub		$tmp22 $tmp20 $tmp21 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:279
#                        x * y * cosang1 - z * sinang,
	mul		$tmp23 ___257_x ___257_y 	%line{279} %argrw{"wrr"}
	mul		$tmp24 $tmp23 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp25 ___257_z ___257_sinang 	%argrw{"wrr"}
	sub		$tmp26 $tmp24 $tmp25 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:280
#                        y * y + (1.0 - y * y) * cosang,
	mul		$tmp27 ___257_y ___257_y 	%line{280} %argrw{"wrr"}
	mul		$tmp28 ___257_y ___257_y 	%argrw{"wrr"}
	sub		$tmp29 $const3 $tmp28 	%argrw{"wrr"}
	mul		$tmp30 $tmp29 ___257_cosang 	%argrw{"wrr"}
	add		$tmp31 $tmp27 $tmp30 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:281
#                        y * z * cosang1 + x * sinang,
	mul		$tmp32 ___257_y ___257_z 	%line{281} %argrw{"wrr"}
	mul		$tmp33 $tmp32 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp34 ___257_x ___257_sinang 	%argrw{"wrr"}
	add		$tmp35 $tmp33 $tmp34 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:283
#                        x * z * cosang1 + y * sinang,
	mul		$tmp36 ___257_x ___257_z 	%line{283} %argrw{"wrr"}
	mul		$tmp37 $tmp36 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp38 ___257_y ___257_sinang 	%argrw{"wrr"}
	add		$tmp39 $tmp37 $tmp38 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:284
#                        y * z * cosang1 - x * sinang,
	mul		$tmp40 ___257_y ___257_z 	%line{284} %argrw{"wrr"}
	mul		$tmp41 $tmp40 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp42 ___257_x ___257_sinang 	%argrw{"wrr"}
	sub		$tmp43 $tmp41 $tmp42 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:285
#                        z * z + (1.0 - z * z) * cosang,
	mul		$tmp44 ___257_z ___257_z 	%line{285} %argrw{"wrr"}
	mul		$tmp45 ___257_z ___257_z 	%argrw{"wrr"}
	sub		$tmp46 $const3 $tmp45 	%argrw{"wrr"}
	mul		$tmp47 $tmp46 ___257_cosang 	%argrw{"wrr"}
	add		$tmp48 $tmp44 $tmp47 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	matrix		___257_M $tmp14 $tmp18 $tmp22 $const2 $tmp26 $tmp31 $tmp35 $const2 $tmp39 $tmp43 $tmp48 $const2 $const2 $const2 $const2 $const3 	%line{275} %argrw{"wrrrrrrrrrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:288
#     return transform (M, p-a) + a;
	sub		$tmp50 T $const9 	%line{288} %argrw{"wrr"}
	transformv	$tmp49 ___257_M $tmp50 	%argrw{"wrr"}
	add		T $tmp49 $const9 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:35
#     if (aniso < 0.0) {
	lt		$tmp51 aniso $const2 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl"} %line{35} %argrw{"wrr"}
	if		$tmp51 75 79 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:36
#       roughness_u = roughness / (1.0 + aniso);
	add		$tmp52 $const3 aniso 	%line{36} %argrw{"wrr"}
	div		roughness_u roughness $tmp52 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:37
#       roughness_v = roughness * (1.0 + aniso);
	add		$tmp53 $const3 aniso 	%line{37} %argrw{"wrr"}
	mul		roughness_v roughness $tmp53 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:40
#       roughness_u = roughness * (1.0 - aniso);
	sub		$tmp54 $const3 aniso 	%line{40} %argrw{"wrr"}
	mul		roughness_u roughness $tmp54 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:41
#       roughness_v = roughness / (1.0 - aniso);
	sub		$tmp55 $const3 aniso 	%line{41} %argrw{"wrr"}
	div		roughness_v roughness $tmp55 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:45
#   if (distribution == "Multiscatter GGX")
	eq		$tmp56 distribution $const14 	%line{45} %argrw{"wrr"}
	if		$tmp56 83 85 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:47
#            microfacet_multi_ggx_aniso(Normal, T, roughness_u, roughness_v, base_color);
	closure		$tmp57 $const15 Normal T roughness_u roughness_v base_color 	%line{47} %argrw{"wrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:46
#     BSDF = base_color *
	mul		BSDF $tmp57 base_color 	%line{46} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_glossy_bsdf.osl:49
#     BSDF = base_color * microfacet(distribution, Normal, T, roughness_u, roughness_v, 0.0, 0);
	closure		$tmp58 $const16 distribution Normal T roughness_u roughness_v $const2 $const11 	%line{49} %argrw{"wrrrrrrrr"}
	mul		BSDF $tmp58 base_color 	%argrw{"wrr"}
	end
