OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_absorption_volume.oso
shader node_absorption_volume
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	float	Density	1		%read{2,2} %write{2147483647,-1}
oparam	closure color	Volume			%read{2147483647,-1} %write{4,4}
temp	closure color	$tmp1	%read{4,4} %write{0,0}
const	string	$const1	"absorption"		%read{0,0} %write{2147483647,-1}
const	color	$const2	1 1 1		%read{1,1} %write{2147483647,-1}
temp	color	$tmp3	%read{3,3} %write{1,1}
temp	float	$tmp4	%read{3,3} %write{2,2}
const	float	$const3	0		%read{2,2} %write{2147483647,-1}
temp	color	$tmp5	%read{4,4} %write{3,3}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_absorption_volume.osl:11
#   Volume = ((color(1.0, 1.0, 1.0) - Color) * max(Density, 0.0)) * absorption();
	closure		$tmp1 $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_absorption_volume.osl"} %line{11} %argrw{"wr"}
	sub		$tmp3 $const2 Color 	%argrw{"wrr"}
	max		$tmp4 Density $const3 	%argrw{"wrr"}
	mul		$tmp5 $tmp3 $tmp4 	%argrw{"wrr"}
	mul		Volume $tmp1 $tmp5 	%argrw{"wrr"}
	end
