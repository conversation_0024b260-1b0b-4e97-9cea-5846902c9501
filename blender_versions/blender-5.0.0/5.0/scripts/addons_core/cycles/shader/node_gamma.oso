OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_gamma.oso
shader node_gamma
param	color	ColorIn	0.800000012 0.800000012 0.800000012		%read{0,0} %write{2147483647,-1}
param	float	Gamma	1		%read{0,0} %write{2147483647,-1}
oparam	color	ColorOut	0 0 0		%read{2147483647,-1} %write{0,0}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_gamma.osl:9
#   ColorOut = pow(ColorIn, Gamma);
	pow		ColorOut ColorIn Gamma 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_gamma.osl"} %line{9} %argrw{"wrr"}
	end
