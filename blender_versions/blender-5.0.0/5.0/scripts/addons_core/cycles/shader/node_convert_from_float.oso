OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_convert_from_float.oso
shader node_convert_from_float
param	float	value_float	0		%read{0,4} %write{2147483647,-1}
oparam	string	value_string	""		%read{2147483647,-1} %write{2147483647,-1}
oparam	int	value_int	0		%read{2147483647,-1} %write{0,0}
oparam	color	value_color	0 0 0		%read{2147483647,-1} %write{1,1}
oparam	vector	value_vector	0 0 0		%read{2147483647,-1} %write{2,2}
oparam	point	value_point	0 0 0		%read{2147483647,-1} %write{3,3}
oparam	normal	value_normal	0 0 0		%read{2147483647,-1} %write{4,4}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_float.osl:15
#   value_int = (int)value_float;
	assign		value_int value_float 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_float.osl"} %line{15} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_float.osl:16
#   value_color = color(value_float, value_float, value_float);
	color		value_color value_float value_float value_float 	%line{16} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_float.osl:17
#   value_vector = vector(value_float, value_float, value_float);
	vector		value_vector value_float value_float value_float 	%line{17} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_float.osl:18
#   value_point = point(value_float, value_float, value_float);
	point		value_point value_float value_float value_float 	%line{18} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_float.osl:19
#   value_normal = normal(value_float, value_float, value_float);
	normal		value_normal value_float value_float value_float 	%line{19} %argrw{"wrrr"}
	end
