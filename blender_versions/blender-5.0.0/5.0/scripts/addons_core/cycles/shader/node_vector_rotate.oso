OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_vector_rotate.oso
shader node_vector_rotate
param	int	invert	0		%read{2,97} %write{2147483647,-1}
param	string	rotate_type	"axis"		%read{0,210} %write{2147483647,-1}
param	vector	VectorIn	0 0 0		%read{94,321} %write{2147483647,-1}
param	point	Center	0 0 0		%read{94,320} %write{2147483647,-1}
param	point	Rotation	0 0 0		%read{4,60} %write{2147483647,-1}
param	vector	Axis	0 0 1		%read{265,270} %write{2147483647,-1}
param	float	Angle	0		%read{98,99} %write{2147483647,-1}
oparam	vector	VectorOut	0 0 0		%read{2147483647,-1} %write{96,321}
local	vector	___257_axis	%read{108,276} %write{105,271}
local	float	___257_cosang	%read{107,314} %write{106,272}
local	float	___257_sinang	%read{118,309} %write{106,272}
local	float	___257_cosang1	%read{117,308} %write{107,273}
local	float	___257_x	%read{111,309} %write{108,274}
local	float	___257_y	%read{116,307} %write{109,275}
local	float	___257_z	%read{118,312} %write{110,276}
local	matrix	___257_M	%read{152,318} %write{150,316}
local	float	___361_cx	%read{25,91} %write{5,51}
local	float	___361_cy	%read{17,91} %write{7,53}
local	float	___361_cz	%read{17,88} %write{9,55}
local	float	___361_sx	%read{23,88} %write{11,57}
local	float	___361_sy	%read{21,86} %write{13,59}
local	float	___361_sz	%read{19,87} %write{15,61}
local	matrix	___361_mat	%read{47,93} %write{16,92}
local	matrix	___385_rmat	%read{95,95} %write{48,93}
local	float	___386_a	%read{106,272} %write{98,99}
const	string	$const1	"euler_xyz"		%read{0,0} %write{2147483647,-1}
temp	int	$tmp1	%read{1,1} %write{0,0}
temp	matrix	$tmp2	%read{48,48} %write{47,47}
const	string	$const2	"euler_to_mat"		%read{3,49} %write{2147483647,-1}
const	int	$const3	0		%read{4,274} %write{2147483647,-1}
temp	float	$tmp3	%read{5,5} %write{4,4}
const	int	$const4	1		%read{6,275} %write{2147483647,-1}
temp	float	$tmp4	%read{7,7} %write{6,6}
const	int	$const5	2		%read{8,276} %write{2147483647,-1}
temp	float	$tmp5	%read{9,9} %write{8,8}
temp	float	$tmp6	%read{11,11} %write{10,10}
temp	float	$tmp7	%read{13,13} %write{12,12}
temp	float	$tmp8	%read{15,15} %write{14,14}
const	float	$const6	1		%read{16,316} %write{2147483647,-1}
temp	float	$tmp9	%read{18,18} %write{17,17}
temp	float	$tmp10	%read{20,20} %write{19,19}
temp	float	$tmp11	%read{22,22} %write{21,21}
temp	float	$tmp12	%read{24,24} %write{23,23}
temp	float	$tmp13	%read{26,26} %write{24,24}
temp	float	$tmp14	%read{26,26} %write{25,25}
temp	float	$tmp15	%read{27,27} %write{26,26}
temp	float	$tmp16	%read{29,29} %write{28,28}
temp	float	$tmp17	%read{31,31} %write{29,29}
temp	float	$tmp18	%read{31,31} %write{30,30}
temp	float	$tmp19	%read{32,32} %write{31,31}
temp	float	$tmp20	%read{34,34} %write{33,33}
temp	float	$tmp21	%read{36,36} %write{35,35}
temp	float	$tmp22	%read{38,38} %write{36,36}
temp	float	$tmp23	%read{38,38} %write{37,37}
temp	float	$tmp24	%read{39,39} %write{38,38}
temp	float	$tmp25	%read{41,41} %write{40,40}
temp	float	$tmp26	%read{43,43} %write{41,41}
temp	float	$tmp27	%read{43,43} %write{42,42}
temp	float	$tmp28	%read{44,44} %write{43,43}
temp	float	$tmp29	%read{46,46} %write{45,45}
temp	float	$tmp30	%read{51,51} %write{50,50}
temp	float	$tmp31	%read{53,53} %write{52,52}
temp	float	$tmp32	%read{55,55} %write{54,54}
temp	float	$tmp33	%read{57,57} %write{56,56}
temp	float	$tmp34	%read{59,59} %write{58,58}
temp	float	$tmp35	%read{61,61} %write{60,60}
temp	float	$tmp36	%read{64,64} %write{63,63}
temp	float	$tmp37	%read{66,66} %write{65,65}
temp	float	$tmp38	%read{68,68} %write{67,67}
temp	float	$tmp39	%read{70,70} %write{69,69}
temp	float	$tmp40	%read{72,72} %write{70,70}
temp	float	$tmp41	%read{72,72} %write{71,71}
temp	float	$tmp42	%read{73,73} %write{72,72}
temp	float	$tmp43	%read{75,75} %write{74,74}
temp	float	$tmp44	%read{77,77} %write{75,75}
temp	float	$tmp45	%read{77,77} %write{76,76}
temp	float	$tmp46	%read{78,78} %write{77,77}
temp	float	$tmp47	%read{80,80} %write{79,79}
temp	float	$tmp48	%read{82,82} %write{81,81}
temp	float	$tmp49	%read{84,84} %write{82,82}
temp	float	$tmp50	%read{84,84} %write{83,83}
temp	float	$tmp51	%read{85,85} %write{84,84}
temp	float	$tmp52	%read{87,87} %write{86,86}
temp	float	$tmp53	%read{89,89} %write{87,87}
temp	float	$tmp54	%read{89,89} %write{88,88}
temp	float	$tmp55	%read{90,90} %write{89,89}
temp	float	$tmp56	%read{92,92} %write{91,91}
temp	point	$tmp57	%read{96,96} %write{95,95}
temp	point	$tmp58	%read{95,95} %write{94,94}
const	string	$const7	"x_axis"		%read{100,100} %write{2147483647,-1}
temp	int	$tmp59	%read{101,101} %write{100,100}
temp	point	$tmp60	%read{154,154} %write{153,153}
temp	point	$tmp61	%read{151,151} %write{102,102}
const	point	$const8	0 0 0		%read{104,319} %write{2147483647,-1}
const	vector	$const9	1 0 0		%read{104,104} %write{2147483647,-1}
const	string	$const10	"rotate"		%read{103,269} %write{2147483647,-1}
temp	vector	$tmp64	%read{105,105} %write{104,104}
temp	float	$tmp65	%read{115,115} %write{111,111}
temp	float	$tmp66	%read{113,113} %write{112,112}
temp	float	$tmp67	%read{114,114} %write{113,113}
temp	float	$tmp68	%read{115,115} %write{114,114}
temp	float	$tmp69	%read{150,150} %write{115,115}
temp	float	$tmp70	%read{117,117} %write{116,116}
temp	float	$tmp71	%read{119,119} %write{117,117}
temp	float	$tmp72	%read{119,119} %write{118,118}
temp	float	$tmp73	%read{150,150} %write{119,119}
temp	float	$tmp74	%read{121,121} %write{120,120}
temp	float	$tmp75	%read{123,123} %write{121,121}
temp	float	$tmp76	%read{123,123} %write{122,122}
temp	float	$tmp77	%read{150,150} %write{123,123}
const	float	$const11	0		%read{150,316} %write{2147483647,-1}
temp	float	$tmp78	%read{125,125} %write{124,124}
temp	float	$tmp79	%read{127,127} %write{125,125}
temp	float	$tmp80	%read{127,127} %write{126,126}
temp	float	$tmp81	%read{150,150} %write{127,127}
temp	float	$tmp82	%read{132,132} %write{128,128}
temp	float	$tmp83	%read{130,130} %write{129,129}
temp	float	$tmp84	%read{131,131} %write{130,130}
temp	float	$tmp85	%read{132,132} %write{131,131}
temp	float	$tmp86	%read{150,150} %write{132,132}
temp	float	$tmp87	%read{134,134} %write{133,133}
temp	float	$tmp88	%read{136,136} %write{134,134}
temp	float	$tmp89	%read{136,136} %write{135,135}
temp	float	$tmp90	%read{150,150} %write{136,136}
temp	float	$tmp91	%read{138,138} %write{137,137}
temp	float	$tmp92	%read{140,140} %write{138,138}
temp	float	$tmp93	%read{140,140} %write{139,139}
temp	float	$tmp94	%read{150,150} %write{140,140}
temp	float	$tmp95	%read{142,142} %write{141,141}
temp	float	$tmp96	%read{144,144} %write{142,142}
temp	float	$tmp97	%read{144,144} %write{143,143}
temp	float	$tmp98	%read{150,150} %write{144,144}
temp	float	$tmp99	%read{149,149} %write{145,145}
temp	float	$tmp100	%read{147,147} %write{146,146}
temp	float	$tmp101	%read{148,148} %write{147,147}
temp	float	$tmp102	%read{149,149} %write{148,148}
temp	float	$tmp103	%read{150,150} %write{149,149}
temp	vector	$tmp104	%read{153,153} %write{152,152}
temp	vector	$tmp105	%read{152,152} %write{151,151}
const	string	$const12	"y_axis"		%read{155,155} %write{2147483647,-1}
temp	int	$tmp106	%read{156,156} %write{155,155}
temp	point	$tmp107	%read{209,209} %write{208,208}
temp	point	$tmp108	%read{206,206} %write{157,157}
const	vector	$const13	0 1 0		%read{159,159} %write{2147483647,-1}
temp	vector	$tmp111	%read{160,160} %write{159,159}
temp	float	$tmp112	%read{170,170} %write{166,166}
temp	float	$tmp113	%read{168,168} %write{167,167}
temp	float	$tmp114	%read{169,169} %write{168,168}
temp	float	$tmp115	%read{170,170} %write{169,169}
temp	float	$tmp116	%read{205,205} %write{170,170}
temp	float	$tmp117	%read{172,172} %write{171,171}
temp	float	$tmp118	%read{174,174} %write{172,172}
temp	float	$tmp119	%read{174,174} %write{173,173}
temp	float	$tmp120	%read{205,205} %write{174,174}
temp	float	$tmp121	%read{176,176} %write{175,175}
temp	float	$tmp122	%read{178,178} %write{176,176}
temp	float	$tmp123	%read{178,178} %write{177,177}
temp	float	$tmp124	%read{205,205} %write{178,178}
temp	float	$tmp125	%read{180,180} %write{179,179}
temp	float	$tmp126	%read{182,182} %write{180,180}
temp	float	$tmp127	%read{182,182} %write{181,181}
temp	float	$tmp128	%read{205,205} %write{182,182}
temp	float	$tmp129	%read{187,187} %write{183,183}
temp	float	$tmp130	%read{185,185} %write{184,184}
temp	float	$tmp131	%read{186,186} %write{185,185}
temp	float	$tmp132	%read{187,187} %write{186,186}
temp	float	$tmp133	%read{205,205} %write{187,187}
temp	float	$tmp134	%read{189,189} %write{188,188}
temp	float	$tmp135	%read{191,191} %write{189,189}
temp	float	$tmp136	%read{191,191} %write{190,190}
temp	float	$tmp137	%read{205,205} %write{191,191}
temp	float	$tmp138	%read{193,193} %write{192,192}
temp	float	$tmp139	%read{195,195} %write{193,193}
temp	float	$tmp140	%read{195,195} %write{194,194}
temp	float	$tmp141	%read{205,205} %write{195,195}
temp	float	$tmp142	%read{197,197} %write{196,196}
temp	float	$tmp143	%read{199,199} %write{197,197}
temp	float	$tmp144	%read{199,199} %write{198,198}
temp	float	$tmp145	%read{205,205} %write{199,199}
temp	float	$tmp146	%read{204,204} %write{200,200}
temp	float	$tmp147	%read{202,202} %write{201,201}
temp	float	$tmp148	%read{203,203} %write{202,202}
temp	float	$tmp149	%read{204,204} %write{203,203}
temp	float	$tmp150	%read{205,205} %write{204,204}
temp	vector	$tmp151	%read{208,208} %write{207,207}
temp	vector	$tmp152	%read{207,207} %write{206,206}
const	string	$const14	"z_axis"		%read{210,210} %write{2147483647,-1}
temp	int	$tmp153	%read{211,211} %write{210,210}
temp	point	$tmp154	%read{264,264} %write{263,263}
temp	point	$tmp155	%read{261,261} %write{212,212}
const	vector	$const15	0 0 1		%read{214,214} %write{2147483647,-1}
temp	vector	$tmp158	%read{215,215} %write{214,214}
temp	float	$tmp159	%read{225,225} %write{221,221}
temp	float	$tmp160	%read{223,223} %write{222,222}
temp	float	$tmp161	%read{224,224} %write{223,223}
temp	float	$tmp162	%read{225,225} %write{224,224}
temp	float	$tmp163	%read{260,260} %write{225,225}
temp	float	$tmp164	%read{227,227} %write{226,226}
temp	float	$tmp165	%read{229,229} %write{227,227}
temp	float	$tmp166	%read{229,229} %write{228,228}
temp	float	$tmp167	%read{260,260} %write{229,229}
temp	float	$tmp168	%read{231,231} %write{230,230}
temp	float	$tmp169	%read{233,233} %write{231,231}
temp	float	$tmp170	%read{233,233} %write{232,232}
temp	float	$tmp171	%read{260,260} %write{233,233}
temp	float	$tmp172	%read{235,235} %write{234,234}
temp	float	$tmp173	%read{237,237} %write{235,235}
temp	float	$tmp174	%read{237,237} %write{236,236}
temp	float	$tmp175	%read{260,260} %write{237,237}
temp	float	$tmp176	%read{242,242} %write{238,238}
temp	float	$tmp177	%read{240,240} %write{239,239}
temp	float	$tmp178	%read{241,241} %write{240,240}
temp	float	$tmp179	%read{242,242} %write{241,241}
temp	float	$tmp180	%read{260,260} %write{242,242}
temp	float	$tmp181	%read{244,244} %write{243,243}
temp	float	$tmp182	%read{246,246} %write{244,244}
temp	float	$tmp183	%read{246,246} %write{245,245}
temp	float	$tmp184	%read{260,260} %write{246,246}
temp	float	$tmp185	%read{248,248} %write{247,247}
temp	float	$tmp186	%read{250,250} %write{248,248}
temp	float	$tmp187	%read{250,250} %write{249,249}
temp	float	$tmp188	%read{260,260} %write{250,250}
temp	float	$tmp189	%read{252,252} %write{251,251}
temp	float	$tmp190	%read{254,254} %write{252,252}
temp	float	$tmp191	%read{254,254} %write{253,253}
temp	float	$tmp192	%read{260,260} %write{254,254}
temp	float	$tmp193	%read{259,259} %write{255,255}
temp	float	$tmp194	%read{257,257} %write{256,256}
temp	float	$tmp195	%read{258,258} %write{257,257}
temp	float	$tmp196	%read{259,259} %write{258,258}
temp	float	$tmp197	%read{260,260} %write{259,259}
temp	vector	$tmp198	%read{263,263} %write{262,262}
temp	vector	$tmp199	%read{262,262} %write{261,261}
temp	float	$tmp200	%read{266,266} %write{265,265}
temp	int	$tmp201	%read{267,267} %write{266,266}
temp	point	$tmp202	%read{320,320} %write{319,319}
temp	point	$tmp203	%read{317,317} %write{268,268}
temp	vector	$tmp205	%read{271,271} %write{270,270}
temp	float	$tmp206	%read{281,281} %write{277,277}
temp	float	$tmp207	%read{279,279} %write{278,278}
temp	float	$tmp208	%read{280,280} %write{279,279}
temp	float	$tmp209	%read{281,281} %write{280,280}
temp	float	$tmp210	%read{316,316} %write{281,281}
temp	float	$tmp211	%read{283,283} %write{282,282}
temp	float	$tmp212	%read{285,285} %write{283,283}
temp	float	$tmp213	%read{285,285} %write{284,284}
temp	float	$tmp214	%read{316,316} %write{285,285}
temp	float	$tmp215	%read{287,287} %write{286,286}
temp	float	$tmp216	%read{289,289} %write{287,287}
temp	float	$tmp217	%read{289,289} %write{288,288}
temp	float	$tmp218	%read{316,316} %write{289,289}
temp	float	$tmp219	%read{291,291} %write{290,290}
temp	float	$tmp220	%read{293,293} %write{291,291}
temp	float	$tmp221	%read{293,293} %write{292,292}
temp	float	$tmp222	%read{316,316} %write{293,293}
temp	float	$tmp223	%read{298,298} %write{294,294}
temp	float	$tmp224	%read{296,296} %write{295,295}
temp	float	$tmp225	%read{297,297} %write{296,296}
temp	float	$tmp226	%read{298,298} %write{297,297}
temp	float	$tmp227	%read{316,316} %write{298,298}
temp	float	$tmp228	%read{300,300} %write{299,299}
temp	float	$tmp229	%read{302,302} %write{300,300}
temp	float	$tmp230	%read{302,302} %write{301,301}
temp	float	$tmp231	%read{316,316} %write{302,302}
temp	float	$tmp232	%read{304,304} %write{303,303}
temp	float	$tmp233	%read{306,306} %write{304,304}
temp	float	$tmp234	%read{306,306} %write{305,305}
temp	float	$tmp235	%read{316,316} %write{306,306}
temp	float	$tmp236	%read{308,308} %write{307,307}
temp	float	$tmp237	%read{310,310} %write{308,308}
temp	float	$tmp238	%read{310,310} %write{309,309}
temp	float	$tmp239	%read{316,316} %write{310,310}
temp	float	$tmp240	%read{315,315} %write{311,311}
temp	float	$tmp241	%read{313,313} %write{312,312}
temp	float	$tmp242	%read{314,314} %write{313,313}
temp	float	$tmp243	%read{315,315} %write{314,314}
temp	float	$tmp244	%read{316,316} %write{315,315}
temp	vector	$tmp245	%read{319,319} %write{318,318}
temp	vector	$tmp246	%read{318,318} %write{317,317}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:17
#   if (rotate_type == "euler_xyz") {
	eq		$tmp1 rotate_type $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{17} %argrw{"wrr"}
	if		$tmp1 97 322 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:18
#     matrix rmat = (invert) ? transpose(euler_to_mat(Rotation)) : euler_to_mat(Rotation);
	if		invert 49 94 	%line{18} %argrw{"r"}
	functioncall	$const2 48 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:93
#   float cx = cos(euler[0]);
	compref		$tmp3 Rotation $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{93} %argrw{"wrr"}
	cos		___361_cx $tmp3 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:94
#   float cy = cos(euler[1]);
	compref		$tmp4 Rotation $const4 	%line{94} %argrw{"wrr"}
	cos		___361_cy $tmp4 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:95
#   float cz = cos(euler[2]);
	compref		$tmp5 Rotation $const5 	%line{95} %argrw{"wrr"}
	cos		___361_cz $tmp5 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:96
#   float sx = sin(euler[0]);
	compref		$tmp6 Rotation $const3 	%line{96} %argrw{"wrr"}
	sin		___361_sx $tmp6 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:97
#   float sy = sin(euler[1]);
	compref		$tmp7 Rotation $const4 	%line{97} %argrw{"wrr"}
	sin		___361_sy $tmp7 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:98
#   float sz = sin(euler[2]);
	compref		$tmp8 Rotation $const5 	%line{98} %argrw{"wrr"}
	sin		___361_sz $tmp8 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:99
#   matrix mat = matrix(1.0);
	assign		___361_mat $const6 	%line{99} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:100
#   mat[0][0] = cy * cz;
	mul		$tmp9 ___361_cy ___361_cz 	%line{100} %argrw{"wrr"}
	mxcompassign	___361_mat $const3 $const3 $tmp9 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:101
#   mat[0][1] = cy * sz;
	mul		$tmp10 ___361_cy ___361_sz 	%line{101} %argrw{"wrr"}
	mxcompassign	___361_mat $const3 $const4 $tmp10 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:102
#   mat[0][2] = -sy;
	neg		$tmp11 ___361_sy 	%line{102} %argrw{"wr"}
	mxcompassign	___361_mat $const3 $const5 $tmp11 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:103
#   mat[1][0] = sy * sx * cz - cx * sz;
	mul		$tmp12 ___361_sy ___361_sx 	%line{103} %argrw{"wrr"}
	mul		$tmp13 $tmp12 ___361_cz 	%argrw{"wrr"}
	mul		$tmp14 ___361_cx ___361_sz 	%argrw{"wrr"}
	sub		$tmp15 $tmp13 $tmp14 	%argrw{"wrr"}
	mxcompassign	___361_mat $const4 $const3 $tmp15 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:104
#   mat[1][1] = sy * sx * sz + cx * cz;
	mul		$tmp16 ___361_sy ___361_sx 	%line{104} %argrw{"wrr"}
	mul		$tmp17 $tmp16 ___361_sz 	%argrw{"wrr"}
	mul		$tmp18 ___361_cx ___361_cz 	%argrw{"wrr"}
	add		$tmp19 $tmp17 $tmp18 	%argrw{"wrr"}
	mxcompassign	___361_mat $const4 $const4 $tmp19 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:105
#   mat[1][2] = cy * sx;
	mul		$tmp20 ___361_cy ___361_sx 	%line{105} %argrw{"wrr"}
	mxcompassign	___361_mat $const4 $const5 $tmp20 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:106
#   +mat[2][0] = sy * cx * cz + sx * sz;
	mul		$tmp21 ___361_sy ___361_cx 	%line{106} %argrw{"wrr"}
	mul		$tmp22 $tmp21 ___361_cz 	%argrw{"wrr"}
	mul		$tmp23 ___361_sx ___361_sz 	%argrw{"wrr"}
	add		$tmp24 $tmp22 $tmp23 	%argrw{"wrr"}
	mxcompassign	___361_mat $const5 $const3 $tmp24 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:107
#   mat[2][1] = sy * cx * sz - sx * cz;
	mul		$tmp25 ___361_sy ___361_cx 	%line{107} %argrw{"wrr"}
	mul		$tmp26 $tmp25 ___361_sz 	%argrw{"wrr"}
	mul		$tmp27 ___361_sx ___361_cz 	%argrw{"wrr"}
	sub		$tmp28 $tmp26 $tmp27 	%argrw{"wrr"}
	mxcompassign	___361_mat $const5 $const4 $tmp28 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:108
#   mat[2][2] = cy * cx;
	mul		$tmp29 ___361_cy ___361_cx 	%line{108} %argrw{"wrr"}
	mxcompassign	___361_mat $const5 $const5 $tmp29 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:109
#   return mat;
	assign		$tmp2 ___361_mat 	%line{109} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:18
#     matrix rmat = (invert) ? transpose(euler_to_mat(Rotation)) : euler_to_mat(Rotation);
	transpose	___385_rmat $tmp2 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{18} %argrw{"wr"}
	functioncall	$const2 94 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:93
#   float cx = cos(euler[0]);
	compref		$tmp30 Rotation $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{93} %argrw{"wrr"}
	cos		___361_cx $tmp30 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:94
#   float cy = cos(euler[1]);
	compref		$tmp31 Rotation $const4 	%line{94} %argrw{"wrr"}
	cos		___361_cy $tmp31 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:95
#   float cz = cos(euler[2]);
	compref		$tmp32 Rotation $const5 	%line{95} %argrw{"wrr"}
	cos		___361_cz $tmp32 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:96
#   float sx = sin(euler[0]);
	compref		$tmp33 Rotation $const3 	%line{96} %argrw{"wrr"}
	sin		___361_sx $tmp33 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:97
#   float sy = sin(euler[1]);
	compref		$tmp34 Rotation $const4 	%line{97} %argrw{"wrr"}
	sin		___361_sy $tmp34 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:98
#   float sz = sin(euler[2]);
	compref		$tmp35 Rotation $const5 	%line{98} %argrw{"wrr"}
	sin		___361_sz $tmp35 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:99
#   matrix mat = matrix(1.0);
	assign		___361_mat $const6 	%line{99} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:100
#   mat[0][0] = cy * cz;
	mul		$tmp36 ___361_cy ___361_cz 	%line{100} %argrw{"wrr"}
	mxcompassign	___361_mat $const3 $const3 $tmp36 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:101
#   mat[0][1] = cy * sz;
	mul		$tmp37 ___361_cy ___361_sz 	%line{101} %argrw{"wrr"}
	mxcompassign	___361_mat $const3 $const4 $tmp37 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:102
#   mat[0][2] = -sy;
	neg		$tmp38 ___361_sy 	%line{102} %argrw{"wr"}
	mxcompassign	___361_mat $const3 $const5 $tmp38 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:103
#   mat[1][0] = sy * sx * cz - cx * sz;
	mul		$tmp39 ___361_sy ___361_sx 	%line{103} %argrw{"wrr"}
	mul		$tmp40 $tmp39 ___361_cz 	%argrw{"wrr"}
	mul		$tmp41 ___361_cx ___361_sz 	%argrw{"wrr"}
	sub		$tmp42 $tmp40 $tmp41 	%argrw{"wrr"}
	mxcompassign	___361_mat $const4 $const3 $tmp42 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:104
#   mat[1][1] = sy * sx * sz + cx * cz;
	mul		$tmp43 ___361_sy ___361_sx 	%line{104} %argrw{"wrr"}
	mul		$tmp44 $tmp43 ___361_sz 	%argrw{"wrr"}
	mul		$tmp45 ___361_cx ___361_cz 	%argrw{"wrr"}
	add		$tmp46 $tmp44 $tmp45 	%argrw{"wrr"}
	mxcompassign	___361_mat $const4 $const4 $tmp46 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:105
#   mat[1][2] = cy * sx;
	mul		$tmp47 ___361_cy ___361_sx 	%line{105} %argrw{"wrr"}
	mxcompassign	___361_mat $const4 $const5 $tmp47 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:106
#   +mat[2][0] = sy * cx * cz + sx * sz;
	mul		$tmp48 ___361_sy ___361_cx 	%line{106} %argrw{"wrr"}
	mul		$tmp49 $tmp48 ___361_cz 	%argrw{"wrr"}
	mul		$tmp50 ___361_sx ___361_sz 	%argrw{"wrr"}
	add		$tmp51 $tmp49 $tmp50 	%argrw{"wrr"}
	mxcompassign	___361_mat $const5 $const3 $tmp51 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:107
#   mat[2][1] = sy * cx * sz - sx * cz;
	mul		$tmp52 ___361_sy ___361_cx 	%line{107} %argrw{"wrr"}
	mul		$tmp53 $tmp52 ___361_sz 	%argrw{"wrr"}
	mul		$tmp54 ___361_sx ___361_cz 	%argrw{"wrr"}
	sub		$tmp55 $tmp53 $tmp54 	%argrw{"wrr"}
	mxcompassign	___361_mat $const5 $const4 $tmp55 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:108
#   mat[2][2] = cy * cx;
	mul		$tmp56 ___361_cy ___361_cx 	%line{108} %argrw{"wrr"}
	mxcompassign	___361_mat $const5 $const5 $tmp56 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:109
#   return mat;
	assign		___385_rmat ___361_mat 	%line{109} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:19
#     VectorOut = transform(rmat, VectorIn - Center) + Center;
	sub		$tmp58 VectorIn Center 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{19} %argrw{"wrr"}
	transform	$tmp57 ___385_rmat $tmp58 	%argrw{"wrr"}
	add		VectorOut $tmp57 Center 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:22
#     float a = (invert) ? -Angle : Angle;
	if		invert 99 100 	%line{22} %argrw{"r"}
	neg		___386_a Angle 	%argrw{"wr"}
	assign		___386_a Angle 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:23
#     if (rotate_type == "x_axis") {
	eq		$tmp59 rotate_type $const7 	%line{23} %argrw{"wrr"}
	if		$tmp59 155 322 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:24
#       VectorOut = rotate(VectorIn - Center, a, point(0.0), vector(1.0, 0.0, 0.0)) + Center;
	sub		$tmp61 VectorIn Center 	%line{24} %argrw{"wrr"}
	functioncall	$const10 154 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:270
#     vector axis = normalize (b - a);
	sub		$tmp64 $const9 $const8 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{270} %argrw{"wrr"}
	normalize	___257_axis $tmp64 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:272
#     sincos (angle, sinang, cosang);
	sincos		___386_a ___257_sinang ___257_cosang 	%line{272} %argrw{"rww"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:273
#     float cosang1 = 1.0 - cosang;
	sub		___257_cosang1 $const6 ___257_cosang 	%line{273} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:274
#     float x = axis[0], y = axis[1], z = axis[2];
	compref		___257_x ___257_axis $const3 	%line{274} %argrw{"wrr"}
	compref		___257_y ___257_axis $const4 	%argrw{"wrr"}
	compref		___257_z ___257_axis $const5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	mul		$tmp65 ___257_x ___257_x 	%line{275} %argrw{"wrr"}
	mul		$tmp66 ___257_x ___257_x 	%argrw{"wrr"}
	sub		$tmp67 $const6 $tmp66 	%argrw{"wrr"}
	mul		$tmp68 $tmp67 ___257_cosang 	%argrw{"wrr"}
	add		$tmp69 $tmp65 $tmp68 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:276
#                        x * y * cosang1 + z * sinang,
	mul		$tmp70 ___257_x ___257_y 	%line{276} %argrw{"wrr"}
	mul		$tmp71 $tmp70 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp72 ___257_z ___257_sinang 	%argrw{"wrr"}
	add		$tmp73 $tmp71 $tmp72 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:277
#                        x * z * cosang1 - y * sinang,
	mul		$tmp74 ___257_x ___257_z 	%line{277} %argrw{"wrr"}
	mul		$tmp75 $tmp74 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp76 ___257_y ___257_sinang 	%argrw{"wrr"}
	sub		$tmp77 $tmp75 $tmp76 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:279
#                        x * y * cosang1 - z * sinang,
	mul		$tmp78 ___257_x ___257_y 	%line{279} %argrw{"wrr"}
	mul		$tmp79 $tmp78 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp80 ___257_z ___257_sinang 	%argrw{"wrr"}
	sub		$tmp81 $tmp79 $tmp80 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:280
#                        y * y + (1.0 - y * y) * cosang,
	mul		$tmp82 ___257_y ___257_y 	%line{280} %argrw{"wrr"}
	mul		$tmp83 ___257_y ___257_y 	%argrw{"wrr"}
	sub		$tmp84 $const6 $tmp83 	%argrw{"wrr"}
	mul		$tmp85 $tmp84 ___257_cosang 	%argrw{"wrr"}
	add		$tmp86 $tmp82 $tmp85 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:281
#                        y * z * cosang1 + x * sinang,
	mul		$tmp87 ___257_y ___257_z 	%line{281} %argrw{"wrr"}
	mul		$tmp88 $tmp87 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp89 ___257_x ___257_sinang 	%argrw{"wrr"}
	add		$tmp90 $tmp88 $tmp89 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:283
#                        x * z * cosang1 + y * sinang,
	mul		$tmp91 ___257_x ___257_z 	%line{283} %argrw{"wrr"}
	mul		$tmp92 $tmp91 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp93 ___257_y ___257_sinang 	%argrw{"wrr"}
	add		$tmp94 $tmp92 $tmp93 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:284
#                        y * z * cosang1 - x * sinang,
	mul		$tmp95 ___257_y ___257_z 	%line{284} %argrw{"wrr"}
	mul		$tmp96 $tmp95 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp97 ___257_x ___257_sinang 	%argrw{"wrr"}
	sub		$tmp98 $tmp96 $tmp97 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:285
#                        z * z + (1.0 - z * z) * cosang,
	mul		$tmp99 ___257_z ___257_z 	%line{285} %argrw{"wrr"}
	mul		$tmp100 ___257_z ___257_z 	%argrw{"wrr"}
	sub		$tmp101 $const6 $tmp100 	%argrw{"wrr"}
	mul		$tmp102 $tmp101 ___257_cosang 	%argrw{"wrr"}
	add		$tmp103 $tmp99 $tmp102 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	matrix		___257_M $tmp69 $tmp73 $tmp77 $const11 $tmp81 $tmp86 $tmp90 $const11 $tmp94 $tmp98 $tmp103 $const11 $const11 $const11 $const11 $const6 	%line{275} %argrw{"wrrrrrrrrrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:288
#     return transform (M, p-a) + a;
	sub		$tmp105 $tmp61 $const8 	%line{288} %argrw{"wrr"}
	transformv	$tmp104 ___257_M $tmp105 	%argrw{"wrr"}
	add		$tmp60 $tmp104 $const8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:24
#       VectorOut = rotate(VectorIn - Center, a, point(0.0), vector(1.0, 0.0, 0.0)) + Center;
	add		VectorOut $tmp60 Center 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{24} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:26
#     else if (rotate_type == "y_axis") {
	eq		$tmp106 rotate_type $const12 	%line{26} %argrw{"wrr"}
	if		$tmp106 210 322 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:27
#       VectorOut = rotate(VectorIn - Center, a, point(0.0), vector(0.0, 1.0, 0.0)) + Center;
	sub		$tmp108 VectorIn Center 	%line{27} %argrw{"wrr"}
	functioncall	$const10 209 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:270
#     vector axis = normalize (b - a);
	sub		$tmp111 $const13 $const8 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{270} %argrw{"wrr"}
	normalize	___257_axis $tmp111 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:272
#     sincos (angle, sinang, cosang);
	sincos		___386_a ___257_sinang ___257_cosang 	%line{272} %argrw{"rww"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:273
#     float cosang1 = 1.0 - cosang;
	sub		___257_cosang1 $const6 ___257_cosang 	%line{273} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:274
#     float x = axis[0], y = axis[1], z = axis[2];
	compref		___257_x ___257_axis $const3 	%line{274} %argrw{"wrr"}
	compref		___257_y ___257_axis $const4 	%argrw{"wrr"}
	compref		___257_z ___257_axis $const5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	mul		$tmp112 ___257_x ___257_x 	%line{275} %argrw{"wrr"}
	mul		$tmp113 ___257_x ___257_x 	%argrw{"wrr"}
	sub		$tmp114 $const6 $tmp113 	%argrw{"wrr"}
	mul		$tmp115 $tmp114 ___257_cosang 	%argrw{"wrr"}
	add		$tmp116 $tmp112 $tmp115 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:276
#                        x * y * cosang1 + z * sinang,
	mul		$tmp117 ___257_x ___257_y 	%line{276} %argrw{"wrr"}
	mul		$tmp118 $tmp117 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp119 ___257_z ___257_sinang 	%argrw{"wrr"}
	add		$tmp120 $tmp118 $tmp119 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:277
#                        x * z * cosang1 - y * sinang,
	mul		$tmp121 ___257_x ___257_z 	%line{277} %argrw{"wrr"}
	mul		$tmp122 $tmp121 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp123 ___257_y ___257_sinang 	%argrw{"wrr"}
	sub		$tmp124 $tmp122 $tmp123 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:279
#                        x * y * cosang1 - z * sinang,
	mul		$tmp125 ___257_x ___257_y 	%line{279} %argrw{"wrr"}
	mul		$tmp126 $tmp125 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp127 ___257_z ___257_sinang 	%argrw{"wrr"}
	sub		$tmp128 $tmp126 $tmp127 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:280
#                        y * y + (1.0 - y * y) * cosang,
	mul		$tmp129 ___257_y ___257_y 	%line{280} %argrw{"wrr"}
	mul		$tmp130 ___257_y ___257_y 	%argrw{"wrr"}
	sub		$tmp131 $const6 $tmp130 	%argrw{"wrr"}
	mul		$tmp132 $tmp131 ___257_cosang 	%argrw{"wrr"}
	add		$tmp133 $tmp129 $tmp132 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:281
#                        y * z * cosang1 + x * sinang,
	mul		$tmp134 ___257_y ___257_z 	%line{281} %argrw{"wrr"}
	mul		$tmp135 $tmp134 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp136 ___257_x ___257_sinang 	%argrw{"wrr"}
	add		$tmp137 $tmp135 $tmp136 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:283
#                        x * z * cosang1 + y * sinang,
	mul		$tmp138 ___257_x ___257_z 	%line{283} %argrw{"wrr"}
	mul		$tmp139 $tmp138 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp140 ___257_y ___257_sinang 	%argrw{"wrr"}
	add		$tmp141 $tmp139 $tmp140 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:284
#                        y * z * cosang1 - x * sinang,
	mul		$tmp142 ___257_y ___257_z 	%line{284} %argrw{"wrr"}
	mul		$tmp143 $tmp142 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp144 ___257_x ___257_sinang 	%argrw{"wrr"}
	sub		$tmp145 $tmp143 $tmp144 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:285
#                        z * z + (1.0 - z * z) * cosang,
	mul		$tmp146 ___257_z ___257_z 	%line{285} %argrw{"wrr"}
	mul		$tmp147 ___257_z ___257_z 	%argrw{"wrr"}
	sub		$tmp148 $const6 $tmp147 	%argrw{"wrr"}
	mul		$tmp149 $tmp148 ___257_cosang 	%argrw{"wrr"}
	add		$tmp150 $tmp146 $tmp149 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	matrix		___257_M $tmp116 $tmp120 $tmp124 $const11 $tmp128 $tmp133 $tmp137 $const11 $tmp141 $tmp145 $tmp150 $const11 $const11 $const11 $const11 $const6 	%line{275} %argrw{"wrrrrrrrrrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:288
#     return transform (M, p-a) + a;
	sub		$tmp152 $tmp108 $const8 	%line{288} %argrw{"wrr"}
	transformv	$tmp151 ___257_M $tmp152 	%argrw{"wrr"}
	add		$tmp107 $tmp151 $const8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:27
#       VectorOut = rotate(VectorIn - Center, a, point(0.0), vector(0.0, 1.0, 0.0)) + Center;
	add		VectorOut $tmp107 Center 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{27} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:29
#     else if (rotate_type == "z_axis") {
	eq		$tmp153 rotate_type $const14 	%line{29} %argrw{"wrr"}
	if		$tmp153 265 322 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:30
#       VectorOut = rotate(VectorIn - Center, a, point(0.0), vector(0.0, 0.0, 1.0)) + Center;
	sub		$tmp155 VectorIn Center 	%line{30} %argrw{"wrr"}
	functioncall	$const10 264 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:270
#     vector axis = normalize (b - a);
	sub		$tmp158 $const15 $const8 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{270} %argrw{"wrr"}
	normalize	___257_axis $tmp158 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:272
#     sincos (angle, sinang, cosang);
	sincos		___386_a ___257_sinang ___257_cosang 	%line{272} %argrw{"rww"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:273
#     float cosang1 = 1.0 - cosang;
	sub		___257_cosang1 $const6 ___257_cosang 	%line{273} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:274
#     float x = axis[0], y = axis[1], z = axis[2];
	compref		___257_x ___257_axis $const3 	%line{274} %argrw{"wrr"}
	compref		___257_y ___257_axis $const4 	%argrw{"wrr"}
	compref		___257_z ___257_axis $const5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	mul		$tmp159 ___257_x ___257_x 	%line{275} %argrw{"wrr"}
	mul		$tmp160 ___257_x ___257_x 	%argrw{"wrr"}
	sub		$tmp161 $const6 $tmp160 	%argrw{"wrr"}
	mul		$tmp162 $tmp161 ___257_cosang 	%argrw{"wrr"}
	add		$tmp163 $tmp159 $tmp162 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:276
#                        x * y * cosang1 + z * sinang,
	mul		$tmp164 ___257_x ___257_y 	%line{276} %argrw{"wrr"}
	mul		$tmp165 $tmp164 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp166 ___257_z ___257_sinang 	%argrw{"wrr"}
	add		$tmp167 $tmp165 $tmp166 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:277
#                        x * z * cosang1 - y * sinang,
	mul		$tmp168 ___257_x ___257_z 	%line{277} %argrw{"wrr"}
	mul		$tmp169 $tmp168 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp170 ___257_y ___257_sinang 	%argrw{"wrr"}
	sub		$tmp171 $tmp169 $tmp170 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:279
#                        x * y * cosang1 - z * sinang,
	mul		$tmp172 ___257_x ___257_y 	%line{279} %argrw{"wrr"}
	mul		$tmp173 $tmp172 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp174 ___257_z ___257_sinang 	%argrw{"wrr"}
	sub		$tmp175 $tmp173 $tmp174 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:280
#                        y * y + (1.0 - y * y) * cosang,
	mul		$tmp176 ___257_y ___257_y 	%line{280} %argrw{"wrr"}
	mul		$tmp177 ___257_y ___257_y 	%argrw{"wrr"}
	sub		$tmp178 $const6 $tmp177 	%argrw{"wrr"}
	mul		$tmp179 $tmp178 ___257_cosang 	%argrw{"wrr"}
	add		$tmp180 $tmp176 $tmp179 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:281
#                        y * z * cosang1 + x * sinang,
	mul		$tmp181 ___257_y ___257_z 	%line{281} %argrw{"wrr"}
	mul		$tmp182 $tmp181 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp183 ___257_x ___257_sinang 	%argrw{"wrr"}
	add		$tmp184 $tmp182 $tmp183 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:283
#                        x * z * cosang1 + y * sinang,
	mul		$tmp185 ___257_x ___257_z 	%line{283} %argrw{"wrr"}
	mul		$tmp186 $tmp185 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp187 ___257_y ___257_sinang 	%argrw{"wrr"}
	add		$tmp188 $tmp186 $tmp187 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:284
#                        y * z * cosang1 - x * sinang,
	mul		$tmp189 ___257_y ___257_z 	%line{284} %argrw{"wrr"}
	mul		$tmp190 $tmp189 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp191 ___257_x ___257_sinang 	%argrw{"wrr"}
	sub		$tmp192 $tmp190 $tmp191 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:285
#                        z * z + (1.0 - z * z) * cosang,
	mul		$tmp193 ___257_z ___257_z 	%line{285} %argrw{"wrr"}
	mul		$tmp194 ___257_z ___257_z 	%argrw{"wrr"}
	sub		$tmp195 $const6 $tmp194 	%argrw{"wrr"}
	mul		$tmp196 $tmp195 ___257_cosang 	%argrw{"wrr"}
	add		$tmp197 $tmp193 $tmp196 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	matrix		___257_M $tmp163 $tmp167 $tmp171 $const11 $tmp175 $tmp180 $tmp184 $const11 $tmp188 $tmp192 $tmp197 $const11 $const11 $const11 $const11 $const6 	%line{275} %argrw{"wrrrrrrrrrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:288
#     return transform (M, p-a) + a;
	sub		$tmp199 $tmp155 $const8 	%line{288} %argrw{"wrr"}
	transformv	$tmp198 ___257_M $tmp199 	%argrw{"wrr"}
	add		$tmp154 $tmp198 $const8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:30
#       VectorOut = rotate(VectorIn - Center, a, point(0.0), vector(0.0, 0.0, 1.0)) + Center;
	add		VectorOut $tmp154 Center 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{30} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:33
#       VectorOut = (length(Axis) != 0.0) ? rotate(VectorIn - Center, a, point(0.0), Axis) + Center :
	length		$tmp200 Axis 	%line{33} %argrw{"wr"}
	neq		$tmp201 $tmp200 $const11 	%argrw{"wrr"}
	if		$tmp201 321 322 	%argrw{"r"}
	sub		$tmp203 VectorIn Center 	%argrw{"wrr"}
	functioncall	$const10 320 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:270
#     vector axis = normalize (b - a);
	sub		$tmp205 Axis $const8 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{270} %argrw{"wrr"}
	normalize	___257_axis $tmp205 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:272
#     sincos (angle, sinang, cosang);
	sincos		___386_a ___257_sinang ___257_cosang 	%line{272} %argrw{"rww"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:273
#     float cosang1 = 1.0 - cosang;
	sub		___257_cosang1 $const6 ___257_cosang 	%line{273} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:274
#     float x = axis[0], y = axis[1], z = axis[2];
	compref		___257_x ___257_axis $const3 	%line{274} %argrw{"wrr"}
	compref		___257_y ___257_axis $const4 	%argrw{"wrr"}
	compref		___257_z ___257_axis $const5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	mul		$tmp206 ___257_x ___257_x 	%line{275} %argrw{"wrr"}
	mul		$tmp207 ___257_x ___257_x 	%argrw{"wrr"}
	sub		$tmp208 $const6 $tmp207 	%argrw{"wrr"}
	mul		$tmp209 $tmp208 ___257_cosang 	%argrw{"wrr"}
	add		$tmp210 $tmp206 $tmp209 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:276
#                        x * y * cosang1 + z * sinang,
	mul		$tmp211 ___257_x ___257_y 	%line{276} %argrw{"wrr"}
	mul		$tmp212 $tmp211 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp213 ___257_z ___257_sinang 	%argrw{"wrr"}
	add		$tmp214 $tmp212 $tmp213 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:277
#                        x * z * cosang1 - y * sinang,
	mul		$tmp215 ___257_x ___257_z 	%line{277} %argrw{"wrr"}
	mul		$tmp216 $tmp215 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp217 ___257_y ___257_sinang 	%argrw{"wrr"}
	sub		$tmp218 $tmp216 $tmp217 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:279
#                        x * y * cosang1 - z * sinang,
	mul		$tmp219 ___257_x ___257_y 	%line{279} %argrw{"wrr"}
	mul		$tmp220 $tmp219 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp221 ___257_z ___257_sinang 	%argrw{"wrr"}
	sub		$tmp222 $tmp220 $tmp221 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:280
#                        y * y + (1.0 - y * y) * cosang,
	mul		$tmp223 ___257_y ___257_y 	%line{280} %argrw{"wrr"}
	mul		$tmp224 ___257_y ___257_y 	%argrw{"wrr"}
	sub		$tmp225 $const6 $tmp224 	%argrw{"wrr"}
	mul		$tmp226 $tmp225 ___257_cosang 	%argrw{"wrr"}
	add		$tmp227 $tmp223 $tmp226 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:281
#                        y * z * cosang1 + x * sinang,
	mul		$tmp228 ___257_y ___257_z 	%line{281} %argrw{"wrr"}
	mul		$tmp229 $tmp228 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp230 ___257_x ___257_sinang 	%argrw{"wrr"}
	add		$tmp231 $tmp229 $tmp230 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:283
#                        x * z * cosang1 + y * sinang,
	mul		$tmp232 ___257_x ___257_z 	%line{283} %argrw{"wrr"}
	mul		$tmp233 $tmp232 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp234 ___257_y ___257_sinang 	%argrw{"wrr"}
	add		$tmp235 $tmp233 $tmp234 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:284
#                        y * z * cosang1 - x * sinang,
	mul		$tmp236 ___257_y ___257_z 	%line{284} %argrw{"wrr"}
	mul		$tmp237 $tmp236 ___257_cosang1 	%argrw{"wrr"}
	mul		$tmp238 ___257_x ___257_sinang 	%argrw{"wrr"}
	sub		$tmp239 $tmp237 $tmp238 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:285
#                        z * z + (1.0 - z * z) * cosang,
	mul		$tmp240 ___257_z ___257_z 	%line{285} %argrw{"wrr"}
	mul		$tmp241 ___257_z ___257_z 	%argrw{"wrr"}
	sub		$tmp242 $const6 $tmp241 	%argrw{"wrr"}
	mul		$tmp243 $tmp242 ___257_cosang 	%argrw{"wrr"}
	add		$tmp244 $tmp240 $tmp243 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:275
#     matrix M = matrix (x * x + (1.0 - x * x) * cosang,
	matrix		___257_M $tmp210 $tmp214 $tmp218 $const11 $tmp222 $tmp227 $tmp231 $const11 $tmp235 $tmp239 $tmp244 $const11 $const11 $const11 $const11 $const6 	%line{275} %argrw{"wrrrrrrrrrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:288
#     return transform (M, p-a) + a;
	sub		$tmp246 $tmp203 $const8 	%line{288} %argrw{"wrr"}
	transformv	$tmp245 ___257_M $tmp246 	%argrw{"wrr"}
	add		$tmp202 $tmp245 $const8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl:33
#       VectorOut = (length(Axis) != 0.0) ? rotate(VectorIn - Center, a, point(0.0), Axis) + Center :
	add		VectorOut $tmp202 Center 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_vector_rotate.osl"} %line{33} %argrw{"wrr"}
	assign		VectorOut VectorIn 	%argrw{"wr"}
	end
