OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_value.oso
shader node_value
param	float	value_value	0		%read{0,0} %write{2147483647,-1}
param	vector	vector_value	0 0 0		%read{1,1} %write{2147483647,-1}
param	color	color_value	0 0 0		%read{2,2} %write{2147483647,-1}
oparam	float	Value	0		%read{2147483647,-1} %write{0,0}
oparam	vector	Vector	0 0 0		%read{2147483647,-1} %write{1,1}
oparam	color	Color	0 0 0		%read{2147483647,-1} %write{2,2}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_value.osl:14
#   Value = value_value;
	assign		Value value_value 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_value.osl"} %line{14} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_value.osl:15
#   Vector = vector_value;
	assign		Vector vector_value 	%line{15} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_value.osl:16
#   Color = color_value;
	assign		Color color_value 	%line{16} %argrw{"wr"}
	end
