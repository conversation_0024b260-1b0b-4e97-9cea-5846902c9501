OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_math.oso
shader node_math
param	string	math_type	"add"		%read{0,220} %write{2147483647,-1}
param	float	Value1	0.5		%read{2,222} %write{2147483647,-1}
param	float	Value2	0.5		%read{2,223} %write{2147483647,-1}
param	float	Value3	0.5		%read{120,235} %write{2147483647,-1}
oparam	float	Value	0		%read{2147483647,-1} %write{2,241}
local	float	___351_h	%read{212,234} %write{210,231}
local	float	___358_range	%read{121,126} %write{120,120}
const	string	$const1	"add"		%read{0,0} %write{2147483647,-1}
temp	int	$tmp1	%read{1,1} %write{0,0}
const	string	$const2	"subtract"		%read{3,3} %write{2147483647,-1}
temp	int	$tmp2	%read{4,4} %write{3,3}
const	string	$const3	"multiply"		%read{6,6} %write{2147483647,-1}
temp	int	$tmp3	%read{7,7} %write{6,6}
const	string	$const4	"divide"		%read{9,9} %write{2147483647,-1}
temp	int	$tmp4	%read{10,10} %write{9,9}
const	string	$const5	"safe_divide"		%read{11,109} %write{2147483647,-1}
const	float	$const6	0		%read{12,230} %write{2147483647,-1}
temp	int	$tmp5	%read{13,13} %write{12,12}
const	string	$const7	"power"		%read{17,17} %write{2147483647,-1}
temp	int	$tmp6	%read{18,18} %write{17,17}
const	string	$const8	"logarithm"		%read{20,20} %write{2147483647,-1}
temp	int	$tmp7	%read{21,21} %write{20,20}
const	string	$const9	"safe_log"		%read{22,22} %write{2147483647,-1}
temp	int	$tmp8	%read{24,24} %write{23,23}
temp	int	$tmp9	%read{25,29} %write{24,28}
const	int	$const10	0		%read{24,192} %write{2147483647,-1}
temp	int	$tmp10	%read{27,27} %write{26,26}
temp	int	$tmp11	%read{28,28} %write{27,27}
temp	float	$tmp12	%read{32,32} %write{30,30}
temp	float	$tmp13	%read{32,32} %write{31,31}
const	string	$const11	"sqrt"		%read{35,35} %write{2147483647,-1}
temp	int	$tmp14	%read{36,36} %write{35,35}
const	string	$const12	"safe_sqrt"		%read{37,37} %write{2147483647,-1}
temp	int	$tmp15	%read{39,39} %write{38,38}
const	string	$const13	"inversesqrt"		%read{43,43} %write{2147483647,-1}
temp	int	$tmp16	%read{44,44} %write{43,43}
const	string	$const14	"absolute"		%read{46,46} %write{2147483647,-1}
temp	int	$tmp17	%read{47,47} %write{46,46}
const	string	$const15	"radians"		%read{49,51} %write{2147483647,-1}
temp	int	$tmp18	%read{50,50} %write{49,49}
const	float	$const16	0.0174532924		%read{52,52} %write{2147483647,-1}
const	string	$const17	"degrees"		%read{53,55} %write{2147483647,-1}
temp	int	$tmp19	%read{54,54} %write{53,53}
const	float	$const18	57.2957764		%read{56,56} %write{2147483647,-1}
const	string	$const19	"minimum"		%read{57,57} %write{2147483647,-1}
temp	int	$tmp20	%read{58,58} %write{57,57}
const	string	$const20	"maximum"		%read{60,60} %write{2147483647,-1}
temp	int	$tmp21	%read{61,61} %write{60,60}
const	string	$const21	"less_than"		%read{63,63} %write{2147483647,-1}
temp	int	$tmp22	%read{64,64} %write{63,63}
temp	int	$tmp23	%read{66,66} %write{65,65}
const	string	$const22	"greater_than"		%read{67,67} %write{2147483647,-1}
temp	int	$tmp24	%read{68,68} %write{67,67}
temp	int	$tmp25	%read{70,70} %write{69,69}
const	string	$const23	"round"		%read{71,71} %write{2147483647,-1}
temp	int	$tmp26	%read{72,72} %write{71,71}
const	float	$const24	0.5		%read{73,73} %write{2147483647,-1}
temp	float	$tmp27	%read{74,74} %write{73,73}
const	string	$const25	"floor"		%read{75,75} %write{2147483647,-1}
temp	int	$tmp28	%read{76,76} %write{75,75}
const	string	$const26	"ceil"		%read{78,78} %write{2147483647,-1}
temp	int	$tmp29	%read{79,79} %write{78,78}
const	string	$const27	"fraction"		%read{81,81} %write{2147483647,-1}
temp	int	$tmp30	%read{82,82} %write{81,81}
temp	float	$tmp31	%read{84,84} %write{83,83}
const	string	$const28	"modulo"		%read{85,85} %write{2147483647,-1}
temp	int	$tmp32	%read{86,86} %write{85,85}
const	string	$const29	"safe_modulo"		%read{87,87} %write{2147483647,-1}
temp	int	$tmp33	%read{89,89} %write{88,88}
const	string	$const30	"floored_modulo"		%read{93,93} %write{2147483647,-1}
temp	int	$tmp34	%read{94,94} %write{93,93}
const	string	$const31	"safe_floored_modulo"		%read{95,95} %write{2147483647,-1}
temp	int	$tmp35	%read{97,97} %write{96,96}
temp	float	$tmp36	%read{100,100} %write{99,99}
temp	float	$tmp37	%read{99,99} %write{98,98}
temp	float	$tmp38	%read{101,101} %write{100,100}
const	string	$const32	"trunc"		%read{104,104} %write{2147483647,-1}
temp	int	$tmp39	%read{105,105} %write{104,104}
const	string	$const33	"snap"		%read{107,107} %write{2147483647,-1}
temp	int	$tmp40	%read{108,108} %write{107,107}
temp	float	$tmp41	%read{116,116} %write{115,115}
temp	float	$tmp42	%read{115,115} %write{112,113}
temp	int	$tmp43	%read{111,111} %write{110,110}
const	string	$const34	"wrap"		%read{117,119} %write{2147483647,-1}
temp	int	$tmp44	%read{118,118} %write{117,117}
temp	int	$tmp45	%read{122,122} %write{121,121}
temp	float	$tmp46	%read{126,126} %write{125,125}
temp	float	$tmp47	%read{124,124} %write{123,123}
temp	float	$tmp48	%read{125,125} %write{124,124}
temp	float	$tmp49	%read{127,127} %write{126,126}
const	string	$const35	"pingpong"		%read{130,132} %write{2147483647,-1}
temp	int	$tmp50	%read{131,131} %write{130,130}
temp	int	$tmp51	%read{134,134} %write{133,133}
temp	float	$tmp52	%read{141,141} %write{140,140}
temp	float	$tmp53	%read{137,137} %write{135,135}
const	float	$const36	2		%read{136,142} %write{2147483647,-1}
temp	float	$tmp54	%read{137,137} %write{136,136}
temp	float	$tmp55	%read{139,140} %write{137,137}
const	string	$const37	"fract"		%read{138,138} %write{2147483647,-1}
temp	float	$tmp56	%read{140,140} %write{139,139}
temp	float	$tmp57	%read{142,142} %write{141,141}
temp	float	$tmp58	%read{143,143} %write{142,142}
temp	float	$tmp59	%read{144,144} %write{143,143}
const	string	$const38	"sine"		%read{147,147} %write{2147483647,-1}
temp	int	$tmp60	%read{148,148} %write{147,147}
const	string	$const39	"cosine"		%read{150,150} %write{2147483647,-1}
temp	int	$tmp61	%read{151,151} %write{150,150}
const	string	$const40	"tangent"		%read{153,153} %write{2147483647,-1}
temp	int	$tmp62	%read{154,154} %write{153,153}
const	string	$const41	"sinh"		%read{156,156} %write{2147483647,-1}
temp	int	$tmp63	%read{157,157} %write{156,156}
const	string	$const42	"cosh"		%read{159,159} %write{2147483647,-1}
temp	int	$tmp64	%read{160,160} %write{159,159}
const	string	$const43	"tanh"		%read{162,162} %write{2147483647,-1}
temp	int	$tmp65	%read{163,163} %write{162,162}
const	string	$const44	"arcsine"		%read{165,165} %write{2147483647,-1}
temp	int	$tmp66	%read{166,166} %write{165,165}
const	string	$const45	"arccosine"		%read{168,168} %write{2147483647,-1}
temp	int	$tmp67	%read{169,169} %write{168,168}
const	string	$const46	"arctangent"		%read{171,171} %write{2147483647,-1}
temp	int	$tmp68	%read{172,172} %write{171,171}
const	string	$const47	"arctan2"		%read{174,174} %write{2147483647,-1}
temp	int	$tmp69	%read{175,175} %write{174,174}
const	string	$const48	"sign"		%read{177,177} %write{2147483647,-1}
temp	int	$tmp70	%read{178,178} %write{177,177}
const	string	$const49	"exponent"		%read{180,180} %write{2147483647,-1}
temp	int	$tmp71	%read{181,181} %write{180,180}
const	string	$const50	"compare"		%read{183,183} %write{2147483647,-1}
temp	int	$tmp72	%read{184,184} %write{183,183}
temp	int	$tmp73	%read{186,186} %write{185,185}
temp	int	$tmp74	%read{187,194} %write{186,193}
temp	float	$tmp75	%read{191,191} %write{189,189}
temp	float	$tmp76	%read{189,189} %write{188,188}
temp	float	$tmp77	%read{191,191} %write{190,190}
const	float	$const51	9.99999975e-06		%read{190,190} %write{2147483647,-1}
temp	int	$tmp78	%read{192,192} %write{191,191}
temp	int	$tmp79	%read{193,193} %write{192,192}
const	float	$const52	1		%read{195,195} %write{2147483647,-1}
const	string	$const53	"multiply_add"		%read{197,197} %write{2147483647,-1}
temp	int	$tmp80	%read{198,198} %write{197,197}
temp	float	$tmp81	%read{200,200} %write{199,199}
const	string	$const54	"smoothmin"		%read{201,224} %write{2147483647,-1}
temp	int	$tmp82	%read{202,202} %write{201,201}
temp	int	$tmp83	%read{205,205} %write{204,204}
temp	float	$tmp84	%read{210,210} %write{209,209}
temp	float	$tmp85	%read{208,208} %write{207,207}
temp	float	$tmp86	%read{207,207} %write{206,206}
temp	float	$tmp87	%read{209,209} %write{208,208}
temp	float	$tmp88	%read{216,216} %write{211,211}
temp	float	$tmp89	%read{213,213} %write{212,212}
temp	float	$tmp90	%read{214,214} %write{213,213}
temp	float	$tmp91	%read{215,215} %write{214,214}
const	float	$const55	0.166666672		%read{215,236} %write{2147483647,-1}
temp	float	$tmp92	%read{216,216} %write{215,215}
const	string	$const56	"smoothmax"		%read{220,220} %write{2147483647,-1}
temp	int	$tmp93	%read{221,221} %write{220,220}
temp	float	$tmp94	%read{241,241} %write{237,239}
temp	float	$tmp95	%read{227,239} %write{222,222}
temp	float	$tmp96	%read{227,239} %write{223,223}
temp	int	$tmp97	%read{226,226} %write{225,225}
temp	float	$tmp98	%read{231,231} %write{230,230}
temp	float	$tmp99	%read{229,229} %write{228,228}
temp	float	$tmp100	%read{228,228} %write{227,227}
temp	float	$tmp101	%read{230,230} %write{229,229}
temp	float	$tmp102	%read{237,237} %write{232,232}
temp	float	$tmp103	%read{234,234} %write{233,233}
temp	float	$tmp104	%read{235,235} %write{234,234}
temp	float	$tmp105	%read{236,236} %write{235,235}
temp	float	$tmp106	%read{237,237} %write{236,236}
const	string	$const57	"%s"		%read{242,242} %write{2147483647,-1}
const	string	$const58	"Unknown math operator!"		%read{242,242} %write{2147483647,-1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:15
#   if (math_type == "add")
	eq		$tmp1 math_type $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{15} %argrw{"wrr"}
	if		$tmp1 3 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:16
#     Value = Value1 + Value2;
	add		Value Value1 Value2 	%line{16} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:17
#   else if (math_type == "subtract")
	eq		$tmp2 math_type $const2 	%line{17} %argrw{"wrr"}
	if		$tmp2 6 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:18
#     Value = Value1 - Value2;
	sub		Value Value1 Value2 	%line{18} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:19
#   else if (math_type == "multiply")
	eq		$tmp3 math_type $const3 	%line{19} %argrw{"wrr"}
	if		$tmp3 9 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:20
#     Value = Value1 * Value2;
	mul		Value Value1 Value2 	%line{20} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:21
#   else if (math_type == "divide")
	eq		$tmp4 math_type $const4 	%line{21} %argrw{"wrr"}
	if		$tmp4 17 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:22
#     Value = safe_divide(Value1, Value2);
	functioncall	$const5 17 	%line{22} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:7
#   return (b != 0.0) ? a / b : 0.0;
	neq		$tmp5 Value2 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{7} %argrw{"wrr"}
	if		$tmp5 15 16 	%argrw{"r"}
	div		Value Value1 Value2 	%argrw{"wrr"}
	assign		Value $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:23
#   else if (math_type == "power")
	eq		$tmp6 math_type $const7 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{23} %argrw{"wrr"}
	if		$tmp6 20 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:24
#     Value = pow(Value1, Value2);
	pow		Value Value1 Value2 	%line{24} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:25
#   else if (math_type == "logarithm")
	eq		$tmp7 math_type $const8 	%line{25} %argrw{"wrr"}
	if		$tmp7 35 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:26
#     Value = safe_log(Value1, Value2);
	functioncall	$const9 35 	%line{26} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:56
#   return (a > 0.0 && b > 0.0) ? log(a) / log(b) : 0.0;
	gt		$tmp8 Value1 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{56} %argrw{"wrr"}
	neq		$tmp9 $tmp8 $const10 	%argrw{"wrr"}
	if		$tmp9 29 29 	%argrw{"r"}
	gt		$tmp10 Value2 $const6 	%argrw{"wrr"}
	neq		$tmp11 $tmp10 $const10 	%argrw{"wrr"}
	assign		$tmp9 $tmp11 	%argrw{"wr"}
	if		$tmp9 33 34 	%argrw{"r"}
	log		$tmp12 Value1 	%argrw{"wr"}
	log		$tmp13 Value2 	%argrw{"wr"}
	div		Value $tmp12 $tmp13 	%argrw{"wrr"}
	assign		Value $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:27
#   else if (math_type == "sqrt")
	eq		$tmp14 math_type $const11 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{27} %argrw{"wrr"}
	if		$tmp14 43 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:28
#     Value = safe_sqrt(Value1);
	functioncall	$const12 43 	%line{28} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:51
#   return (a > 0.0) ? sqrt(a) : 0.0;
	gt		$tmp15 Value1 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{51} %argrw{"wrr"}
	if		$tmp15 41 42 	%argrw{"r"}
	sqrt		Value Value1 	%argrw{"wr"}
	assign		Value $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:29
#   else if (math_type == "inversesqrt")
	eq		$tmp16 math_type $const13 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{29} %argrw{"wrr"}
	if		$tmp16 46 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:30
#     Value = inversesqrt(Value1);
	inversesqrt	Value Value1 	%line{30} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:31
#   else if (math_type == "absolute")
	eq		$tmp17 math_type $const14 	%line{31} %argrw{"wrr"}
	if		$tmp17 49 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:32
#     Value = fabs(Value1);
	fabs		Value Value1 	%line{32} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:33
#   else if (math_type == "radians")
	eq		$tmp18 math_type $const15 	%line{33} %argrw{"wrr"}
	if		$tmp18 53 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:34
#     Value = radians(Value1);
	functioncall	$const15 53 	%line{34} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:65
# float  radians (float x)  { return x*(M_PI/180.0); }
	mul		Value Value1 $const16 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{65} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:35
#   else if (math_type == "degrees")
	eq		$tmp19 math_type $const17 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{35} %argrw{"wrr"}
	if		$tmp19 57 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:36
#     Value = degrees(Value1);
	functioncall	$const17 57 	%line{36} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:60
# float  degrees (float x)  { return x*(180.0/M_PI); }
	mul		Value Value1 $const18 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{60} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:37
#   else if (math_type == "minimum")
	eq		$tmp20 math_type $const19 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{37} %argrw{"wrr"}
	if		$tmp20 60 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:38
#     Value = min(Value1, Value2);
	min		Value Value1 Value2 	%line{38} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:39
#   else if (math_type == "maximum")
	eq		$tmp21 math_type $const20 	%line{39} %argrw{"wrr"}
	if		$tmp21 63 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:40
#     Value = max(Value1, Value2);
	max		Value Value1 Value2 	%line{40} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:41
#   else if (math_type == "less_than")
	eq		$tmp22 math_type $const21 	%line{41} %argrw{"wrr"}
	if		$tmp22 67 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:42
#     Value = Value1 < Value2;
	lt		$tmp23 Value1 Value2 	%line{42} %argrw{"wrr"}
	assign		Value $tmp23 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:43
#   else if (math_type == "greater_than")
	eq		$tmp24 math_type $const22 	%line{43} %argrw{"wrr"}
	if		$tmp24 71 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:44
#     Value = Value1 > Value2;
	gt		$tmp25 Value1 Value2 	%line{44} %argrw{"wrr"}
	assign		Value $tmp25 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:45
#   else if (math_type == "round")
	eq		$tmp26 math_type $const23 	%line{45} %argrw{"wrr"}
	if		$tmp26 75 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:46
#     Value = floor(Value1 + 0.5);
	add		$tmp27 Value1 $const24 	%line{46} %argrw{"wrr"}
	floor		Value $tmp27 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:47
#   else if (math_type == "floor")
	eq		$tmp28 math_type $const25 	%line{47} %argrw{"wrr"}
	if		$tmp28 78 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:48
#     Value = floor(Value1);
	floor		Value Value1 	%line{48} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:49
#   else if (math_type == "ceil")
	eq		$tmp29 math_type $const26 	%line{49} %argrw{"wrr"}
	if		$tmp29 81 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:50
#     Value = ceil(Value1);
	ceil		Value Value1 	%line{50} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:51
#   else if (math_type == "fraction")
	eq		$tmp30 math_type $const27 	%line{51} %argrw{"wrr"}
	if		$tmp30 85 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:52
#     Value = Value1 - floor(Value1);
	floor		$tmp31 Value1 	%line{52} %argrw{"wr"}
	sub		Value Value1 $tmp31 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:53
#   else if (math_type == "modulo")
	eq		$tmp32 math_type $const28 	%line{53} %argrw{"wrr"}
	if		$tmp32 93 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:54
#     Value = safe_modulo(Value1, Value2);
	functioncall	$const29 93 	%line{54} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:19
#   return (b != 0.0) ? fmod(a, b) : 0.0;
	neq		$tmp33 Value2 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{19} %argrw{"wrr"}
	if		$tmp33 91 92 	%argrw{"r"}
	fmod		Value Value1 Value2 	%argrw{"wrr"}
	assign		Value $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:55
#   else if (math_type == "floored_modulo")
	eq		$tmp34 math_type $const30 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{55} %argrw{"wrr"}
	if		$tmp34 104 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:56
#     Value = safe_floored_modulo(Value1, Value2);
	functioncall	$const31 104 	%line{56} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:24
#   return (b != 0.0) ? a - floor(a / b) * b : 0.0;
	neq		$tmp35 Value2 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{24} %argrw{"wrr"}
	if		$tmp35 102 103 	%argrw{"r"}
	div		$tmp37 Value1 Value2 	%argrw{"wrr"}
	floor		$tmp36 $tmp37 	%argrw{"wr"}
	mul		$tmp38 $tmp36 Value2 	%argrw{"wrr"}
	sub		Value Value1 $tmp38 	%argrw{"wrr"}
	assign		Value $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:57
#   else if (math_type == "trunc")
	eq		$tmp39 math_type $const32 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{57} %argrw{"wrr"}
	if		$tmp39 107 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:58
#     Value = trunc(Value1);
	trunc		Value Value1 	%line{58} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:59
#   else if (math_type == "snap")
	eq		$tmp40 math_type $const33 	%line{59} %argrw{"wrr"}
	if		$tmp40 117 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:60
#     Value = floor(safe_divide(Value1, Value2)) * Value2;
	functioncall	$const5 115 	%line{60} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:7
#   return (b != 0.0) ? a / b : 0.0;
	neq		$tmp43 Value2 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{7} %argrw{"wrr"}
	if		$tmp43 113 114 	%argrw{"r"}
	div		$tmp42 Value1 Value2 	%argrw{"wrr"}
	assign		$tmp42 $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:60
#     Value = floor(safe_divide(Value1, Value2)) * Value2;
	floor		$tmp41 $tmp42 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{60} %argrw{"wr"}
	mul		Value $tmp41 Value2 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:61
#   else if (math_type == "wrap")
	eq		$tmp44 math_type $const34 	%line{61} %argrw{"wrr"}
	if		$tmp44 130 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:62
#     Value = wrap(Value1, Value2, Value3);
	functioncall	$const34 130 	%line{62} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:73
#   float range = max - min;
	sub		___358_range Value2 Value3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{73} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:74
#   return (range != 0.0) ? value - (range * floor((value - min) / range)) : min;
	neq		$tmp45 ___358_range $const6 	%line{74} %argrw{"wrr"}
	if		$tmp45 128 129 	%argrw{"r"}
	sub		$tmp47 Value1 Value3 	%argrw{"wrr"}
	div		$tmp48 $tmp47 ___358_range 	%argrw{"wrr"}
	floor		$tmp46 $tmp48 	%argrw{"wr"}
	mul		$tmp49 ___358_range $tmp46 	%argrw{"wrr"}
	sub		Value Value1 $tmp49 	%argrw{"wrr"}
	assign		Value Value3 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:63
#   else if (math_type == "pingpong")
	eq		$tmp50 math_type $const35 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{63} %argrw{"wrr"}
	if		$tmp50 147 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:64
#     Value = pingpong(Value1, Value2);
	functioncall	$const35 147 	%line{64} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:46
#   return (b != 0.0) ? abs(fract((a - b) / (b * 2.0)) * b * 2.0 - b) : 0.0;
	neq		$tmp51 Value2 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{46} %argrw{"wrr"}
	if		$tmp51 145 146 	%argrw{"r"}
	sub		$tmp53 Value1 Value2 	%argrw{"wrr"}
	mul		$tmp54 Value2 $const36 	%argrw{"wrr"}
	div		$tmp55 $tmp53 $tmp54 	%argrw{"wrr"}
	functioncall	$const37 141 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:29
#   return a - floor(a);
	floor		$tmp56 $tmp55 	%line{29} %argrw{"wr"}
	sub		$tmp52 $tmp55 $tmp56 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:46
#   return (b != 0.0) ? abs(fract((a - b) / (b * 2.0)) * b * 2.0 - b) : 0.0;
	mul		$tmp57 $tmp52 Value2 	%line{46} %argrw{"wrr"}
	mul		$tmp58 $tmp57 $const36 	%argrw{"wrr"}
	sub		$tmp59 $tmp58 Value2 	%argrw{"wrr"}
	abs		Value $tmp59 	%argrw{"wr"}
	assign		Value $const6 	%argrw{"wr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:65
#   else if (math_type == "sine")
	eq		$tmp60 math_type $const38 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{65} %argrw{"wrr"}
	if		$tmp60 150 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:66
#     Value = sin(Value1);
	sin		Value Value1 	%line{66} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:67
#   else if (math_type == "cosine")
	eq		$tmp61 math_type $const39 	%line{67} %argrw{"wrr"}
	if		$tmp61 153 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:68
#     Value = cos(Value1);
	cos		Value Value1 	%line{68} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:69
#   else if (math_type == "tangent")
	eq		$tmp62 math_type $const40 	%line{69} %argrw{"wrr"}
	if		$tmp62 156 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:70
#     Value = tan(Value1);
	tan		Value Value1 	%line{70} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:71
#   else if (math_type == "sinh")
	eq		$tmp63 math_type $const41 	%line{71} %argrw{"wrr"}
	if		$tmp63 159 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:72
#     Value = sinh(Value1);
	sinh		Value Value1 	%line{72} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:73
#   else if (math_type == "cosh")
	eq		$tmp64 math_type $const42 	%line{73} %argrw{"wrr"}
	if		$tmp64 162 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:74
#     Value = cosh(Value1);
	cosh		Value Value1 	%line{74} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:75
#   else if (math_type == "tanh")
	eq		$tmp65 math_type $const43 	%line{75} %argrw{"wrr"}
	if		$tmp65 165 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:76
#     Value = tanh(Value1);
	tanh		Value Value1 	%line{76} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:77
#   else if (math_type == "arcsine")
	eq		$tmp66 math_type $const44 	%line{77} %argrw{"wrr"}
	if		$tmp66 168 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:78
#     Value = asin(Value1);
	asin		Value Value1 	%line{78} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:79
#   else if (math_type == "arccosine")
	eq		$tmp67 math_type $const45 	%line{79} %argrw{"wrr"}
	if		$tmp67 171 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:80
#     Value = acos(Value1);
	acos		Value Value1 	%line{80} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:81
#   else if (math_type == "arctangent")
	eq		$tmp68 math_type $const46 	%line{81} %argrw{"wrr"}
	if		$tmp68 174 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:82
#     Value = atan(Value1);
	atan		Value Value1 	%line{82} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:83
#   else if (math_type == "arctan2")
	eq		$tmp69 math_type $const47 	%line{83} %argrw{"wrr"}
	if		$tmp69 177 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:84
#     Value = atan2(Value1, Value2);
	atan2		Value Value1 Value2 	%line{84} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:85
#   else if (math_type == "sign")
	eq		$tmp70 math_type $const48 	%line{85} %argrw{"wrr"}
	if		$tmp70 180 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:86
#     Value = sign(Value1);
	sign		Value Value1 	%line{86} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:87
#   else if (math_type == "exponent")
	eq		$tmp71 math_type $const49 	%line{87} %argrw{"wrr"}
	if		$tmp71 183 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:88
#     Value = exp(Value1);
	exp		Value Value1 	%line{88} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:89
#   else if (math_type == "compare")
	eq		$tmp72 math_type $const50 	%line{89} %argrw{"wrr"}
	if		$tmp72 197 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:90
#     Value = ((Value1 == Value2) || (abs(Value1 - Value2) <= max(Value3, 1e-5))) ? 1.0 : 0.0;
	eq		$tmp73 Value1 Value2 	%line{90} %argrw{"wrr"}
	neq		$tmp74 $tmp73 $const10 	%argrw{"wrr"}
	if		$tmp74 188 194 	%argrw{"r"}
	sub		$tmp76 Value1 Value2 	%argrw{"wrr"}
	abs		$tmp75 $tmp76 	%argrw{"wr"}
	max		$tmp77 Value3 $const51 	%argrw{"wrr"}
	le		$tmp78 $tmp75 $tmp77 	%argrw{"wrr"}
	neq		$tmp79 $tmp78 $const10 	%argrw{"wrr"}
	assign		$tmp74 $tmp79 	%argrw{"wr"}
	if		$tmp74 196 197 	%argrw{"r"}
	assign		Value $const52 	%argrw{"wr"}
	assign		Value $const6 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:91
#   else if (math_type == "multiply_add")
	eq		$tmp80 math_type $const53 	%line{91} %argrw{"wrr"}
	if		$tmp80 201 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:92
#     Value = Value1 * Value2 + Value3;
	mul		$tmp81 Value1 Value2 	%line{92} %argrw{"wrr"}
	add		Value $tmp81 Value3 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:93
#   else if (math_type == "smoothmin")
	eq		$tmp82 math_type $const54 	%line{93} %argrw{"wrr"}
	if		$tmp82 220 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:94
#     Value = smoothmin(Value1, Value2, Value3);
	functioncall	$const54 220 	%line{94} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:35
#   if (c != 0.0) {
	neq		$tmp83 Value3 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{35} %argrw{"wrr"}
	if		$tmp83 218 220 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:36
#     float h = max(c - abs(a - b), 0.0) / c;
	sub		$tmp86 Value1 Value2 	%line{36} %argrw{"wrr"}
	abs		$tmp85 $tmp86 	%argrw{"wr"}
	sub		$tmp87 Value3 $tmp85 	%argrw{"wrr"}
	max		$tmp84 $tmp87 $const6 	%argrw{"wrr"}
	div		___351_h $tmp84 Value3 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:37
#     return min(a, b) - h * h * h * c * (1.0 / 6.0);
	min		$tmp88 Value1 Value2 	%line{37} %argrw{"wrr"}
	mul		$tmp89 ___351_h ___351_h 	%argrw{"wrr"}
	mul		$tmp90 $tmp89 ___351_h 	%argrw{"wrr"}
	mul		$tmp91 $tmp90 Value3 	%argrw{"wrr"}
	mul		$tmp92 $tmp91 $const55 	%argrw{"wrr"}
	sub		Value $tmp88 $tmp92 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:40
#     return min(a, b);
	min		Value Value1 Value2 	%line{40} %argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:95
#   else if (math_type == "smoothmax")
	eq		$tmp93 math_type $const56 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{95} %argrw{"wrr"}
	if		$tmp93 242 243 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:96
#     Value = -(smoothmin(-Value1, -Value2, Value3));
	neg		$tmp95 Value1 	%line{96} %argrw{"wr"}
	neg		$tmp96 Value2 	%argrw{"wr"}
	functioncall	$const54 241 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:35
#   if (c != 0.0) {
	neq		$tmp97 Value3 $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h"} %line{35} %argrw{"wrr"}
	if		$tmp97 239 241 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:36
#     float h = max(c - abs(a - b), 0.0) / c;
	sub		$tmp100 $tmp95 $tmp96 	%line{36} %argrw{"wrr"}
	abs		$tmp99 $tmp100 	%argrw{"wr"}
	sub		$tmp101 Value3 $tmp99 	%argrw{"wrr"}
	max		$tmp98 $tmp101 $const6 	%argrw{"wrr"}
	div		___351_h $tmp98 Value3 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:37
#     return min(a, b) - h * h * h * c * (1.0 / 6.0);
	min		$tmp102 $tmp95 $tmp96 	%line{37} %argrw{"wrr"}
	mul		$tmp103 ___351_h ___351_h 	%argrw{"wrr"}
	mul		$tmp104 $tmp103 ___351_h 	%argrw{"wrr"}
	mul		$tmp105 $tmp104 Value3 	%argrw{"wrr"}
	mul		$tmp106 $tmp105 $const55 	%argrw{"wrr"}
	sub		$tmp94 $tmp102 $tmp106 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.h:40
#     return min(a, b);
	min		$tmp94 $tmp95 $tmp96 	%line{40} %argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:96
#     Value = -(smoothmin(-Value1, -Value2, Value3));
	neg		Value $tmp94 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl"} %line{96} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_math.osl:98
#     warning("%s", "Unknown math operator!");
	warning		$const57 $const58 	%line{98} %argrw{"rr"}
	end
