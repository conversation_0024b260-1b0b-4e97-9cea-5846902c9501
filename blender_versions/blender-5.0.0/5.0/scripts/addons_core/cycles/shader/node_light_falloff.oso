OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_light_falloff.oso
shader node_light_falloff
param	float	Strength	0		%read{1,1} %write{2147483647,-1}
param	float	Smooth	0		%read{8,11} %write{2147483647,-1}
oparam	float	Quadratic	0		%read{2147483647,-1} %write{5,14}
oparam	float	Linear	0		%read{2147483647,-1} %write{6,15}
oparam	float	Constant	0		%read{2147483647,-1} %write{7,17}
local	float	ray_length	%read{3,17} %write{0,2}
local	float	strength	%read{5,16} %write{1,13}
local	float	___370_squared	%read{11,12} %write{10,10}
const	float	$const1	0		%read{0,8} %write{2147483647,-1}
temp	int	$tmp1	%read{2147483647,-1} %write{2,2}
const	string	$const2	"path:ray_length"		%read{2,2} %write{2147483647,-1}
const	float	$const3	3.40282347e+38		%read{3,3} %write{2147483647,-1}
temp	int	$tmp2	%read{4,4} %write{3,3}
temp	int	$tmp3	%read{9,9} %write{8,8}
temp	float	$tmp4	%read{12,12} %write{11,11}
temp	float	$tmp5	%read{13,13} %write{12,12}
temp	float	$tmp6	%read{17,17} %write{16,16}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:13
#   float ray_length = 0.0;
	assign		ray_length $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl"} %line{13} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:14
#   float strength = Strength;
	assign		strength Strength 	%line{14} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:15
#   getattribute("path:ray_length", ray_length);
	getattribute	$tmp1 $const2 ray_length 	%line{15} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:17
#   if (ray_length == FLT_MAX) {
	eq		$tmp2 ray_length $const3 	%line{17} %argrw{"wrr"}
	if		$tmp2 8 18 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:20
#     Quadratic = strength;
	assign		Quadratic strength 	%line{20} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:21
#     Linear = strength;
	assign		Linear strength 	%line{21} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:22
#     Constant = strength;
	assign		Constant strength 	%line{22} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:25
#     if (Smooth > 0.0) {
	gt		$tmp3 Smooth $const1 	%line{25} %argrw{"wrr"}
	if		$tmp3 14 14 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:26
#       float squared = ray_length * ray_length;
	mul		___370_squared ray_length ray_length 	%line{26} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:27
#       strength *= squared / (Smooth + squared);
	add		$tmp4 Smooth ___370_squared 	%line{27} %argrw{"wrr"}
	div		$tmp5 ___370_squared $tmp4 	%argrw{"wrr"}
	mul		strength strength $tmp5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:30
#     Quadratic = strength;
	assign		Quadratic strength 	%line{30} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:31
#     Linear = (strength * ray_length);
	mul		Linear strength ray_length 	%line{31} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_light_falloff.osl:32
#     Constant = (strength * ray_length * ray_length);
	mul		$tmp6 strength ray_length 	%line{32} %argrw{"wrr"}
	mul		Constant $tmp6 ray_length 	%argrw{"wrr"}
	end
