OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_toon_bsdf.oso
shader node_toon_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{4,8} %write{2147483647,-1}
param	string	component	"diffuse"		%read{1,5} %write{2147483647,-1}
param	float	Size	0.5		%read{3,7} %write{2147483647,-1}
param	float	Smooth	0		%read{3,7} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{3,7} %write{0,0} %initexpr
oparam	closure color	BSDF			%read{2147483647,-1} %write{4,8}
global	normal	N	%read{0,0} %write{2147483647,-1}
const	string	$const1	"diffuse"		%read{1,1} %write{2147483647,-1}
temp	int	$tmp1	%read{2,2} %write{1,1}
temp	closure color	$tmp2	%read{4,4} %write{3,3}
const	string	$const2	"diffuse_toon"		%read{3,3} %write{2147483647,-1}
const	string	$const3	"glossy"		%read{5,5} %write{2147483647,-1}
temp	int	$tmp3	%read{6,6} %write{5,5}
temp	closure color	$tmp4	%read{8,8} %write{7,7}
const	string	$const4	"glossy_toon"		%read{7,7} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl:11
#                       normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl"} %line{11} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl:14
#   if (component == "diffuse")
	eq		$tmp1 component $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl"} %line{14} %argrw{"wrr"}
	if		$tmp1 5 9 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl:15
#     BSDF = Color * diffuse_toon(Normal, Size, Smooth);
	closure		$tmp2 $const2 Normal Size Smooth 	%line{15} %argrw{"wrrrr"}
	mul		BSDF $tmp2 Color 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl:16
#   else if (component == "glossy")
	eq		$tmp3 component $const3 	%line{16} %argrw{"wrr"}
	if		$tmp3 9 9 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_toon_bsdf.osl:17
#     BSDF = Color * glossy_toon(Normal, Size, Smooth);
	closure		$tmp4 $const4 Normal Size Smooth 	%line{17} %argrw{"wrrrr"}
	mul		BSDF $tmp4 Color 	%argrw{"wrr"}
	end
