OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_attribute.oso
shader node_attribute
param	string	bump_offset	"center"		%read{9,23} %write{2147483647,-1}
param	float	bump_filter_width	0.100000001		%read{12,35} %write{2147483647,-1} %derivs
param	string	name	""		%read{1,7} %write{2147483647,-1} %derivs
oparam	point	Vector	0 0 0		%read{14,30} %write{6,30} %derivs
oparam	color	Color	0 0 0		%read{6,27} %write{5,27} %derivs
oparam	float	Fac	0		%read{17,33} %write{7,33} %derivs
oparam	float	Alpha	0		%read{20,36} %write{8,36} %derivs
local	float[4]	data	%read{2,8} %write{0,1} %derivs
const	float[4]	$const1	0 0 0 0 		%read{0,0} %write{2147483647,-1}
temp	int	$tmp1	%read{2147483647,-1} %write{1,1}
const	int	$const2	0		%read{2,2} %write{2147483647,-1}
temp	float	$tmp2	%read{5,5} %write{2,2} %derivs
const	int	$const3	1		%read{3,3} %write{2147483647,-1}
temp	float	$tmp3	%read{5,5} %write{3,3} %derivs
const	int	$const4	2		%read{4,4} %write{2147483647,-1}
temp	float	$tmp4	%read{5,5} %write{4,4} %derivs
temp	int	$tmp5	%read{2147483647,-1} %write{7,7}
const	int	$const5	3		%read{8,8} %write{2147483647,-1}
const	string	$const6	"dx"		%read{9,9} %write{2147483647,-1}
temp	int	$tmp6	%read{10,10} %write{9,9}
temp	color	$tmp7	%read{12,12} %write{11,11} %derivs
temp	color	$tmp8	%read{13,13} %write{12,12} %derivs
temp	vector	$tmp9	%read{15,15} %write{14,14} %derivs
temp	vector	$tmp10	%read{16,16} %write{15,15} %derivs
temp	float	$tmp11	%read{18,18} %write{17,17} %derivs
temp	float	$tmp12	%read{19,19} %write{18,18} %derivs
temp	float	$tmp13	%read{21,21} %write{20,20} %derivs
temp	float	$tmp14	%read{22,22} %write{21,21} %derivs
const	string	$const7	"dy"		%read{23,23} %write{2147483647,-1}
temp	int	$tmp15	%read{24,24} %write{23,23}
temp	color	$tmp16	%read{26,26} %write{25,25} %derivs
temp	color	$tmp17	%read{27,27} %write{26,26} %derivs
temp	vector	$tmp18	%read{29,29} %write{28,28} %derivs
temp	vector	$tmp19	%read{30,30} %write{29,29} %derivs
temp	float	$tmp20	%read{32,32} %write{31,31} %derivs
temp	float	$tmp21	%read{33,33} %write{32,32} %derivs
temp	float	$tmp22	%read{35,35} %write{34,34} %derivs
temp	float	$tmp23	%read{36,36} %write{35,35} %derivs
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:15
#   float data[4] = {0.0, 0.0, 0.0, 0.0};
	assign		data $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl"} %line{15} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:16
#   getattribute(name, data);
	getattribute	$tmp1 name data 	%line{16} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:17
#   Color = color(data[0], data[1], data[2]);
	aref		$tmp2 data $const2 	%line{17} %argrw{"wrr"}
	aref		$tmp3 data $const3 	%argrw{"wrr"}
	aref		$tmp4 data $const4 	%argrw{"wrr"}
	color		Color $tmp2 $tmp3 $tmp4 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:18
#   Vector = point(Color);
	assign		Vector Color 	%line{18} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:19
#   getattribute(name, Fac);
	getattribute	$tmp5 name Fac 	%line{19} %argrw{"wrw"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:20
#   Alpha = data[3];
	aref		Alpha data $const5 	%line{20} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:22
#   if (bump_offset == "dx") {
	eq		$tmp6 bump_offset $const6 	%line{22} %argrw{"wrr"}
	if		$tmp6 23 37 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:23
#     Color += Dx(Color) * bump_filter_width;
	Dx		$tmp7 Color 	%line{23} %argrw{"wr"} %argderivs{1}
	mul		$tmp8 $tmp7 bump_filter_width 	%argrw{"wrr"}
	add		Color Color $tmp8 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:24
#     Vector += Dx(Vector) * bump_filter_width;
	Dx		$tmp9 Vector 	%line{24} %argrw{"wr"} %argderivs{1}
	mul		$tmp10 $tmp9 bump_filter_width 	%argrw{"wrr"}
	add		Vector Vector $tmp10 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:25
#     Fac += Dx(Fac) * bump_filter_width;
	Dx		$tmp11 Fac 	%line{25} %argrw{"wr"} %argderivs{1}
	mul		$tmp12 $tmp11 bump_filter_width 	%argrw{"wrr"}
	add		Fac Fac $tmp12 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:26
#     Alpha += Dx(Alpha) * bump_filter_width;
	Dx		$tmp13 Alpha 	%line{26} %argrw{"wr"} %argderivs{1}
	mul		$tmp14 $tmp13 bump_filter_width 	%argrw{"wrr"}
	add		Alpha Alpha $tmp14 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:28
#   else if (bump_offset == "dy") {
	eq		$tmp15 bump_offset $const7 	%line{28} %argrw{"wrr"}
	if		$tmp15 37 37 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:29
#     Color += Dy(Color) * bump_filter_width;
	Dy		$tmp16 Color 	%line{29} %argrw{"wr"} %argderivs{1}
	mul		$tmp17 $tmp16 bump_filter_width 	%argrw{"wrr"}
	add		Color Color $tmp17 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:30
#     Vector += Dy(Vector) * bump_filter_width;
	Dy		$tmp18 Vector 	%line{30} %argrw{"wr"} %argderivs{1}
	mul		$tmp19 $tmp18 bump_filter_width 	%argrw{"wrr"}
	add		Vector Vector $tmp19 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:31
#     Fac += Dy(Fac) * bump_filter_width;
	Dy		$tmp20 Fac 	%line{31} %argrw{"wr"} %argderivs{1}
	mul		$tmp21 $tmp20 bump_filter_width 	%argrw{"wrr"}
	add		Fac Fac $tmp21 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_attribute.osl:32
#     Alpha += Dy(Alpha) * bump_filter_width;
	Dy		$tmp22 Alpha 	%line{32} %argrw{"wr"} %argderivs{1}
	mul		$tmp23 $tmp22 bump_filter_width 	%argrw{"wrr"}
	add		Alpha Alpha $tmp23 	%argrw{"wrr"}
	end
