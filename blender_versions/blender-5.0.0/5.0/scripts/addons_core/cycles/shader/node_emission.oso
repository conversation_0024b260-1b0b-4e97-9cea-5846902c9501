OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_emission.oso
shader node_emission
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	float	Strength	1		%read{1,1} %write{2147483647,-1}
oparam	closure color	Emission			%read{2147483647,-1} %write{2,2}
temp	closure color	$tmp1	%read{2,2} %write{0,0}
const	string	$const1	"emission"		%read{0,0} %write{2147483647,-1}
temp	color	$tmp2	%read{2,2} %write{1,1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_emission.osl:9
#   Emission = (Strength * Color) * emission();
	closure		$tmp1 $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_emission.osl"} %line{9} %argrw{"wr"}
	mul		$tmp2 Strength Color 	%argrw{"wrr"}
	mul		Emission $tmp1 $tmp2 	%argrw{"wrr"}
	end
