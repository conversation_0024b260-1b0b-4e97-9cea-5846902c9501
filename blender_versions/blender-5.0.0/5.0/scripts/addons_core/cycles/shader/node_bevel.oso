OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_bevel.oso
shader node_bevel
param	int	samples	4		%read{2,2} %write{2147483647,-1} %derivs
param	float	Radius	0.0500000007		%read{3,3} %write{2147483647,-1} %derivs
param	normal	NormalIn	0 0 0		%read{5,5} %write{0,0} %initexpr
oparam	normal	NormalOut	0 0 0		%read{2147483647,-1} %write{1,6} %initexpr
global	normal	N	%read{0,4} %write{2147483647,-1}
local	vector	bevel_N	%read{4,4} %write{3,3}
const	string	$const1	"@bevel"		%read{3,3} %write{2147483647,-1}
temp	float	$tmp1	%read{3,3} %write{2,2} %derivs
temp	vector	$tmp2	%read{5,5} %write{4,4}
temp	normal	$tmp3	%read{6,6} %write{5,5}
code NormalIn
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl:9
#                   normal NormalIn = N,
	assign		NormalIn N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl"} %line{9} %argrw{"wr"}
code NormalOut
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl:10
#                   output normal NormalOut = N)
	assign		NormalOut N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl"} %line{10} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl:13
#   vector bevel_N = (normal)(color)texture("@bevel", samples, Radius);
	assign		$tmp1 samples 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl"} %line{13} %argrw{"wr"}
	texture		bevel_N $const1 $tmp1 Radius 	%argrw{"wrrr"} %argderivs{2,3}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bevel.osl:16
#   NormalOut = normalize(NormalIn + (bevel_N - N));
	sub		$tmp2 bevel_N N 	%line{16} %argrw{"wrr"}
	add		$tmp3 NormalIn $tmp2 	%argrw{"wrr"}
	normalize	NormalOut $tmp3 	%argrw{"wr"}
	end
