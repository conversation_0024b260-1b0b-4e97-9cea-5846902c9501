OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_invert.oso
shader node_invert
param	float	Fac	1		%read{1,1} %write{2147483647,-1}
param	color	ColorIn	0.800000012 0.800000012 0.800000012		%read{0,1} %write{2147483647,-1}
oparam	color	ColorOut	0.800000012 0.800000012 0.800000012		%read{2147483647,-1} %write{1,1}
local	color	ColorInv	%read{1,1} %write{0,0}
const	color	$const1	1 1 1		%read{0,0} %write{2147483647,-1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_invert.osl:9
#   color ColorInv = color(1.0) - ColorIn;
	sub		ColorInv $const1 ColorIn 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_invert.osl"} %line{9} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_invert.osl:10
#   ColorOut = mix(ColorIn, ColorInv, Fac);
	mix		ColorOut ColorIn ColorInv Fac 	%line{10} %argrw{"wrrr"}
	end
