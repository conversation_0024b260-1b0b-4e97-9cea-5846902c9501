OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_set_normal.oso
surface node_set_normal
param	normal	Direction	0 0 0		%read{2,3} %write{0,0} %initexpr
oparam	normal	Normal	0 0 0		%read{2147483647,-1} %write{1,3} %initexpr
global	normal	N	%read{0,1} %write{2,2}
code Direction
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl:7
# surface node_set_normal(normal Direction = N, output normal Normal = N)
	assign		Direction N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl"} %line{7} %argrw{"wr"}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl:7
# surface node_set_normal(normal Direction = N, output normal Normal = N)
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl"} %line{7} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl:9
#   N = Direction;
	assign		N Direction 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl"} %line{9} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_set_normal.osl:10
#   Normal = Direction;
	assign		Normal Direction 	%line{10} %argrw{"wr"}
	end
