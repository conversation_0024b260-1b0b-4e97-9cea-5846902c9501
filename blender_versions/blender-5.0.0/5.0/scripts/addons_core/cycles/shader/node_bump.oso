OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_bump.oso
surface node_bump
param	int	invert	0		%read{23,23} %write{2147483647,-1}
param	int	use_object_space	0		%read{4,37} %write{2147483647,-1}
param	normal	NormalIn	0 0 0		%read{3,3} %write{0,0} %initexpr
param	float	Strength	0.100000001		%read{21,21} %write{2147483647,-1}
param	float	Distance	1		%read{22,22} %write{2147483647,-1}
param	float	FilterWidth	0.100000001		%read{25,25} %write{2147483647,-1}
param	float	SampleCenter	0		%read{15,17} %write{2147483647,-1}
param	float	SampleX	0		%read{15,15} %write{2147483647,-1}
param	float	SampleY	0		%read{17,17} %write{2147483647,-1}
oparam	normal	NormalOut	0 0 0		%read{32,38} %write{1,39} %initexpr
global	point	P	%read{2,2} %write{2147483647,-1} %derivs
global	normal	N	%read{0,1} %write{2147483647,-1}
local	point	Ptmp	%read{6,11} %write{2,6} %derivs
local	normal	Normal	%read{8,34} %write{3,9}
local	vector	dPdx	%read{13,14} %write{10,10}
local	vector	dPdy	%read{12,12} %write{11,11}
local	vector	Rx	%read{14,16} %write{12,12}
local	vector	Ry	%read{18,18} %write{13,13}
local	float	det	%read{20,27} %write{14,14}
local	vector	surfgrad	%read{29,29} %write{19,19}
local	float	absdet	%read{25,25} %write{20,20}
local	float	strength	%read{32,33} %write{21,21}
local	float	dist	%read{24,28} %write{22,24}
const	string	$const1	"object"		%read{6,38} %write{2147483647,-1}
const	string	$const2	"transform"		%read{5,7} %write{2147483647,-1}
const	string	$const3	"common"		%read{6,8} %write{2147483647,-1}
temp	normal	$tmp1	%read{9,9} %write{8,8}
temp	float	$tmp2	%read{16,16} %write{15,15}
temp	vector	$tmp3	%read{19,19} %write{16,16}
temp	float	$tmp4	%read{18,18} %write{17,17}
temp	vector	$tmp5	%read{19,19} %write{18,18}
const	float	$const4	0		%read{21,21} %write{2147483647,-1}
const	float	$const5	-1		%read{24,24} %write{2147483647,-1}
temp	float	$tmp6	%read{26,26} %write{25,25}
temp	normal	$tmp7	%read{30,30} %write{26,26}
temp	float	$tmp8	%read{28,28} %write{27,27}
temp	float	$tmp9	%read{29,29} %write{28,28}
temp	vector	$tmp10	%read{30,30} %write{29,29}
temp	normal	$tmp11	%read{31,31} %write{30,30}
temp	normal	$tmp12	%read{35,35} %write{32,32}
const	float	$const6	1		%read{33,33} %write{2147483647,-1}
temp	float	$tmp13	%read{34,34} %write{33,33}
temp	normal	$tmp14	%read{35,35} %write{34,34}
temp	normal	$tmp15	%read{36,36} %write{35,35}
temp	normal	$tmp16	%read{39,39} %write{38,38}
const	string	$const7	"world"		%read{38,38} %write{2147483647,-1}
code NormalIn
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:12
#                   normal NormalIn = N,
	assign		NormalIn N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl"} %line{12} %argrw{"wr"}
code NormalOut
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:19
#                   output normal NormalOut = N)
	assign		NormalOut N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl"} %line{19} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:21
#   point Ptmp = P;
	assign		Ptmp P 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl"} %line{21} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:22
#   normal Normal = NormalIn;
	assign		Normal NormalIn 	%line{22} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:24
#   if (use_object_space) {
	if		use_object_space 10 10 	%line{24} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:25
#     Ptmp = transform("object", Ptmp);
	functioncall	$const2 7 	%line{25} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:263
# point  transform (string to, point p)  { return transform("common",to,p); }
	transform	Ptmp $const3 $const1 Ptmp 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{263} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:26
#     Normal = normalize(transform("object", Normal));
	functioncall	$const2 9 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl"} %line{26} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:261
# normal transform (string to, normal p) { return transform("common",to,p); }
	transformn	$tmp1 $const3 $const1 Normal 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{261} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:26
#     Normal = normalize(transform("object", Normal));
	normalize	Normal $tmp1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl"} %line{26} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:30
#   vector dPdx = Dx(Ptmp);
	Dx		dPdx Ptmp 	%line{30} %argrw{"wr"} %argderivs{1}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:31
#   vector dPdy = Dy(Ptmp);
	Dy		dPdy Ptmp 	%line{31} %argrw{"wr"} %argderivs{1}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:33
#   vector Rx = cross(dPdy, Normal);
	cross		Rx dPdy Normal 	%line{33} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:34
#   vector Ry = cross(Normal, dPdx);
	cross		Ry Normal dPdx 	%line{34} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:37
#   float det = dot(dPdx, Rx);
	dot		det dPdx Rx 	%line{37} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:38
#   vector surfgrad = (SampleX - SampleCenter) * Rx + (SampleY - SampleCenter) * Ry;
	sub		$tmp2 SampleX SampleCenter 	%line{38} %argrw{"wrr"}
	mul		$tmp3 $tmp2 Rx 	%argrw{"wrr"}
	sub		$tmp4 SampleY SampleCenter 	%argrw{"wrr"}
	mul		$tmp5 $tmp4 Ry 	%argrw{"wrr"}
	add		surfgrad $tmp3 $tmp5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:40
#   float absdet = fabs(det);
	fabs		absdet det 	%line{40} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:42
#   float strength = max(Strength, 0.0);
	max		strength Strength $const4 	%line{42} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:43
#   float dist = Distance;
	assign		dist Distance 	%line{43} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:45
#   if (invert)
	if		invert 25 25 	%line{45} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:46
#     dist *= -1.0;
	mul		dist dist $const5 	%line{46} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:49
#   NormalOut = normalize(FilterWidth * absdet * Normal - dist * sign(det) * surfgrad);
	mul		$tmp6 FilterWidth absdet 	%line{49} %argrw{"wrr"}
	mul		$tmp7 $tmp6 Normal 	%argrw{"wrr"}
	sign		$tmp8 det 	%argrw{"wr"}
	mul		$tmp9 dist $tmp8 	%argrw{"wrr"}
	mul		$tmp10 $tmp9 surfgrad 	%argrw{"wrr"}
	sub		$tmp11 $tmp7 $tmp10 	%argrw{"wrr"}
	normalize	NormalOut $tmp11 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:50
#   NormalOut = normalize(strength * NormalOut + (1.0 - strength) * Normal);
	mul		$tmp12 strength NormalOut 	%line{50} %argrw{"wrr"}
	sub		$tmp13 $const6 strength 	%argrw{"wrr"}
	mul		$tmp14 $tmp13 Normal 	%argrw{"wrr"}
	add		$tmp15 $tmp12 $tmp14 	%argrw{"wrr"}
	normalize	NormalOut $tmp15 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:52
#   if (use_object_space) {
	if		use_object_space 40 40 	%line{52} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_bump.osl:53
#     NormalOut = normalize(transform("object", "world", NormalOut));
	transformn	$tmp16 $const1 $const7 NormalOut 	%line{53} %argrw{"wrrr"}
	normalize	NormalOut $tmp16 	%argrw{"wr"}
	end
