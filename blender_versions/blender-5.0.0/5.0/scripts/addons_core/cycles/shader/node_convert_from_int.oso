OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_convert_from_int.oso
shader node_convert_from_int
param	int	value_int	0		%read{0,0} %write{2147483647,-1}
oparam	string	value_string	""		%read{2147483647,-1} %write{2147483647,-1}
oparam	float	value_float	0		%read{2147483647,-1} %write{1,1}
oparam	color	value_color	0 0 0		%read{2147483647,-1} %write{2,2}
oparam	vector	value_vector	0 0 0		%read{2147483647,-1} %write{3,3}
oparam	point	value_point	0 0 0		%read{2147483647,-1} %write{4,4}
oparam	normal	value_normal	0 0 0		%read{2147483647,-1} %write{5,5}
local	float	f	%read{1,5} %write{0,0}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl:15
#   float f = (float)value_int;
	assign		f value_int 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl"} %line{15} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl:16
#   value_float = f;
	assign		value_float f 	%line{16} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl:17
#   value_color = color(f, f, f);
	color		value_color f f f 	%line{17} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl:18
#   value_vector = vector(f, f, f);
	vector		value_vector f f f 	%line{18} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl:19
#   value_point = point(f, f, f);
	point		value_point f f f 	%line{19} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_convert_from_int.osl:20
#   value_normal = normal(f, f, f);
	normal		value_normal f f f 	%line{20} %argrw{"wrrr"}
	end
