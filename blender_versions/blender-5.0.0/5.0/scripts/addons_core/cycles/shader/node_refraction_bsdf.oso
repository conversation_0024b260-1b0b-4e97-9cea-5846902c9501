OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.oso
shader node_refraction_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{9,9} %write{2147483647,-1}
param	string	distribution	"ggx"		%read{8,8} %write{2147483647,-1}
param	float	Roughness	0.200000003		%read{6,6} %write{2147483647,-1}
param	float	IOR	1.45000005		%read{1,1} %write{2147483647,-1}
param	normal	Normal	0 0 0		%read{8,8} %write{0,0} %initexpr
oparam	closure color	BSDF			%read{2147483647,-1} %write{9,9}
global	normal	N	%read{0,0} %write{2147483647,-1}
local	float	f	%read{4,5} %write{1,1}
local	float	eta	%read{8,8} %write{4,5}
local	float	roughness	%read{8,8} %write{6,6}
const	float	$const1	9.99999975e-06		%read{1,1} %write{2147483647,-1}
temp	int	$tmp1	%read{3,3} %write{2,2}
const	float	$const2	1		%read{4,4} %write{2147483647,-1}
temp	closure color	$tmp2	%read{9,9} %write{8,8}
const	int	$const3	1		%read{8,8} %write{2147483647,-1}
const	string	$const4	"microfacet"		%read{7,8} %write{2147483647,-1}
const	vector	$const5	0 0 0		%read{8,8} %write{2147483647,-1}
code Normal
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl:11
#                             normal Normal = N,
	assign		Normal N 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl"} %line{11} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl:14
#   float f = max(IOR, 1e-5);
	max		f IOR $const1 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl"} %line{14} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl:15
#   float eta = backfacing() ? 1.0 / f : f;
	backfacing	$tmp1 	%line{15} %argrw{"w"}
	if		$tmp1 5 6 	%argrw{"r"}
	div		eta $const2 f 	%argrw{"wrr"}
	assign		eta f 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl:16
#   float roughness = Roughness * Roughness;
	mul		roughness Roughness Roughness 	%line{16} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl:18
#   BSDF = Color * microfacet(distribution, Normal, roughness, eta, 1);
	functioncall	$const4 9 	%line{18} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:463
#     return microfacet(distribution, N, vector(0), alpha, alpha, eta, refr);
	closure		$tmp2 $const4 distribution Normal $const5 roughness roughness eta $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{463} %argrw{"wrrrrrrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl:18
#   BSDF = Color * microfacet(distribution, Normal, roughness, eta, 1);
	mul		BSDF $tmp2 Color 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_refraction_bsdf.osl"} %line{18} %argrw{"wrr"}
	end
