OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_magic_texture.oso
shader node_magic_texture
param	int	use_mapping	0		%read{2,2} %write{2147483647,-1}
param	matrix	mapping	0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0		%read{3,3} %write{2147483647,-1}
param	int	depth	2		%read{42,109} %write{2147483647,-1}
param	float	Distortion	5		%read{5,5} %write{2147483647,-1}
param	float	Scale	5		%read{7,21} %write{2147483647,-1}
param	point	Vector	0 0 0		%read{1,1} %write{0,0} %initexpr
oparam	float	Fac	0		%read{2147483647,-1} %write{131,131}
oparam	color	Color	0 0 0		%read{126,129} %write{125,125}
global	point	P	%read{0,0} %write{2147483647,-1}
local	float	___368_dist	%read{44,121} %write{5,118}
local	float	___368_a	%read{27,36} %write{12,12}
local	float	___368_b	%read{27,37} %write{19,19}
local	float	___368_c	%read{28,38} %write{26,26}
local	float	___368_x	%read{44,122} %write{30,119}
local	float	___368_y	%read{45,123} %write{35,120}
local	float	___368_z	%read{46,124} %write{41,121}
local	point	p	%read{3,20} %write{1,3}
const	string	$const1	"magic"		%read{4,4} %write{2147483647,-1}
const	int	$const2	0		%read{6,126} %write{2147483647,-1}
temp	float	$tmp1	%read{7,7} %write{6,6}
temp	float	$tmp2	%read{9,12} %write{7,7}
const	float	$const3	6.28318548		%read{9,25} %write{2147483647,-1}
const	string	$const4	"mod"		%read{8,22} %write{2147483647,-1}
temp	float	$tmp3	%read{11,11} %write{10,10}
temp	float	$tmp4	%read{10,10} %write{9,9}
temp	float	$tmp5	%read{12,12} %write{11,11}
const	int	$const5	1		%read{13,127} %write{2147483647,-1}
temp	float	$tmp6	%read{14,14} %write{13,13}
temp	float	$tmp7	%read{16,19} %write{14,14}
temp	float	$tmp8	%read{18,18} %write{17,17}
temp	float	$tmp9	%read{17,17} %write{16,16}
temp	float	$tmp10	%read{19,19} %write{18,18}
const	int	$const6	2		%read{20,129} %write{2147483647,-1}
temp	float	$tmp11	%read{21,21} %write{20,20}
temp	float	$tmp12	%read{23,26} %write{21,21}
temp	float	$tmp13	%read{25,25} %write{24,24}
temp	float	$tmp14	%read{24,24} %write{23,23}
temp	float	$tmp15	%read{26,26} %write{25,25}
temp	float	$tmp16	%read{28,28} %write{27,27}
temp	float	$tmp17	%read{29,29} %write{28,28}
const	float	$const7	5		%read{29,39} %write{2147483647,-1}
temp	float	$tmp18	%read{30,30} %write{29,29}
temp	float	$tmp19	%read{32,32} %write{31,31}
temp	float	$tmp20	%read{33,33} %write{32,32}
temp	float	$tmp21	%read{34,34} %write{33,33}
temp	float	$tmp22	%read{35,35} %write{34,34}
temp	float	$tmp23	%read{41,41} %write{40,40}
temp	float	$tmp24	%read{37,37} %write{36,36}
temp	float	$tmp25	%read{38,38} %write{37,37}
temp	float	$tmp26	%read{39,39} %write{38,38}
temp	float	$tmp27	%read{40,40} %write{39,39}
temp	int	$tmp28	%read{43,43} %write{42,42}
temp	float	$tmp29	%read{50,50} %write{49,49}
temp	float	$tmp30	%read{48,48} %write{47,47}
temp	float	$tmp31	%read{49,49} %write{48,48}
temp	int	$tmp32	%read{53,53} %write{52,52}
temp	float	$tmp33	%read{55,55} %write{54,54}
temp	float	$tmp34	%read{56,56} %write{55,55}
temp	int	$tmp35	%read{59,59} %write{58,58}
temp	float	$tmp36	%read{61,61} %write{60,60}
temp	float	$tmp37	%read{62,62} %write{61,61}
temp	float	$tmp38	%read{63,63} %write{62,62}
const	int	$const8	3		%read{65,65} %write{2147483647,-1}
temp	int	$tmp39	%read{66,66} %write{65,65}
temp	float	$tmp40	%read{71,71} %write{70,70}
temp	float	$tmp41	%read{68,68} %write{67,67}
temp	float	$tmp42	%read{69,69} %write{68,68}
temp	float	$tmp43	%read{70,70} %write{69,69}
const	int	$const9	4		%read{73,73} %write{2147483647,-1}
temp	int	$tmp44	%read{74,74} %write{73,73}
temp	float	$tmp45	%read{79,79} %write{78,78}
temp	float	$tmp46	%read{76,76} %write{75,75}
temp	float	$tmp47	%read{77,77} %write{76,76}
temp	float	$tmp48	%read{78,78} %write{77,77}
const	int	$const10	5		%read{81,81} %write{2147483647,-1}
temp	int	$tmp49	%read{82,82} %write{81,81}
temp	float	$tmp50	%read{87,87} %write{86,86}
temp	float	$tmp51	%read{84,84} %write{83,83}
temp	float	$tmp52	%read{85,85} %write{84,84}
temp	float	$tmp53	%read{86,86} %write{85,85}
const	int	$const11	6		%read{89,89} %write{2147483647,-1}
temp	int	$tmp54	%read{90,90} %write{89,89}
temp	float	$tmp55	%read{92,92} %write{91,91}
temp	float	$tmp56	%read{93,93} %write{92,92}
const	int	$const12	7		%read{95,95} %write{2147483647,-1}
temp	int	$tmp57	%read{96,96} %write{95,95}
temp	float	$tmp58	%read{98,98} %write{97,97}
temp	float	$tmp59	%read{99,99} %write{98,98}
const	int	$const13	8		%read{101,101} %write{2147483647,-1}
temp	int	$tmp60	%read{102,102} %write{101,101}
temp	float	$tmp61	%read{107,107} %write{106,106}
temp	float	$tmp62	%read{104,104} %write{103,103}
temp	float	$tmp63	%read{105,105} %write{104,104}
temp	float	$tmp64	%read{106,106} %write{105,105}
const	int	$const14	9		%read{109,109} %write{2147483647,-1}
temp	int	$tmp65	%read{110,110} %write{109,109}
temp	float	$tmp66	%read{114,114} %write{113,113}
temp	float	$tmp67	%read{112,112} %write{111,111}
temp	float	$tmp68	%read{113,113} %write{112,112}
const	float	$const15	0		%read{116,116} %write{2147483647,-1}
temp	int	$tmp69	%read{117,117} %write{116,116}
const	float	$const16	2		%read{118,118} %write{2147483647,-1}
const	float	$const17	0.5		%read{122,124} %write{2147483647,-1}
temp	float	$tmp70	%read{125,125} %write{122,122}
temp	float	$tmp71	%read{125,125} %write{123,123}
temp	float	$tmp72	%read{125,125} %write{124,124}
temp	float	$tmp73	%read{128,128} %write{126,126}
temp	float	$tmp74	%read{128,128} %write{127,127}
temp	float	$tmp75	%read{130,130} %write{128,128}
temp	float	$tmp76	%read{130,130} %write{129,129}
temp	float	$tmp77	%read{131,131} %write{130,130}
const	float	$const18	0.333333343		%read{131,131} %write{2147483647,-1}
code Vector
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:88
#                           point Vector = P,
	assign		Vector P 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl"} %line{88} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:92
#   point p = Vector;
	assign		p Vector 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl"} %line{92} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:94
#   if (use_mapping)
	if		use_mapping 4 4 	%line{94} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:95
#     p = transform(mapping, p);
	transform	p mapping p 	%line{95} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:97
#   Color = magic(p, Scale, depth, Distortion);
	functioncall	$const1 126 	%line{97} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:10
#   float dist = distortion;
	assign		___368_dist Distortion 	%line{10} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:12
#   float a = mod(p.x * scale, M_2PI);
	compref		$tmp1 p $const2 	%line{12} %argrw{"wrr"}
	mul		$tmp2 $tmp1 Scale 	%argrw{"wrr"}
	functioncall	$const4 13 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:132
# float  mod (float  a, float  b) { return a - b*floor(a/b); }
	div		$tmp4 $tmp2 $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{132} %argrw{"wrr"}
	floor		$tmp3 $tmp4 	%argrw{"wr"}
	mul		$tmp5 $const3 $tmp3 	%argrw{"wrr"}
	sub		___368_a $tmp2 $tmp5 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:13
#   float b = mod(p.y * scale, M_2PI);
	compref		$tmp6 p $const5 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl"} %line{13} %argrw{"wrr"}
	mul		$tmp7 $tmp6 Scale 	%argrw{"wrr"}
	functioncall	$const4 20 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:132
# float  mod (float  a, float  b) { return a - b*floor(a/b); }
	div		$tmp9 $tmp7 $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{132} %argrw{"wrr"}
	floor		$tmp8 $tmp9 	%argrw{"wr"}
	mul		$tmp10 $const3 $tmp8 	%argrw{"wrr"}
	sub		___368_b $tmp7 $tmp10 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:14
#   float c = mod(p.z * scale, M_2PI);
	compref		$tmp11 p $const6 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl"} %line{14} %argrw{"wrr"}
	mul		$tmp12 $tmp11 Scale 	%argrw{"wrr"}
	functioncall	$const4 27 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:132
# float  mod (float  a, float  b) { return a - b*floor(a/b); }
	div		$tmp14 $tmp12 $const3 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{132} %argrw{"wrr"}
	floor		$tmp13 $tmp14 	%argrw{"wr"}
	mul		$tmp15 $const3 $tmp13 	%argrw{"wrr"}
	sub		___368_c $tmp12 $tmp15 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:16
#   float x = sin((a + b + c) * 5.0);
	add		$tmp16 ___368_a ___368_b 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl"} %line{16} %argrw{"wrr"}
	add		$tmp17 $tmp16 ___368_c 	%argrw{"wrr"}
	mul		$tmp18 $tmp17 $const7 	%argrw{"wrr"}
	sin		___368_x $tmp18 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:17
#   float y = cos((-a + b - c) * 5.0);
	neg		$tmp19 ___368_a 	%line{17} %argrw{"wr"}
	add		$tmp20 $tmp19 ___368_b 	%argrw{"wrr"}
	sub		$tmp21 $tmp20 ___368_c 	%argrw{"wrr"}
	mul		$tmp22 $tmp21 $const7 	%argrw{"wrr"}
	cos		___368_y $tmp22 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:18
#   float z = -cos((-a - b + c) * 5.0);
	neg		$tmp24 ___368_a 	%line{18} %argrw{"wr"}
	sub		$tmp25 $tmp24 ___368_b 	%argrw{"wrr"}
	add		$tmp26 $tmp25 ___368_c 	%argrw{"wrr"}
	mul		$tmp27 $tmp26 $const7 	%argrw{"wrr"}
	cos		$tmp23 $tmp27 	%argrw{"wr"}
	neg		___368_z $tmp23 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:20
#   if (n > 0) {
	gt		$tmp28 depth $const2 	%line{20} %argrw{"wrr"}
	if		$tmp28 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:21
#     x *= dist;
	mul		___368_x ___368_x ___368_dist 	%line{21} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:22
#     y *= dist;
	mul		___368_y ___368_y ___368_dist 	%line{22} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:23
#     z *= dist;
	mul		___368_z ___368_z ___368_dist 	%line{23} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:24
#     y = -cos(x - y + z);
	sub		$tmp30 ___368_x ___368_y 	%line{24} %argrw{"wrr"}
	add		$tmp31 $tmp30 ___368_z 	%argrw{"wrr"}
	cos		$tmp29 $tmp31 	%argrw{"wr"}
	neg		___368_y $tmp29 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:25
#     y *= dist;
	mul		___368_y ___368_y ___368_dist 	%line{25} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:27
#     if (n > 1) {
	gt		$tmp32 depth $const5 	%line{27} %argrw{"wrr"}
	if		$tmp32 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:28
#       x = cos(x - y - z);
	sub		$tmp33 ___368_x ___368_y 	%line{28} %argrw{"wrr"}
	sub		$tmp34 $tmp33 ___368_z 	%argrw{"wrr"}
	cos		___368_x $tmp34 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:29
#       x *= dist;
	mul		___368_x ___368_x ___368_dist 	%line{29} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:31
#       if (n > 2) {
	gt		$tmp35 depth $const6 	%line{31} %argrw{"wrr"}
	if		$tmp35 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:32
#         z = sin(-x - y - z);
	neg		$tmp36 ___368_x 	%line{32} %argrw{"wr"}
	sub		$tmp37 $tmp36 ___368_y 	%argrw{"wrr"}
	sub		$tmp38 $tmp37 ___368_z 	%argrw{"wrr"}
	sin		___368_z $tmp38 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:33
#         z *= dist;
	mul		___368_z ___368_z ___368_dist 	%line{33} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:35
#         if (n > 3) {
	gt		$tmp39 depth $const8 	%line{35} %argrw{"wrr"}
	if		$tmp39 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:36
#           x = -cos(-x + y - z);
	neg		$tmp41 ___368_x 	%line{36} %argrw{"wr"}
	add		$tmp42 $tmp41 ___368_y 	%argrw{"wrr"}
	sub		$tmp43 $tmp42 ___368_z 	%argrw{"wrr"}
	cos		$tmp40 $tmp43 	%argrw{"wr"}
	neg		___368_x $tmp40 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:37
#           x *= dist;
	mul		___368_x ___368_x ___368_dist 	%line{37} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:39
#           if (n > 4) {
	gt		$tmp44 depth $const9 	%line{39} %argrw{"wrr"}
	if		$tmp44 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:40
#             y = -sin(-x + y + z);
	neg		$tmp46 ___368_x 	%line{40} %argrw{"wr"}
	add		$tmp47 $tmp46 ___368_y 	%argrw{"wrr"}
	add		$tmp48 $tmp47 ___368_z 	%argrw{"wrr"}
	sin		$tmp45 $tmp48 	%argrw{"wr"}
	neg		___368_y $tmp45 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:41
#             y *= dist;
	mul		___368_y ___368_y ___368_dist 	%line{41} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:43
#             if (n > 5) {
	gt		$tmp49 depth $const10 	%line{43} %argrw{"wrr"}
	if		$tmp49 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:44
#               y = -cos(-x + y + z);
	neg		$tmp51 ___368_x 	%line{44} %argrw{"wr"}
	add		$tmp52 $tmp51 ___368_y 	%argrw{"wrr"}
	add		$tmp53 $tmp52 ___368_z 	%argrw{"wrr"}
	cos		$tmp50 $tmp53 	%argrw{"wr"}
	neg		___368_y $tmp50 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:45
#               y *= dist;
	mul		___368_y ___368_y ___368_dist 	%line{45} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:47
#               if (n > 6) {
	gt		$tmp54 depth $const11 	%line{47} %argrw{"wrr"}
	if		$tmp54 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:48
#                 x = cos(x + y + z);
	add		$tmp55 ___368_x ___368_y 	%line{48} %argrw{"wrr"}
	add		$tmp56 $tmp55 ___368_z 	%argrw{"wrr"}
	cos		___368_x $tmp56 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:49
#                 x *= dist;
	mul		___368_x ___368_x ___368_dist 	%line{49} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:51
#                 if (n > 7) {
	gt		$tmp57 depth $const12 	%line{51} %argrw{"wrr"}
	if		$tmp57 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:52
#                   z = sin(x + y - z);
	add		$tmp58 ___368_x ___368_y 	%line{52} %argrw{"wrr"}
	sub		$tmp59 $tmp58 ___368_z 	%argrw{"wrr"}
	sin		___368_z $tmp59 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:53
#                   z *= dist;
	mul		___368_z ___368_z ___368_dist 	%line{53} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:55
#                   if (n > 8) {
	gt		$tmp60 depth $const13 	%line{55} %argrw{"wrr"}
	if		$tmp60 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:56
#                     x = -cos(-x - y + z);
	neg		$tmp62 ___368_x 	%line{56} %argrw{"wr"}
	sub		$tmp63 $tmp62 ___368_y 	%argrw{"wrr"}
	add		$tmp64 $tmp63 ___368_z 	%argrw{"wrr"}
	cos		$tmp61 $tmp64 	%argrw{"wr"}
	neg		___368_x $tmp61 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:57
#                     x *= dist;
	mul		___368_x ___368_x ___368_dist 	%line{57} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:59
#                     if (n > 9) {
	gt		$tmp65 depth $const14 	%line{59} %argrw{"wrr"}
	if		$tmp65 116 116 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:60
#                       y = -sin(x - y + z);
	sub		$tmp67 ___368_x ___368_y 	%line{60} %argrw{"wrr"}
	add		$tmp68 $tmp67 ___368_z 	%argrw{"wrr"}
	sin		$tmp66 $tmp68 	%argrw{"wr"}
	neg		___368_y $tmp66 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:61
#                       y *= dist;
	mul		___368_y ___368_y ___368_dist 	%line{61} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:73
#   if (dist != 0.0) {
	neq		$tmp69 ___368_dist $const15 	%line{73} %argrw{"wrr"}
	if		$tmp69 122 122 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:74
#     dist *= 2.0;
	mul		___368_dist ___368_dist $const16 	%line{74} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:75
#     x /= dist;
	div		___368_x ___368_x ___368_dist 	%line{75} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:76
#     y /= dist;
	div		___368_y ___368_y ___368_dist 	%line{76} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:77
#     z /= dist;
	div		___368_z ___368_z ___368_dist 	%line{77} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:80
#   return color(0.5 - x, 0.5 - y, 0.5 - z);
	sub		$tmp70 $const17 ___368_x 	%line{80} %argrw{"wrr"}
	sub		$tmp71 $const17 ___368_y 	%argrw{"wrr"}
	sub		$tmp72 $const17 ___368_z 	%argrw{"wrr"}
	color		Color $tmp70 $tmp71 $tmp72 	%argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_magic_texture.osl:98
#   Fac = (Color[0] + Color[1] + Color[2]) * (1.0 / 3.0);
	compref		$tmp73 Color $const2 	%line{98} %argrw{"wrr"}
	compref		$tmp74 Color $const5 	%argrw{"wrr"}
	add		$tmp75 $tmp73 $tmp74 	%argrw{"wrr"}
	compref		$tmp76 Color $const6 	%argrw{"wrr"}
	add		$tmp77 $tmp75 $tmp76 	%argrw{"wrr"}
	mul		Fac $tmp77 $const18 	%argrw{"wrr"}
	end
