OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_environment_texture.oso
shader node_environment_texture
param	int	use_mapping	0		%read{2,2} %write{2147483647,-1}
param	matrix	mapping	0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0		%read{3,3} %write{2147483647,-1} %derivs
param	vector	Vector	0 0 0		%read{1,1} %write{0,0} %derivs %initexpr
param	string	filename	""		%read{49,49} %write{2147483647,-1}
param	string	projection	"equirectangular"		%read{5,5} %write{2147483647,-1}
param	string	interpolation	"linear"		%read{49,49} %write{2147483647,-1}
param	int	compress_as_srgb	0		%read{68,68} %write{2147483647,-1}
param	int	ignore_alpha	0		%read{50,50} %write{2147483647,-1}
param	int	unassociate_alpha	0		%read{52,52} %write{2147483647,-1}
param	int	is_float	1		%read{64,64} %write{2147483647,-1}
oparam	color	Color	0 0 0		%read{61,96} %write{49,109}
oparam	float	Alpha	1		%read{54,61} %write{49,51}
global	point	P	%read{0,0} %write{2147483647,-1} %derivs
local	float	___410_u	%read{25,25} %write{13,13} %derivs
local	float	___410_v	%read{25,25} %write{24,24} %derivs
local	vector	___411_dir	%read{28,42} %write{27,38} %derivs
local	float	___411_div	%read{36,38} %write{35,35} %derivs
local	float	___411_u	%read{45,45} %write{41,41} %derivs
local	float	___411_v	%read{45,45} %write{44,44} %derivs
local	vector	p	%read{3,47} %write{1,45} %derivs
const	string	$const1	"equirectangular"		%read{5,5} %write{2147483647,-1}
temp	int	$tmp1	%read{6,6} %write{5,5}
const	string	$const2	"environment_texture_direction_to_equirectangular"		%read{7,7} %write{2147483647,-1}
temp	float	$tmp2	%read{11,11} %write{10,10} %derivs
const	int	$const3	1		%read{8,83} %write{2147483647,-1}
temp	float	$tmp3	%read{10,10} %write{8,8} %derivs
const	int	$const4	0		%read{9,70} %write{2147483647,-1}
temp	float	$tmp4	%read{10,10} %write{9,9} %derivs
temp	float	$tmp5	%read{12,12} %write{11,11} %derivs
const	float	$const5	6.28318548		%read{12,12} %write{2147483647,-1}
temp	float	$tmp6	%read{13,13} %write{12,12} %derivs
const	float	$const6	0.5		%read{13,44} %write{2147483647,-1}
temp	float	$tmp7	%read{23,23} %write{22,22} %derivs
const	int	$const7	2		%read{14,96} %write{2147483647,-1}
temp	float	$tmp8	%read{22,22} %write{14,14} %derivs
temp	float	$tmp9	%read{22,22} %write{21,21} %derivs
temp	float	$tmp10	%read{18,18} %write{15,15} %derivs
temp	float	$tmp11	%read{19,19} %write{16,16} %derivs
const	string	$const8	"hypot"		%read{17,17} %write{2147483647,-1}
temp	float	$tmp12	%read{20,20} %write{18,18} %derivs
temp	float	$tmp13	%read{20,20} %write{19,19} %derivs
temp	float	$tmp14	%read{21,21} %write{20,20} %derivs
const	float	$const9	3.14159274		%read{23,23} %write{2147483647,-1}
temp	float	$tmp15	%read{24,24} %write{23,23} %derivs
const	float	$const10	0		%read{25,102} %write{2147483647,-1}
const	string	$const11	"environment_texture_direction_to_mirrorball"		%read{26,26} %write{2147483647,-1}
temp	float	$tmp16	%read{29,29} %write{28,28} %derivs
const	float	$const12	1		%read{29,66} %write{2147483647,-1}
temp	float	$tmp17	%read{30,30} %write{29,29} %derivs
const	float	$const13	2		%read{35,35} %write{2147483647,-1}
temp	float	$tmp18	%read{35,35} %write{34,34} %derivs
temp	float	$tmp19	%read{34,34} %write{33,33} %derivs
const	float	$const14	-0.5		%read{32,32} %write{2147483647,-1}
temp	float	$tmp20	%read{32,32} %write{31,31} %derivs
temp	float	$tmp21	%read{33,33} %write{32,32} %derivs
temp	int	$tmp22	%read{37,37} %write{36,36}
temp	float	$tmp23	%read{40,40} %write{39,39} %derivs
temp	float	$tmp24	%read{41,41} %write{40,40} %derivs
temp	float	$tmp25	%read{43,43} %write{42,42} %derivs
temp	float	$tmp26	%read{44,44} %write{43,43} %derivs
temp	float	$tmp27	%read{49,49} %write{46,46} %derivs
temp	float	$tmp28	%read{48,48} %write{47,47} %derivs
temp	float	$tmp29	%read{49,49} %write{48,48} %derivs
const	string	$const15	"wrap"		%read{49,49} %write{2147483647,-1}
const	string	$const16	"periodic"		%read{49,49} %write{2147483647,-1}
const	string	$const17	"interp"		%read{49,49} %write{2147483647,-1}
const	string	$const18	"alpha"		%read{49,49} %write{2147483647,-1}
const	string	$const19	"color_unpremultiply"		%read{53,53} %write{2147483647,-1}
temp	int	$tmp30	%read{55,55} %write{54,54}
temp	int	$tmp31	%read{56,60} %write{55,59}
temp	int	$tmp32	%read{58,58} %write{57,57}
temp	int	$tmp33	%read{59,59} %write{58,58}
temp	int	$tmp34	%read{65,65} %write{64,64}
temp	color	$tmp35	%read{67,67} %write{66,66}
const	string	$const20	"color_srgb_to_scene_linear"		%read{69,97} %write{2147483647,-1}
temp	float	$tmp36	%read{109,109} %write{76,81}
temp	float	$tmp37	%read{72,79} %write{70,70}
const	float	$const21	0.0404499993		%read{72,98} %write{2147483647,-1}
temp	int	$tmp38	%read{73,73} %write{72,72}
temp	int	$tmp39	%read{75,75} %write{74,74}
const	float	$const22	0.0773993805		%read{77,103} %write{2147483647,-1}
const	float	$const23	0.0549999997		%read{79,105} %write{2147483647,-1}
temp	float	$tmp40	%read{80,80} %write{79,79}
const	float	$const24	0.947867334		%read{80,106} %write{2147483647,-1}
temp	float	$tmp41	%read{81,81} %write{80,80}
const	float	$const25	2.4000001		%read{81,107} %write{2147483647,-1}
temp	float	$tmp42	%read{109,109} %write{89,94}
temp	float	$tmp43	%read{85,92} %write{83,83}
temp	int	$tmp44	%read{86,86} %write{85,85}
temp	int	$tmp45	%read{88,88} %write{87,87}
temp	float	$tmp46	%read{93,93} %write{92,92}
temp	float	$tmp47	%read{94,94} %write{93,93}
temp	float	$tmp48	%read{109,109} %write{102,107}
temp	float	$tmp49	%read{98,105} %write{96,96}
temp	int	$tmp50	%read{99,99} %write{98,98}
temp	int	$tmp51	%read{101,101} %write{100,100}
temp	float	$tmp52	%read{106,106} %write{105,105}
temp	float	$tmp53	%read{107,107} %write{106,106}
code Vector
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:34
#     vector Vector = P,
	assign		Vector P 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl"} %line{34} %argrw{"wr"}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:45
#   vector p = Vector;
	assign		p Vector 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl"} %line{45} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:47
#   if (use_mapping)
	if		use_mapping 4 4 	%line{47} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:48
#     p = transform(mapping, p);
	transformv	p mapping p 	%line{48} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:50
#   p = normalize(p);
	normalize	p p 	%line{50} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:52
#   if (projection == "equirectangular")
	eq		$tmp1 projection $const1 	%line{52} %argrw{"wrr"}
	if		$tmp1 26 46 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:53
#     p = environment_texture_direction_to_equirectangular(p);
	functioncall	$const2 26 	%line{53} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:10
#   float u = -atan2(dir[1], dir[0]) / (M_2PI) + 0.5;
	compref		$tmp3 p $const3 	%line{10} %argrw{"wrr"}
	compref		$tmp4 p $const4 	%argrw{"wrr"}
	atan2		$tmp2 $tmp3 $tmp4 	%argrw{"wrr"}
	neg		$tmp5 $tmp2 	%argrw{"wr"}
	div		$tmp6 $tmp5 $const5 	%argrw{"wrr"}
	add		___410_u $tmp6 $const6 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:11
#   float v = atan2(dir[2], hypot(dir[0], dir[1])) / M_PI + 0.5;
	compref		$tmp8 p $const7 	%line{11} %argrw{"wrr"}
	compref		$tmp10 p $const4 	%argrw{"wrr"}
	compref		$tmp11 p $const3 	%argrw{"wrr"}
	functioncall	$const8 22 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h:101
# float hypot (float a, float b) { return sqrt (a*a + b*b); }
	mul		$tmp12 $tmp10 $tmp10 	%filename{"/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders/stdosl.h"} %line{101} %argrw{"wrr"}
	mul		$tmp13 $tmp11 $tmp11 	%argrw{"wrr"}
	add		$tmp14 $tmp12 $tmp13 	%argrw{"wrr"}
	sqrt		$tmp9 $tmp14 	%argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:11
#   float v = atan2(dir[2], hypot(dir[0], dir[1])) / M_PI + 0.5;
	atan2		$tmp7 $tmp8 $tmp9 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl"} %line{11} %argrw{"wrr"}
	div		$tmp15 $tmp7 $const9 	%argrw{"wrr"}
	add		___410_v $tmp15 $const6 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:13
#   return vector(u, v, 0.0);
	vector		p ___410_u ___410_v $const10 	%line{13} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:55
#     p = environment_texture_direction_to_mirrorball(p);
	functioncall	$const11 46 	%line{55} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:18
#   vector dir = idir;
	assign		___411_dir p 	%line{18} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:19
#   dir[1] -= 1.0;
	compref		$tmp16 ___411_dir $const3 	%line{19} %argrw{"wrr"}
	sub		$tmp17 $tmp16 $const12 	%argrw{"wrr"}
	compassign	___411_dir $const3 $tmp17 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:21
#   float div = 2.0 * sqrt(max(-0.5 * dir[1], 0.0));
	compref		$tmp20 ___411_dir $const3 	%line{21} %argrw{"wrr"}
	mul		$tmp21 $const14 $tmp20 	%argrw{"wrr"}
	max		$tmp19 $tmp21 $const10 	%argrw{"wrr"}
	sqrt		$tmp18 $tmp19 	%argrw{"wr"}
	mul		___411_div $const13 $tmp18 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:22
#   if (div > 0.0)
	gt		$tmp22 ___411_div $const10 	%line{22} %argrw{"wrr"}
	if		$tmp22 39 39 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:23
#     dir /= div;
	div		___411_dir ___411_dir ___411_div 	%line{23} %argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:25
#   float u = 0.5 * (dir[0] + 1.0);
	compref		$tmp23 ___411_dir $const4 	%line{25} %argrw{"wrr"}
	add		$tmp24 $tmp23 $const12 	%argrw{"wrr"}
	mul		___411_u $const6 $tmp24 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:26
#   float v = 0.5 * (dir[2] + 1.0);
	compref		$tmp25 ___411_dir $const7 	%line{26} %argrw{"wrr"}
	add		$tmp26 $tmp25 $const12 	%argrw{"wrr"}
	mul		___411_v $const6 $tmp26 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:28
#   return vector(u, v, 0.0);
	vector		p ___411_u ___411_v $const10 	%line{28} %argrw{"wrrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:59
#       filename, p[0], 1.0 - p[1], "wrap", "periodic", "interp", interpolation, "alpha", Alpha);
	compref		$tmp27 p $const4 	%line{59} %argrw{"wrr"}
	compref		$tmp28 p $const3 	%argrw{"wrr"}
	sub		$tmp29 $const12 $tmp28 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:58
#   Color = (color)texture(
	texture		Color filename $tmp27 $tmp29 $const15 $const16 $const17 interpolation $const18 Alpha 	%line{58} %argrw{"wrrrrrrrrw"} %argderivs{2,3}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:61
#   if (ignore_alpha) {
	if		ignore_alpha 52 68 	%line{61} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:62
#     Alpha = 1.0;
	assign		Alpha $const12 	%line{62} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:64
#   else if (unassociate_alpha) {
	if		unassociate_alpha 68 68 	%line{64} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:65
#     Color = color_unpremultiply(Color, Alpha);
	functioncall	$const19 64 	%line{65} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:43
#   if (alpha != 1.0 && alpha != 0.0) {
	neq		$tmp30 Alpha $const12 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{43} %argrw{"wrr"}
	neq		$tmp31 $tmp30 $const4 	%argrw{"wrr"}
	if		$tmp31 60 60 	%argrw{"r"}
	neq		$tmp32 Alpha $const10 	%argrw{"wrr"}
	neq		$tmp33 $tmp32 $const4 	%argrw{"wrr"}
	assign		$tmp31 $tmp33 	%argrw{"wr"}
	if		$tmp31 63 63 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:44
#     return c / alpha;
	div		Color Color Alpha 	%line{44} %argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:47
#   return c;
	assign		Color Color 	%line{47} %argrw{"wr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:67
#     if (!is_float)
	eq		$tmp34 is_float $const4 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl"} %line{67} %argrw{"wrr"}
	if		$tmp34 68 68 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:68
#       Color = min(Color, 1.0);
	assign		$tmp35 $const12 	%line{68} %argrw{"wr"}
	min		Color Color $tmp35 	%argrw{"wrr"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:71
#   if (compress_as_srgb)
	if		compress_as_srgb 110 110 	%line{71} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_environment_texture.osl:72
#     Color = color_srgb_to_scene_linear(Color);
	functioncall	$const20 110 	%line{72} %argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:29
#   return color(color_srgb_to_scene_linear(c[0]),
	compref		$tmp37 Color $const4 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h"} %line{29} %argrw{"wrr"}
	functioncall	$const20 83 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:9
#   if (c < 0.04045) {
	lt		$tmp38 $tmp37 $const21 	%line{9} %argrw{"wrr"}
	if		$tmp38 79 83 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:10
#     return (c < 0.0) ? 0.0 : c * (1.0 / 12.92);
	lt		$tmp39 $tmp37 $const10 	%line{10} %argrw{"wrr"}
	if		$tmp39 77 78 	%argrw{"r"}
	assign		$tmp36 $const10 	%argrw{"wr"}
	mul		$tmp36 $tmp37 $const22 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:13
#     return pow((c + 0.055) * (1.0 / 1.055), 2.4);
	add		$tmp40 $tmp37 $const23 	%line{13} %argrw{"wrr"}
	mul		$tmp41 $tmp40 $const24 	%argrw{"wrr"}
	pow		$tmp36 $tmp41 $const25 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:30
#                color_srgb_to_scene_linear(c[1]),
	compref		$tmp43 Color $const3 	%line{30} %argrw{"wrr"}
	functioncall	$const20 96 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:9
#   if (c < 0.04045) {
	lt		$tmp44 $tmp43 $const21 	%line{9} %argrw{"wrr"}
	if		$tmp44 92 96 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:10
#     return (c < 0.0) ? 0.0 : c * (1.0 / 12.92);
	lt		$tmp45 $tmp43 $const10 	%line{10} %argrw{"wrr"}
	if		$tmp45 90 91 	%argrw{"r"}
	assign		$tmp42 $const10 	%argrw{"wr"}
	mul		$tmp42 $tmp43 $const22 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:13
#     return pow((c + 0.055) * (1.0 / 1.055), 2.4);
	add		$tmp46 $tmp43 $const23 	%line{13} %argrw{"wrr"}
	mul		$tmp47 $tmp46 $const24 	%argrw{"wrr"}
	pow		$tmp42 $tmp47 $const25 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:31
#                color_srgb_to_scene_linear(c[2]));
	compref		$tmp49 Color $const7 	%line{31} %argrw{"wrr"}
	functioncall	$const20 109 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:9
#   if (c < 0.04045) {
	lt		$tmp50 $tmp49 $const21 	%line{9} %argrw{"wrr"}
	if		$tmp50 105 109 	%argrw{"r"}
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:10
#     return (c < 0.0) ? 0.0 : c * (1.0 / 12.92);
	lt		$tmp51 $tmp49 $const10 	%line{10} %argrw{"wrr"}
	if		$tmp51 103 104 	%argrw{"r"}
	assign		$tmp48 $const10 	%argrw{"wr"}
	mul		$tmp48 $tmp49 $const22 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:13
#     return pow((c + 0.055) * (1.0 / 1.055), 2.4);
	add		$tmp52 $tmp49 $const23 	%line{13} %argrw{"wrr"}
	mul		$tmp53 $tmp52 $const24 	%argrw{"wrr"}
	pow		$tmp48 $tmp53 $const25 	%argrw{"wrr"}
	return
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_color.h:29
#   return color(color_srgb_to_scene_linear(c[0]),
	color		Color $tmp36 $tmp42 $tmp48 	%line{29} %argrw{"wrrr"}
	end
