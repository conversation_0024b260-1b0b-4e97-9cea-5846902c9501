OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_ray_portal_bsdf.oso
shader node_ray_portal_bsdf
param	color	Color	0.800000012 0.800000012 0.800000012		%read{1,1} %write{2147483647,-1}
param	vector	Position	0 0 0		%read{0,0} %write{2147483647,-1}
param	vector	Direction	0 0 0		%read{0,0} %write{2147483647,-1}
oparam	closure color	BSDF			%read{2147483647,-1} %write{1,1}
temp	closure color	$tmp1	%read{1,1} %write{0,0}
const	string	$const1	"ray_portal_bsdf"		%read{0,0} %write{2147483647,-1}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ray_portal_bsdf.osl:12
#   BSDF = Color * ray_portal_bsdf(Position, Direction);
	closure		$tmp1 $const1 Position Direction 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_ray_portal_bsdf.osl"} %line{12} %argrw{"wrrr"}
	mul		BSDF $tmp1 Color 	%argrw{"wrr"}
	end
