OpenShadingLanguage 1.00
# Compiled by oslc 1.14.4.0dev
# options: -q -O2 -I/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders -I/home/<USER>/git/blender-vdev/blender.git/lib/linux_x64/osl/share/OSL/shaders -o /home/<USER>/git/blender-vdev/build_release/intern/cycles/kernel/osl/shaders/node_output_surface.oso
surface node_output_surface
param	closure color	Surface			%read{0,0} %write{2147483647,-1}
global	closure color	Ci	%read{2147483647,-1} %write{0,0}
code ___main___
# /home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_output_surface.osl:9
#   Ci = Surface;
	assign		Ci Surface 	%filename{"/home/<USER>/git/blender-vdev/blender.git/intern/cycles/kernel/osl/shaders/node_output_surface.osl"} %line{9} %argrw{"wr"}
	end
