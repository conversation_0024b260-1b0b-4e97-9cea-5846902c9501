# SPDX-FileCopyrightText: 2019-2022 Blender Foundation
#
# SPDX-License-Identifier: GPL-2.0-or-later

from .misc import shuffle_matrix
from .widgets import (create_widget, widget_generator, register_widget,
                      generate_circle_geometry)


# Common Widgets

@widget_generator(register="line")
def create_line_widget(geom):
    """ Creates a basic line widget, a line that spans the length of the bone. """
    geom.verts = [(0, 0, 0), (0, 1, 0)]
    geom.edges = [(0, 1)]


@widget_generator
def create_circle_widget(geom, *, radius=1.0, head_tail=0.0, radius_x=None, head_tail_x=None, with_line=False):
    """ Creates a basic circle widget, a circle around the y-axis.
        radius: the radius of the circle
        head_tail: where along the length of the bone the circle is (0.0=head, 1.0=tail)
        radius_x: if not None, specifies a different radius along the X axis, creating an ellipse
        head_tail_x: if not None, specifies a different value along the X axis to create
                     a circle deformed in the Y direction.
    """
    radius_x = radius_x if radius_x is not None else radius
    ht_delta = head_tail_x - head_tail if head_tail_x is not None else 0
    generate_circle_geometry(
        geom, (0, 0, head_tail), radius,
        radius_x=radius_x, depth_x=ht_delta,
        matrix=shuffle_matrix['xzy'], steps=32
    )
    if with_line:
        geom.edges.append((8, 24))  # Z axis line


register_widget("circle", create_circle_widget, radius=0.5)


@widget_generator(register="cube")
def create_cube_widget(geom, *, radius=0.5):
    """ Creates a basic cube widget.
    """
    r = radius
    geom.verts = [(r, r, r), (r, -r, r), (-r, -r, r), (-r, r, r), (r, r, -r), (r, -r, -r), (-r, -r, -r), (-r, r, -r)]
    geom.edges = [(0, 1), (1, 2), (2, 3), (3, 0), (4, 5), (5, 6), (6, 7), (7, 4), (0, 4), (1, 5), (2, 6), (3, 7)]


@widget_generator(register="diamond")
def create_diamond_widget(geom, *, radius=0.5):
    """Creates a basic octahedron widget"""
    r = radius
    geom.verts = [(r, 0, 0), (0, -r, 0), (0, r, 0), (0, 0, -r), (0, 0, r), (-r, 0, 0)]
    geom.edges = [(0, 1), (2, 3), (4, 5), (1, 5), (5, 2), (0, 2), (4, 2), (3, 1), (1, 4), (5, 3), (3, 0), (4, 0)]


@widget_generator(register="cube_truncated")
def create_truncated_cube_widget(geom, *, radius=0.5):
    """Creates a basic truncated cube widget"""
    r = radius
    r3 = radius / 3
    geom.verts = [(r, r3, r), (r, -r3, r), (r3, -r, r), (-r3, -r, r), (-r, -r3, r), (-r, r3, r),
                  (-r3, r, r), (r3, r, r), (r, r3, -r), (r, -r3, -r), (r3, -r, -r), (-r3, -r, -r),
                  (-r, -r3, -r), (-r, r3, -r), (-r3, r, -r), (r3, r, -r), (r, r, r3), (r, r, -r3),
                  (r, -r, r3), (r, -r, -r3), (-r, -r, r3), (-r, -r, -r3), (-r, r, r3), (-r, r, -r3)]
    geom.edges = [(0, 1), (2, 3), (4, 5), (6, 7), (8, 9), (10, 11), (12, 13), (14, 15), (16, 17), (18, 19),
                  (20, 21), (22, 23), (6, 5), (5, 22), (22, 6), (7, 0), (0, 16), (16, 7), (14, 13), (13, 23),
                  (23, 14), (15, 8), (8, 17), (17, 15), (12, 11), (11, 21), (21, 12), (10, 9), (9, 19), (19, 10),
                  (2, 1), (1, 18), (18, 2), (4, 3), (3, 20), (20, 4)]


@widget_generator(register="cuboctahedron")
def create_cuboctahedron_widget(geom, *, radius=0.5):
    """Creates a basic cuboctahedron (deeply truncated cube) widget"""
    r = radius
    geom.verts = [(r, 0, r), (0, -r, r), (-r, 0, r), (0, r, r), (r, 0, -r), (0, -r, -r),
                  (-r, 0, -r), (0, r, -r), (r, r, 0), (r, -r, 0), (-r, -r, 0), (-r, r, 0)]
    geom.edges = [(7, 8), (3, 8), (3, 11), (7, 11), (2, 3), (1, 2), (0, 1), (0, 3), (0, 8), (0, 9),
                  (4, 9), (4, 8), (5, 9), (1, 9), (1, 10), (5, 10), (2, 10), (6, 10), (6, 11), (2, 11),
                  (4, 7), (4, 5), (5, 6), (6, 7)]


def create_chain_widget(rig, bone_name, cube=False, radius=0.5, invert=False,
                        bone_transform_name=None, axis="y", offset=0.0):
    """Creates a basic chain widget
    """
    obj = create_widget(rig, bone_name, bone_transform_name)
    if obj is not None:
        r = radius
        if cube:
            rh = r
        else:
            rh = radius / 2
        if invert:
            verts = [(rh, rh, rh), (r, -r, r), (-r, -r, r), (-rh, rh, rh), (rh, rh, -rh),
                     (r, -r, -r), (-r, -r, -r), (-rh, rh, -rh)]
        else:
            verts = [(r, r, r), (rh, -rh, rh), (-rh, -rh, rh), (-r, r, r), (r, r, -r),
                     (rh, -rh, -rh), (-rh, -rh, -rh), (-r, r, -r)]
        edges = [(0, 1), (1, 2), (2, 3), (3, 0), (4, 5), (5, 6), (6, 7), (7, 4), (0, 4),
                 (1, 5), (2, 6), (3, 7)]
        mesh = obj.data
        mesh.from_pydata(verts, edges, [])
        mesh.update()
        from .widgets import adjust_widget_axis
        adjust_widget_axis(obj, axis=axis, offset=offset)
        return obj


@widget_generator(register="sphere")
def create_sphere_widget(geom, *, radius=0.5):
    """ Creates a basic sphere widget, three perpendicular overlapping circles.
    """
    geom.verts = [(0.3535533845424652, 0.3535533845424652, 0.0),
                  (0.4619397521018982, 0.19134171307086945, 0.0),
                  (0.5, -2.1855694143368964e-08, 0.0),
                  (0.4619397521018982, -0.19134175777435303, 0.0),
                  (0.3535533845424652, -0.3535533845424652, 0.0),
                  (0.19134174287319183, -0.4619397521018982, 0.0),
                  (7.549790126404332e-08, -0.5, 0.0),
                  (-0.1913416087627411, -0.46193981170654297, 0.0),
                  (-0.35355329513549805, -0.35355350375175476, 0.0),
                  (-0.4619397521018982, -0.19134178757667542, 0.0),
                  (-0.5, 5.962440319251527e-09, 0.0),
                  (-0.4619397222995758, 0.1913418024778366, 0.0),
                  (-0.35355326533317566, 0.35355350375175476, 0.0),
                  (-0.19134148955345154, 0.46193987131118774, 0.0),
                  (3.2584136988589307e-07, 0.5, 0.0),
                  (0.1913420855998993, 0.46193960309028625, 0.0),
                  (7.450580596923828e-08, 0.46193960309028625, 0.19134199619293213),
                  (5.9254205098113744e-08, 0.5, 2.323586443253589e-07),
                  (4.470348358154297e-08, 0.46193987131118774, -0.1913415789604187),
                  (2.9802322387695312e-08, 0.35355350375175476, -0.3535533547401428),
                  (2.9802322387695312e-08, 0.19134178757667542, -0.46193981170654297),
                  (5.960464477539063e-08, -1.1151834122813398e-08, -0.5000000596046448),
                  (5.960464477539063e-08, -0.1913418024778366, -0.46193984150886536),
                  (5.960464477539063e-08, -0.35355350375175476, -0.3535533845424652),
                  (7.450580596923828e-08, -0.46193981170654297, -0.19134166836738586),
                  (9.348272556053416e-08, -0.5, 1.624372103492533e-08),
                  (1.043081283569336e-07, -0.4619397521018982, 0.19134168326854706),
                  (1.1920928955078125e-07, -0.3535533845424652, 0.35355329513549805),
                  (1.1920928955078125e-07, -0.19134174287319183, 0.46193966269493103),
                  (1.1920928955078125e-07, -4.7414250303745575e-09, 0.49999991059303284),
                  (1.1920928955078125e-07, 0.19134172797203064, 0.46193966269493103),
                  (8.940696716308594e-08, 0.3535533845424652, 0.35355329513549805),
                  (0.3535534739494324, 0.0, 0.35355329513549805),
                  (0.1913418173789978, -2.9802322387695312e-08, 0.46193966269493103),
                  (8.303572940349113e-08, -5.005858838558197e-08, 0.49999991059303284),
                  (-0.19134165346622467, -5.960464477539063e-08, 0.46193966269493103),
                  (-0.35355329513549805, -8.940696716308594e-08, 0.35355329513549805),
                  (-0.46193963289260864, -5.960464477539063e-08, 0.19134168326854706),
                  (-0.49999991059303284, -5.960464477539063e-08, 1.624372103492533e-08),
                  (-0.4619397521018982, -2.9802322387695312e-08, -0.19134166836738586),
                  (-0.3535534143447876, -2.9802322387695312e-08, -0.3535533845424652),
                  (-0.19134171307086945, 0.0, -0.46193984150886536),
                  (7.662531942287387e-08, 9.546055501630235e-09, -0.5000000596046448),
                  (0.19134187698364258, 5.960464477539063e-08, -0.46193981170654297),
                  (0.3535535931587219, 5.960464477539063e-08, -0.3535533547401428),
                  (0.4619399905204773, 5.960464477539063e-08, -0.1913415789604187),
                  (0.5000000596046448, 5.960464477539063e-08, 2.323586443253589e-07),
                  (0.4619396924972534, 2.9802322387695312e-08, 0.19134199619293213)]
    if radius != 0.5:
        radius /= 0.5
        geom.verts = [(a[0] * radius, a[1] * radius, a[2] * radius) for a in geom.verts]
    geom.edges = [(0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6), (6, 7), (7, 8), (8, 9), (9, 10),
                  (10, 11), (11, 12), (12, 13), (13, 14), (14, 15), (0, 15), (16, 31), (16, 17),
                  (17, 18), (18, 19), (19, 20), (20, 21), (21, 22), (22, 23), (23, 24), (24, 25),
                  (25, 26), (26, 27), (27, 28), (28, 29), (29, 30), (30, 31), (32, 33), (33, 34),
                  (34, 35), (35, 36), (36, 37), (37, 38), (38, 39), (39, 40), (40, 41), (41, 42),
                  (42, 43), (43, 44), (44, 45), (45, 46), (46, 47), (32, 47)]


@widget_generator(register="limb")
def create_limb_widget(geom):
    """ Creates a basic limb widget, a line that spans the length of the
        bone, with a circle around the center.
    """
    geom.verts = [(-1.1920928955078125e-07, 1.7881393432617188e-07, 0.0),
                  (3.5762786865234375e-07, 1.0000004768371582, 0.0),
                  (0.1767769455909729, 0.5000001192092896, 0.17677664756774902),
                  (0.20786768198013306, 0.5000001192092896, 0.1388925313949585),
                  (0.23097014427185059, 0.5000001192092896, 0.09567084908485413),
                  (0.24519658088684082, 0.5000001192092896, 0.048772573471069336),
                  (0.2500002384185791, 0.5000001192092896, -2.545945676502015e-09),
                  (0.24519658088684082, 0.5000001192092896, -0.048772573471069336),
                  (0.23097014427185059, 0.5000001192092896, -0.09567084908485413),
                  (0.20786768198013306, 0.5000001192092896, -0.13889259099960327),
                  (0.1767769455909729, 0.5000001192092896, -0.1767767071723938),
                  (0.13889282941818237, 0.5000001192092896, -0.20786744356155396),
                  (0.09567105770111084, 0.5000001192092896, -0.23096990585327148),
                  (0.04877278208732605, 0.5000001192092896, -0.24519634246826172),
                  (1.7279069197684294e-07, 0.5000000596046448, -0.25),
                  (-0.0487724244594574, 0.5000001192092896, -0.24519634246826172),
                  (-0.09567070007324219, 0.5000001192092896, -0.2309698462486267),
                  (-0.13889241218566895, 0.5000001192092896, -0.20786738395690918),
                  (-0.17677652835845947, 0.5000001192092896, -0.17677664756774902),
                  (-0.20786726474761963, 0.5000001192092896, -0.13889244198799133),
                  (-0.23096972703933716, 0.5000001192092896, -0.09567070007324219),
                  (-0.24519610404968262, 0.5000001192092896, -0.04877239465713501),
                  (-0.2499997615814209, 0.5000001192092896, 2.1997936983098043e-07),
                  (-0.24519598484039307, 0.5000001192092896, 0.04877282679080963),
                  (-0.23096948862075806, 0.5000001192092896, 0.09567108750343323),
                  (-0.20786696672439575, 0.5000001192092896, 0.1388927698135376),
                  (-0.1767762303352356, 0.5000001192092896, 0.17677688598632812),
                  (-0.13889199495315552, 0.5000001192092896, 0.2078675627708435),
                  (-0.09567028284072876, 0.5000001192092896, 0.23097002506256104),
                  (-0.048771947622299194, 0.5000001192092896, 0.24519634246826172),
                  (6.555903269145347e-07, 0.5000001192092896, 0.25),
                  (0.04877324402332306, 0.5000001192092896, 0.24519622325897217),
                  (0.09567153453826904, 0.5000001192092896, 0.23096966743469238),
                  (0.13889318704605103, 0.5000001192092896, 0.20786714553833008)]
    geom.edges = [(0, 1), (2, 3), (4, 3), (5, 4), (5, 6), (6, 7), (8, 7), (8, 9), (10, 9),
                  (10, 11), (11, 12), (13, 12), (14, 13), (14, 15), (16, 15), (16, 17), (17, 18),
                  (19, 18), (19, 20), (21, 20), (21, 22), (22, 23), (24, 23), (25, 24), (25, 26),
                  (27, 26), (27, 28), (29, 28), (29, 30), (30, 31), (32, 31), (32, 33), (2, 33)]


@widget_generator(register="bone")
def create_bone_widget(geom, *, r1=0.1, l1=0.0, r2=0.04, l2=1.0):
    """ Creates a basic bone widget, a simple obelisk-esk shape.
    """
    geom.verts = [(r2, l2, -r2), (r1, l1, -r1), (-r1, l1, -r1), (-r2, l2, -r2), (r2, l2, r2),
                  (r1, l1, r1), (-r1, l1, r1), (-r2, l2, r2)]
    geom.edges = [(1, 2), (0, 1), (0, 3), (2, 3), (4, 5), (5, 6), (6, 7), (4, 7), (1, 5), (0, 4),
                  (2, 6), (3, 7)]


@widget_generator(register="pivot")
def create_pivot_widget(geom, *, radius=0.5, axis_size=1.0, cap_size=1.0, square=True):
    """Creates a widget similar to Plain Axes empty, but with a cross or
       a square on the end of each axis line.
    """
    axis = radius * axis_size
    cap = 0.1 * radius * cap_size
    if square:
        geom.verts = [(0, 0, -axis), (-axis, 0, 0), (0, 0, axis), (axis, 0, 0), (axis, cap, -cap),
                      (axis, cap, cap), (0, -axis, 0), (0, axis, 0), (cap, axis, cap),
                      (cap, axis, -cap), (axis, -cap, -cap), (axis, -cap, cap),
                      (-cap, axis, cap), (-cap, axis, -cap), (-axis, cap, cap), (-axis, cap, -cap),
                      (-axis, -cap, cap), (-axis, -cap, -cap), (-cap, -axis, cap),
                      (-cap, -axis, -cap), (cap, -axis, cap), (cap, -axis, -cap),
                      (-cap, -cap, -axis), (-cap, cap, -axis),
                      (cap, -cap, -axis), (cap, cap, -axis), (-cap, cap, axis), (-cap, -cap, axis),
                      (cap, cap, axis), (cap, -cap, axis)]
        geom.edges = [(10, 4), (4, 5), (8, 9), (0, 2), (12, 8), (6, 7), (11, 10), (13, 12),
                      (5, 11), (9, 13), (3, 1), (14, 15), (16, 14), (17, 16), (15, 17), (18, 19),
                      (20, 18), (21, 20), (19, 21), (22, 23), (24, 22), (25, 24), (23, 25),
                      (26, 27), (28, 26), (29, 28), (27, 29)]
    else:
        geom.verts = [(0, 0, -axis), (-axis, 0, 0), (0, 0, axis), (axis, 0, 0), (-cap, 0, -axis),
                      (-axis, 0, -cap), (-axis, 0, cap), (-cap, 0, axis), (cap, 0, axis),
                      (axis, 0, cap), (axis, 0, -cap), (cap, 0, -axis), (0, -axis, 0),
                      (0, axis, 0), (0, -cap, -axis), (0, -axis, -cap), (0, -axis, cap),
                      (0, -cap, axis), (0, cap, axis), (0, axis, cap), (0, axis, -cap),
                      (0, cap, -axis), (-axis, -cap, 0), (-cap, -axis, 0), (cap, -axis, 0),
                      (axis, -cap, 0), (axis, cap, 0), (cap, axis, 0), (-cap, axis, 0),
                      (-axis, cap, 0)]
        geom.edges = [(4, 0), (6, 1), (8, 2), (10, 3), (1, 5), (2, 7), (3, 9), (0, 11), (16, 12),
                      (0, 21), (2, 17), (20, 13), (12, 15), (0, 2), (18, 2), (13, 19), (12, 13),
                      (1, 29), (22, 1), (3, 25), (13, 27), (14, 0), (26, 3), (28, 13), (24, 12),
                      (12, 23), (3, 1)]


register_widget("pivot_cross", create_pivot_widget, square=False)


@widget_generator(register="shoulder")
def create_shoulder_widget(geom, *, radius=0.5):
    r = radius * 2
    geom.verts = [(0, 0, 0), (0, 1, 0),
                  (0.41214 * r, 0.5 + (0.276111 - 0.5) * r, 0.282165 * r),
                  (0.469006 * r, 0.5 + (0.31436 - 0.5) * r, 0.168047 * r),
                  (0.492711 * r, 0.5 + (0.370708 - 0.5) * r, 0.0740018 * r),
                  (0.498419 * r, 0.5 + (0.440597 - 0.5) * r, 0.0160567 * r),
                  (0.5 * r, 0.5, 0), (0.498419 * r, 0.5 + (0.559402 - 0.5) * r, 0.0160563 * r),
                  (0.492712 * r, 0.5 + (0.629291 - 0.5) * r, 0.074001 * r),
                  (0.469006 * r, 0.5 + (0.68564 - 0.5) * r, 0.168046 * r),
                  (0.412141 * r, 0.5 + (0.723889 - 0.5) * r, 0.282164 * r),
                  (0.316952 * r, 0.5 + (0.742335 - 0.5) * r, 0.383591 * r),
                  (0.207152 * r, 0.5 + (0.74771 - 0.5) * r, 0.453489 * r),
                  (0.0999976 * r, 0.5 + (0.74949 - 0.5) * r, 0.489649 * r),
                  (0, 0.5 + (0.75 - 0.5) * r, 0.5 * r), (-0.099997 * r, 0.5 + (0.74949 - 0.5) * r, 0.489649 * r),
                  (-0.207152 * r, 0.5 + (0.74771 - 0.5) * r, 0.453489 * r),
                  (-0.316951 * r, 0.5 + (0.742335 - 0.5) * r, 0.383592 * r),
                  (-0.412141 * r, 0.5 + (0.723889 - 0.5) * r, 0.282165 * r),
                  (-0.469006 * r, 0.5 + (0.68564 - 0.5) * r, 0.168046 * r),
                  (-0.492711 * r, 0.5 + (0.629291 - 0.5) * r, 0.0740011 * r),
                  (-0.498419 * r, 0.5 + (0.559402 - 0.5) * r, 0.0160563 * r),
                  (-0.5 * r, 0.5, 0), (-0.498419 * r, 0.5 + (0.440598 - 0.5) * r, 0.0160563 * r),
                  (-0.492711 * r, 0.5 + (0.370709 - 0.5) * r, 0.0740012 * r),
                  (-0.469006 * r, 0.5 + (0.31436 - 0.5) * r, 0.168047 * r),
                  (-0.41214 * r, 0.5 + (0.276111 - 0.5) * r, 0.282165 * r),
                  (-0.316951 * r, 0.5 + (0.257665 - 0.5) * r, 0.383592 * r),
                  (-0.207151 * r, 0.5 + (0.25229 - 0.5) * r, 0.453489 * r),
                  (-0.0999959 * r, 0.5 + (0.25051 - 0.5) * r, 0.489649 * r),
                  (0, 0.5 + (0.25 - 0.5) * r, 0.5 * r), (0.0999986 * r, 0.5 + (0.25051 - 0.5) * r, 0.489648 * r),
                  (0.207153 * r, 0.5 + (0.25229 - 0.5) * r, 0.453488 * r),
                  (0.316953 * r, 0.5 + (0.257665 - 0.5) * r, 0.38359 * r),
                  ]
    geom.edges = [(0, 1), (2, 3), (4, 3), (5, 4), (5, 6), (6, 7), (8, 7), (8, 9), (10, 9), (10, 11),
                  (11, 12), (13, 12), (14, 13), (14, 15), (16, 15), (16, 17), (17, 18), (19, 18), (19, 20), (21, 20),
                  (21, 22), (22, 23), (24, 23), (25, 24), (25, 26), (27, 26), (27, 28), (29, 28), (29, 30), (30, 31),
                  (32, 31), (32, 33), (2, 33), ]
